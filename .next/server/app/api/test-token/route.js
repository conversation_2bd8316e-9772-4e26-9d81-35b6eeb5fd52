"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/test-token/route";
exports.ids = ["app/api/test-token/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-token%2Froute&page=%2Fapi%2Ftest-token%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-token%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-token%2Froute&page=%2Fapi%2Ftest-token%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-token%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_test_token_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/test-token/route.ts */ \"(rsc)/./src/app/api/test-token/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/test-token/route\",\n        pathname: \"/api/test-token\",\n        filename: \"route\",\n        bundlePath: \"app/api/test-token/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/test-token/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_test_token_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/test-token/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-token%2Froute&page=%2Fapi%2Ftest-token%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-token%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/test-token/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/test-token/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request) {\n    try {\n        console.log(\"\\uD83D\\uDD0D Testing token validity...\");\n        // Step 1: Get fresh token\n        const loginResponse = await fetch(`${API_BASE_URL}/system-auth/login`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                username: \"admin\",\n                password: \"admin123456\"\n            })\n        });\n        if (!loginResponse.ok) {\n            console.error(\"❌ Login failed:\", loginResponse.status);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Login failed\"\n            }, {\n                status: 500\n            });\n        }\n        const loginData = await loginResponse.json();\n        console.log(\"✅ Fresh token obtained\");\n        // Step 2: Test profile with fresh token\n        const profileResponse = await fetch(`${API_BASE_URL}/system-auth/profile`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": `Bearer ${loginData.accessToken}`\n            }\n        });\n        console.log(\"\\uD83D\\uDD0D Profile response status:\", profileResponse.status);\n        if (!profileResponse.ok) {\n            const errorText = await profileResponse.text();\n            console.error(\"❌ Profile failed with fresh token:\", errorText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Profile failed with fresh token\",\n                status: profileResponse.status,\n                details: errorText\n            }, {\n                status: 500\n            });\n        }\n        const profileData = await profileResponse.json();\n        console.log(\"✅ Profile success with fresh token\");\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            tokenLength: loginData.accessToken.length,\n            tokenPreview: loginData.accessToken.substring(0, 30) + \"...\",\n            profile: profileData\n        });\n    } catch (error) {\n        console.error(\"❌ Test Token Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/test-token/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftest-token%2Froute&page=%2Fapi%2Ftest-token%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-token%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();