"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/broadcast-links/route";
exports.ids = ["app/api/broadcast-links/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Froute&page=%2Fapi%2Fbroadcast-links%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Froute&page=%2Fapi%2Fbroadcast-links%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_broadcast_links_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/broadcast-links/route.ts */ \"(rsc)/./src/app/api/broadcast-links/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/broadcast-links/route\",\n        pathname: \"/api/broadcast-links\",\n        filename: \"route\",\n        bundlePath: \"app/api/broadcast-links/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/broadcast-links/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_broadcast_links_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/broadcast-links/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Froute&page=%2Fapi%2Fbroadcast-links%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/broadcast-links/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/broadcast-links/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Forward all query parameters\n        const params = new URLSearchParams();\n        searchParams.forEach((value, key)=>{\n            params.append(key, value);\n        });\n        console.log(\"\\uD83D\\uDD04 Proxying broadcast links request:\", `${API_BASE_URL}/broadcast-links?${params.toString()}`);\n        const response = await fetch(`${API_BASE_URL}/broadcast-links?${params.toString()}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch broadcast links\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ Broadcast links fetched successfully:\", data.meta);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        console.log(\"\\uD83D\\uDCDD Mock: Creating broadcast link:\", body);\n        // Mock successful creation\n        const mockData = {\n            data: {\n                id: Math.floor(Math.random() * 1000) + 1,\n                fixtureId: body.fixtureId,\n                linkName: body.linkName,\n                linkUrl: body.linkUrl,\n                linkComment: body.linkComment,\n                language: body.language || \"English\",\n                quality: body.quality || \"HD\",\n                addedBy: 1,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            },\n            status: 201\n        };\n        console.log(\"✅ Mock: Broadcast link created successfully:\", mockData.data.id);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(mockData, {\n            status: 201\n        });\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/broadcast-links/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Froute&page=%2Fapi%2Fbroadcast-links%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();