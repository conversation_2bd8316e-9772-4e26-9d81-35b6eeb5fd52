"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/broadcast-links/fixture/[fixtureId]/route";
exports.ids = ["app/api/broadcast-links/fixture/[fixtureId]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&page=%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&page=%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_broadcast_links_fixture_fixtureId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts */ \"(rsc)/./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/broadcast-links/fixture/[fixtureId]/route\",\n        pathname: \"/api/broadcast-links/fixture/[fixtureId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/broadcast-links/fixture/[fixtureId]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/broadcast-links/fixture/[fixtureId]/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_broadcast_links_fixture_fixtureId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/broadcast-links/fixture/[fixtureId]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZicm9hZGNhc3QtbGlua3MlMkZmaXh0dXJlJTJGJTVCZml4dHVyZUlkJTVEJTJGcm91dGUmcGFnZT0lMkZhcGklMkZicm9hZGNhc3QtbGlua3MlMkZmaXh0dXJlJTJGJTVCZml4dHVyZUlkJTVEJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGYnJvYWRjYXN0LWxpbmtzJTJGZml4dHVyZSUyRiU1QmZpeHR1cmVJZCU1RCUyRnJvdXRlLnRzJmFwcERpcj0lMkZob21lJTJGZHV5YW5oc3RhciUyRkZFQ01TLXNwb3J0JTJGc3JjJTJGYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj0lMkZob21lJTJGZHV5YW5oc3RhciUyRkZFQ01TLXNwb3J0JmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFzRztBQUN2QztBQUNjO0FBQ3FDO0FBQ2xIO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixnSEFBbUI7QUFDM0M7QUFDQSxjQUFjLHlFQUFTO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxZQUFZO0FBQ1osQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBLFFBQVEsdUdBQXVHO0FBQy9HO0FBQ0E7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDNko7O0FBRTdKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY21zLWFwaXNwb3J0c2dhbWUvP2IxYTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL2FwcC1yb3V0ZS9tb2R1bGUuY29tcGlsZWRcIjtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBwYXRjaEZldGNoIGFzIF9wYXRjaEZldGNoIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvbGliL3BhdGNoLWZldGNoXCI7XG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiL2hvbWUvZHV5YW5oc3Rhci9GRUNNUy1zcG9ydC9zcmMvYXBwL2FwaS9icm9hZGNhc3QtbGlua3MvZml4dHVyZS9bZml4dHVyZUlkXS9yb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvYnJvYWRjYXN0LWxpbmtzL2ZpeHR1cmUvW2ZpeHR1cmVJZF0vcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9icm9hZGNhc3QtbGlua3MvZml4dHVyZS9bZml4dHVyZUlkXVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvYnJvYWRjYXN0LWxpbmtzL2ZpeHR1cmUvW2ZpeHR1cmVJZF0vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCIvaG9tZS9kdXlhbmhzdGFyL0ZFQ01TLXNwb3J0L3NyYy9hcHAvYXBpL2Jyb2FkY2FzdC1saW5rcy9maXh0dXJlL1tmaXh0dXJlSWRdL3JvdXRlLnRzXCIsXG4gICAgbmV4dENvbmZpZ091dHB1dCxcbiAgICB1c2VybGFuZFxufSk7XG4vLyBQdWxsIG91dCB0aGUgZXhwb3J0cyB0aGF0IHdlIG5lZWQgdG8gZXhwb3NlIGZyb20gdGhlIG1vZHVsZS4gVGhpcyBzaG91bGRcbi8vIGJlIGVsaW1pbmF0ZWQgd2hlbiB3ZSd2ZSBtb3ZlZCB0aGUgb3RoZXIgcm91dGVzIHRvIHRoZSBuZXcgZm9ybWF0LiBUaGVzZVxuLy8gYXJlIHVzZWQgdG8gaG9vayBpbnRvIHRoZSByb3V0ZS5cbmNvbnN0IHsgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCB9ID0gcm91dGVNb2R1bGU7XG5jb25zdCBvcmlnaW5hbFBhdGhuYW1lID0gXCIvYXBpL2Jyb2FkY2FzdC1saW5rcy9maXh0dXJlL1tmaXh0dXJlSWRdL3JvdXRlXCI7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHNlcnZlckhvb2tzLFxuICAgICAgICBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIGhlYWRlckhvb2tzLCBzdGF0aWNHZW5lcmF0aW9uQmFpbG91dCwgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&page=%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts":
/*!******************************************************************!*\
  !*** ./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mock_broadcast_links_storage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mock/broadcast-links-storage */ \"(rsc)/./src/lib/mock/broadcast-links-storage.ts\");\n\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const fixtureId = params.fixtureId;\n        console.log(\"\\uD83D\\uDD04 Proxying broadcast links for fixture request:\", `${API_BASE_URL}/football/broadcast-links/fixture/${fixtureId}`);\n        // Get data from mock storage\n        const fixtureIdNum = parseInt(fixtureId);\n        const links = _lib_mock_broadcast_links_storage__WEBPACK_IMPORTED_MODULE_1__.mockBroadcastLinksStorage.getByFixture(fixtureIdNum);\n        console.log(\"\\uD83D\\uDCDD Mock: Returning broadcast links for fixture:\", fixtureId, \"- Found:\", links.length, \"links\");\n        const mockData = {\n            data: links,\n            status: 200\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(mockData);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mock/broadcast-links-storage.ts":
/*!*************************************************!*\
  !*** ./src/lib/mock/broadcast-links-storage.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mockBroadcastLinksStorage: () => (/* binding */ mockBroadcastLinksStorage)\n/* harmony export */ });\n// In-memory storage for mock broadcast links\n// This will be replaced with real database when API is implemented\n// In-memory storage with some initial data for testing\nlet broadcastLinksStorage = [\n    {\n        id: 1,\n        fixtureId: 1274453,\n        linkName: \"ESPN HD Stream\",\n        linkUrl: \"https://espn.com/live\",\n        linkComment: \"Official HD stream with English commentary\",\n        language: \"English\",\n        quality: \"HD\",\n        addedBy: 1,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    },\n    {\n        id: 2,\n        fixtureId: 1274453,\n        linkName: \"Sky Sports 4K\",\n        linkUrl: \"https://skysports.com/live\",\n        linkComment: \"4K Ultra HD stream with multiple language options\",\n        language: \"English\",\n        quality: \"4K\",\n        addedBy: 1,\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n    }\n];\nlet nextId = 3;\nconst mockBroadcastLinksStorage = {\n    // Get all links for a fixture\n    getByFixture: (fixtureId)=>{\n        return broadcastLinksStorage.filter((link)=>link.fixtureId === fixtureId);\n    },\n    // Create new link\n    create: (data)=>{\n        const newLink = {\n            ...data,\n            id: nextId++,\n            addedBy: 1,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        broadcastLinksStorage.push(newLink);\n        return newLink;\n    },\n    // Update link\n    update: (id, data)=>{\n        const index = broadcastLinksStorage.findIndex((link)=>link.id === id);\n        if (index === -1) return null;\n        broadcastLinksStorage[index] = {\n            ...broadcastLinksStorage[index],\n            ...data,\n            updatedAt: new Date().toISOString()\n        };\n        return broadcastLinksStorage[index];\n    },\n    // Delete link\n    delete: (id)=>{\n        const index = broadcastLinksStorage.findIndex((link)=>link.id === id);\n        if (index === -1) return false;\n        broadcastLinksStorage.splice(index, 1);\n        return true;\n    },\n    // Get by ID\n    getById: (id)=>{\n        return broadcastLinksStorage.find((link)=>link.id === id) || null;\n    },\n    // Clear all (for testing)\n    clear: ()=>{\n        broadcastLinksStorage = [];\n        nextId = 1;\n    },\n    // Get all (for debugging)\n    getAll: ()=>{\n        return [\n            ...broadcastLinksStorage\n        ];\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mock/broadcast-links-storage.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&page=%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();