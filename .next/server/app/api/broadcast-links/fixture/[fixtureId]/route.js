"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/broadcast-links/fixture/[fixtureId]/route";
exports.ids = ["app/api/broadcast-links/fixture/[fixtureId]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&page=%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&page=%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_broadcast_links_fixture_fixtureId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts */ \"(rsc)/./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/broadcast-links/fixture/[fixtureId]/route\",\n        pathname: \"/api/broadcast-links/fixture/[fixtureId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/broadcast-links/fixture/[fixtureId]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/broadcast-links/fixture/[fixtureId]/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_broadcast_links_fixture_fixtureId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/broadcast-links/fixture/[fixtureId]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&page=%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts":
/*!******************************************************************!*\
  !*** ./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const fixtureId = params.fixtureId;\n        // Call real API endpoint\n        console.log(\"\\uD83D\\uDD04 Proxying broadcast links for fixture request:\", `${API_BASE_URL}/broadcast-links/fixture/${fixtureId}`);\n        const authHeader = request.headers.get(\"authorization\");\n        console.log(\"\\uD83D\\uDD11 Authorization header received:\", authHeader ? \"Present\" : \"Missing\");\n        console.log(\"\\uD83D\\uDD11 Token preview:\", authHeader ? authHeader.substring(0, 30) + \"...\" : \"No token\");\n        try {\n            const response = await fetch(`${API_BASE_URL}/broadcast-links/fixture/${fixtureId}`, {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    ...authHeader && {\n                        \"Authorization\": authHeader\n                    }\n                }\n            });\n            if (!response.ok) {\n                console.error(\"❌ API Error:\", response.status, response.statusText);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Failed to fetch broadcast links for fixture\",\n                    status: response.status,\n                    message: response.statusText\n                }, {\n                    status: response.status\n                });\n            }\n            const data = await response.json();\n            console.log(\"✅ Broadcast links for fixture fetched successfully:\", data.data?.length || 0, \"links\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        } catch (networkError) {\n            console.error(\"❌ Network Error:\", networkError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Network error occurred\",\n                message: networkError.message\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/broadcast-links/fixture/[fixtureId]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&page=%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbroadcast-links%2Ffixture%2F%5BfixtureId%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();