(()=>{var e={};e.id=165,e.ids=[165],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3961:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>l});var o=t(482),s=t(9108),n=t(2563),a=t.n(n),i=t(8300),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,48)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"]}],c=[],u="/_not-found",m={require:t,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},7342:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,6840,23)),Promise.resolve().then(t.t.bind(t,8771,23)),Promise.resolve().then(t.t.bind(t,3225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,3982,23))},1473:(e,r,t)=>{Promise.resolve().then(t.bind(t,6865)),Promise.resolve().then(t.bind(t,9284))},6865:(e,r,t)=>{"use strict";t.r(r),t.d(r,{DefaultErrorFallback:()=>b,ErrorBoundary:()=>v,useErrorHandler:()=>y});var o=t(5344),s=t(3729),n=t.n(s),a=t(5877),i=t(2193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let d=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=s.forwardRef(({className:e,variant:r,size:t,asChild:s=!1,...n},i)=>{let l=s?a.g7:"button";return o.jsx(l,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(d({variant:r,size:t,className:e})),ref:i,...n})});l.displayName="Button",function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let c=s.forwardRef(({className:e,...r},t)=>o.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-xl border bg-card text-card-foreground shadow",e),...r}));c.displayName="Card";let u=s.forwardRef(({className:e,...r},t)=>o.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...r}));u.displayName="CardHeader";let m=s.forwardRef(({className:e,...r},t)=>o.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("font-semibold leading-none tracking-tight",e),...r}));m.displayName="CardTitle";let h=s.forwardRef(({className:e,...r},t)=>o.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...r}));h.displayName="CardDescription";let f=s.forwardRef(({className:e,...r},t)=>o.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...r}));f.displayName="CardContent",s.forwardRef(({className:e,...r},t)=>o.jsx("div",{ref:t,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter";var p=t(5719),x=t(3733);class v extends n().Component{constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,r){console.error("ErrorBoundary caught an error:",e,r)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return o.jsx(e,{error:this.state.error,resetError:this.resetError})}return o.jsx(b,{error:this.state.error,resetError:this.resetError})}return this.props.children}}let b=({error:e,resetError:r})=>o.jsx("div",{className:"flex items-center justify-center min-h-[400px] p-4",children:(0,o.jsxs)(c,{className:"w-full max-w-md",children:[(0,o.jsxs)(u,{className:"text-center",children:[o.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:o.jsx(p.Z,{className:"h-6 w-6 text-red-600"})}),o.jsx(m,{className:"text-red-900",children:"Something went wrong"}),o.jsx(h,{children:"An unexpected error occurred. Please try refreshing the page."})]}),(0,o.jsxs)(f,{className:"space-y-4",children:[!1,(0,o.jsxs)(l,{onClick:r,className:"w-full",variant:"outline",children:[o.jsx(x.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]})}),y=()=>{let[e,r]=n().useState(null),t=n().useCallback(()=>{r(null)},[]),o=n().useCallback(e=>{r(e)},[]);return n().useEffect(()=>{if(e)throw e},[e]),{captureError:o,resetError:t}}},9284:(e,r,t)=>{"use strict";t.r(r),t.d(r,{QueryProvider:()=>i});var o=t(5344),s=t(8814),n=t(1494),a=t(3729);let i=({children:e})=>{let[r]=(0,a.useState)(()=>new s.S({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1,retryDelay:1e3}}}));return(0,o.jsxs)(n.aH,{client:r,children:[e,!1]})}},48:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x,metadata:()=>p});var o=t(5036),s=t(2195),n=t.n(s);t(5023);var a=t(6843);let i=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx`),{__esModule:d,$$typeof:l}=i;i.default;let c=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx#QueryProvider`),u=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx`),{__esModule:m,$$typeof:h}=u;u.default;let f=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#ErrorBoundary`);(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#DefaultErrorFallback`),(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#useErrorHandler`);let p={title:"APISportsGame CMS",description:"Content Management System for APISportsGame API"};function x({children:e}){return o.jsx("html",{lang:"en",children:o.jsx("body",{className:n().className,children:o.jsx(f,{children:o.jsx(c,{children:e})})})})}},5023:()=>{}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[638,783],()=>t(3961));module.exports=o})();