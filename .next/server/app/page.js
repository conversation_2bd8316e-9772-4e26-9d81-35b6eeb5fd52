(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},8527:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>f,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>u});var n=r(482),i=r(9108),o=r(2563),a=r.n(o),s=r(8300),l={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8075)),"/home/<USER>/FECMS-sport/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,48)),"/home/<USER>/FECMS-sport/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/home/<USER>/FECMS-sport/src/app/page.tsx"],d="/page",f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},7342:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},1485:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,1900,23))},1473:(e,t,r)=>{Promise.resolve().then(r.bind(r,6865)),Promise.resolve().then(r.bind(r,9284))},1900:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return x}});let n=r(9694),i=r(7824),o=r(5344),a=i._(r(3729)),s=n._(r(1202)),l=n._(r(1758)),u=r(3855),c=r(3053),d=r(4187);r(837);let f=r(6150),p=n._(r(4931)),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function h(e,t,r,n,i,o){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&i(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let n=!1,i=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>n,isPropagationStopped:()=>i,persist:()=>{},preventDefault:()=>{n=!0,t.preventDefault()},stopPropagation:()=>{i=!0,t.stopPropagation()}})}(null==n?void 0:n.current)&&n.current(e)}}))}function g(e){let[t,r]=a.version.split(".",2),n=parseInt(t,10),i=parseInt(r,10);return n>18||18===n&&i>=3?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let y=(0,a.forwardRef)((e,t)=>{let{src:r,srcSet:n,sizes:i,height:s,width:l,decoding:u,className:c,style:d,fetchPriority:f,placeholder:p,loading:m,unoptimized:y,fill:b,onLoadRef:x,onLoadingCompleteRef:v,setBlurComplete:_,setShowAltText:E,onLoad:w,onError:j,...P}=e;return(0,o.jsx)("img",{...P,...g(f),loading:m,width:l,height:s,decoding:u,"data-nimg":b?"fill":"1",className:c,style:d,sizes:i,srcSet:n,src:r,ref:(0,a.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(j&&(e.src=e.src),e.complete&&h(e,p,x,v,_,y))},[r,p,x,v,_,j,y,t]),onLoad:e=>{h(e.currentTarget,p,x,v,_,y)},onError:e=>{E(!0),"empty"!==p&&_(!0),j&&j(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,n={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...g(r.fetchPriority)};return t&&s.default.preload?(s.default.preload(r.src,n),null):(0,o.jsx)(l.default,{children:(0,o.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...n},"__nimg-"+r.src+r.srcSet+r.sizes)})}let x=(0,a.forwardRef)((e,t)=>{let r=(0,a.useContext)(f.RouterContext),n=(0,a.useContext)(d.ImageConfigContext),i=(0,a.useMemo)(()=>{let e=m||n||c.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),r=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:r}},[n]),{onLoad:s,onLoadingComplete:l}=e,h=(0,a.useRef)(s);(0,a.useEffect)(()=>{h.current=s},[s]);let g=(0,a.useRef)(l);(0,a.useEffect)(()=>{g.current=l},[l]);let[x,v]=(0,a.useState)(!1),[_,E]=(0,a.useState)(!1),{props:w,meta:j}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:i,blurComplete:x,showAltText:_});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(y,{...w,unoptimized:j.unoptimized,placeholder:j.placeholder,fill:j.fill,onLoadRef:h,onLoadingCompleteRef:g,setBlurComplete:v,setShowAltText:E,ref:t}),j.priority?(0,o.jsx)(b,{isAppRouter:!r,imgAttributes:w}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7637:(e,t,r)=>{"use strict";e.exports=r(6372).vendored.contexts.AmpContext},2158:(e,t,r)=>{"use strict";e.exports=r(6372).vendored.contexts.HeadManagerContext},4187:(e,t,r)=>{"use strict";e.exports=r(6372).vendored.contexts.ImageConfigContext},6150:(e,t,r)=>{"use strict";e.exports=r(6372).vendored.contexts.RouterContext},3126:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},3855:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),r(837);let n=r(3990),i=r(3053);function o(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r;let s,l,u,{src:c,sizes:d,unoptimized:f=!1,priority:p=!1,loading:m,className:h,quality:g,width:y,height:b,fill:x=!1,style:v,onLoad:_,onLoadingComplete:E,placeholder:w="empty",blurDataURL:j,fetchPriority:P,layout:R,objectFit:O,objectPosition:S,lazyBoundary:C,lazyRoot:N,...A}=e,{imgConf:M,showAltText:k,blurComplete:T,defaultLoader:I}=t,D=M||i.imageConfigDefault;if("allSizes"in D)s=D;else{let e=[...D.deviceSizes,...D.imageSizes].sort((e,t)=>e-t),t=D.deviceSizes.sort((e,t)=>e-t);s={...D,allSizes:e,deviceSizes:t}}let U=A.loader||I;delete A.loader,delete A.srcSet;let z="__next_img_default"in U;if(z){if("custom"===s.loader)throw Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=U;U=t=>{let{config:r,...n}=t;return e(n)}}if(R){"fill"===R&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(v={...v,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!d&&(d=t)}let L="",$=a(y),F=a(b);if("object"==typeof(r=c)&&(o(r)||void 0!==r.src)){let e=o(c)?c.default:c;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(l=e.blurWidth,u=e.blurHeight,j=j||e.blurDataURL,L=e.src,!x){if($||F){if($&&!F){let t=$/e.width;F=Math.round(e.height*t)}else if(!$&&F){let t=F/e.height;$=Math.round(e.width*t)}}else $=e.width,F=e.height}}let q=!p&&("lazy"===m||void 0===m);(!(c="string"==typeof c?c:L)||c.startsWith("data:")||c.startsWith("blob:"))&&(f=!0,q=!1),s.unoptimized&&(f=!0),z&&c.endsWith(".svg")&&!s.dangerouslyAllowSVG&&(f=!0),p&&(P="high");let G=a(g),W=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:O,objectPosition:S}:{},k?{}:{color:"transparent"},v),H=T||"empty"===w?null:"blur"===w?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:$,heightInt:F,blurWidth:l,blurHeight:u,blurDataURL:j||"",objectFit:W.objectFit})+'")':'url("'+w+'")',B=H?{backgroundSize:W.objectFit||"cover",backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:H}:{},X=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:o,sizes:a,loader:s}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);n)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,a),c=l.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:l.map((e,n)=>s({config:t,src:r,quality:o,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:s({config:t,src:r,quality:o,width:l[c]})}}({config:s,src:c,unoptimized:f,width:$,quality:G,sizes:d,loader:U});return{props:{...A,loading:q?"lazy":m,fetchPriority:P,width:$,height:F,decoding:"async",className:h,style:{...W,...B},sizes:X.sizes,srcSet:X.srcSet,src:X.src},meta:{unoptimized:f,priority:p,placeholder:w,fill:x}}}},1758:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{defaultHead:function(){return d},default:function(){return h}});let n=r(9694),i=r(7824),o=r(5344),a=i._(r(3729)),s=n._(r(7984)),l=r(7637),u=r(2158),c=r(3126);function d(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===a.default.Fragment?e.concat(a.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(837);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(d(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return i=>{let o=!0,a=!1;if(i.key&&"number"!=typeof i.key&&i.key.indexOf("$")>0){a=!0;let t=i.key.slice(i.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(i.type){case"title":case"base":t.has(i.type)?o=!1:t.add(i.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(i.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?o=!1:r.add(t);else{let e=i.props[t],r=n[t]||new Set;("name"!==t||!a)&&r.has(e)?o=!1:(r.add(e),n[t]=r)}}}}return o}}()).reverse().map((e,t)=>{let n=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,a.default.cloneElement(e,t)}return a.default.cloneElement(e,{key:n})})}let h=function(e){let{children:t}=e,r=(0,a.useContext)(l.AmpStateContext),n=(0,a.useContext)(u.HeadManagerContext);return(0,o.jsx)(s.default,{reduceComponentsToState:m,headManager:n,inAmpMode:(0,c.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3990:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:o,objectFit:a}=e,s=n?40*n:t,l=i?40*i:r,u=s&&l?"viewBox='0 0 "+s+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},3053:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},4931:(e,t)=>{"use strict";function r(e){let{config:t,src:r,width:n,quality:i}=e;return t.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+(i||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},7984:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let n=r(3729),i=()=>{},o=()=>{};function a(e){var t;let{headManager:r,reduceComponentsToState:a}=e;function s(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(a(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),s(),i(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),i(()=>(r&&(r._pendingUpdate=s),()=>{r&&(r._pendingUpdate=s)})),o(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},6865:(e,t,r)=>{"use strict";r.r(t),r.d(t,{DefaultErrorFallback:()=>b,ErrorBoundary:()=>y,useErrorHandler:()=>x});var n=r(5344),i=r(3729),o=r.n(i),a=r(5877),s=r(2193);!function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let l=(0,s.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=i.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,...o},s)=>{let u=i?a.g7:"button";return n.jsx(u,{className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l({variant:t,size:r,className:e})),ref:s,...o})});u.displayName="Button",function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let c=i.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("rounded-xl border bg-card text-card-foreground shadow",e),...t}));c.displayName="Card";let d=i.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex flex-col space-y-1.5 p-6",e),...t}));d.displayName="CardHeader";let f=i.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("font-semibold leading-none tracking-tight",e),...t}));f.displayName="CardTitle";let p=i.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("text-sm text-muted-foreground",e),...t}));p.displayName="CardDescription";let m=i.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("p-6 pt-0",e),...t}));m.displayName="CardContent",i.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";var h=r(5719),g=r(3733);class y extends o().Component{constructor(e){super(e),this.resetError=()=>{this.setState({hasError:!1,error:void 0})},this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t)}render(){if(this.state.hasError){if(this.props.fallback){let e=this.props.fallback;return n.jsx(e,{error:this.state.error,resetError:this.resetError})}return n.jsx(b,{error:this.state.error,resetError:this.resetError})}return this.props.children}}let b=({error:e,resetError:t})=>n.jsx("div",{className:"flex items-center justify-center min-h-[400px] p-4",children:(0,n.jsxs)(c,{className:"w-full max-w-md",children:[(0,n.jsxs)(d,{className:"text-center",children:[n.jsx("div",{className:"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100",children:n.jsx(h.Z,{className:"h-6 w-6 text-red-600"})}),n.jsx(f,{className:"text-red-900",children:"Something went wrong"}),n.jsx(p,{children:"An unexpected error occurred. Please try refreshing the page."})]}),(0,n.jsxs)(m,{className:"space-y-4",children:[!1,(0,n.jsxs)(u,{onClick:t,className:"w-full",variant:"outline",children:[n.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Try Again"]})]})]})}),x=()=>{let[e,t]=o().useState(null),r=o().useCallback(()=>{t(null)},[]),n=o().useCallback(e=>{t(e)},[]);return o().useEffect(()=>{if(e)throw e},[e]),{captureError:n,resetError:r}}},9284:(e,t,r)=>{"use strict";r.r(t),r.d(t,{QueryProvider:()=>s});var n=r(5344),i=r(8814),o=r(1494),a=r(3729);let s=({children:e})=>{let[t]=(0,a.useState)(()=>new i.S({defaultOptions:{queries:{staleTime:3e5,cacheTime:6e5,retry:3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:!0},mutations:{retry:1,retryDelay:1e3}}}));return(0,n.jsxs)(o.aH,{client:t,children:[e,!1]})}},4409:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RSC_HEADER:function(){return r},ACTION:function(){return n},NEXT_ROUTER_STATE_TREE:function(){return i},NEXT_ROUTER_PREFETCH_HEADER:function(){return o},NEXT_URL:function(){return a},RSC_CONTENT_TYPE_HEADER:function(){return s},RSC_VARY_HEADER:function(){return l},FLIGHT_PARAMETERS:function(){return u},NEXT_RSC_UNION_QUERY:function(){return c},NEXT_DID_POSTPONE_HEADER:function(){return d}});let r="RSC",n="Next-Action",i="Next-Router-State-Tree",o="Next-Router-Prefetch",a="Next-Url",s="text/x-component",l=r+", "+i+", "+o+", "+a,u=[[r],[i],[o]],c="_rsc",d="x-nextjs-postponed";("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6987:(e,t,r)=>{"use strict";let{createProxy:n}=r(6843);e.exports=n("/home/<USER>/FECMS-sport/node_modules/next/dist/client/image-component.js")},7363:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{/*!
 * cookie
 * Copyright(c) 2012-2014 Roman Shtylman
 * Copyright(c) 2015 Douglas Christopher Wilson
 * MIT Licensed
 */t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},o=t.split(n),a=(r||{}).decode||e,s=0;s<o.length;s++){var l=o[s],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[c]&&(i[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var o=n||{},a=o.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var s=a(t);if(s&&!i.test(s))throw TypeError("argument val is invalid");var l=e+"="+s;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(o.domain){if(!i.test(o.domain))throw TypeError("option domain is invalid");l+="; Domain="+o.domain}if(o.path){if(!i.test(o.path))throw TypeError("option path is invalid");l+="; Path="+o.path}if(o.expires){if("function"!=typeof o.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+o.expires.toUTCString()}if(o.httpOnly&&(l+="; HttpOnly"),o.secure&&(l+="; Secure"),o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6358:(e,t)=>{"use strict";function r(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",o=r+1;o<e.length;){var a=e.charCodeAt(o);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[o++];continue}break}if(!i)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:i}),r=o;continue}if("("===n){var s=1,l="",o=r+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '+o);for(;o<e.length;){if("\\"===e[o]){l+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--s){o++;break}}else if("("===e[o]&&(s++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at "+o);l+=e[o++]}if(s)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=o;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,a="[^"+o(t.delimiter||"/#?")+"]+?",s=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},f=function(e){var t=d(e);if(void 0!==t)return t;var n=r[u];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var m=d("CHAR"),h=d("NAME"),g=d("PATTERN");if(h||g){var y=m||"";-1===i.indexOf(y)&&(c+=y,y=""),c&&(s.push(c),c=""),s.push({name:h||l++,prefix:y,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var b=m||d("ESCAPED_CHAR");if(b){c+=b;continue}if(c&&(s.push(c),c=""),d("OPEN")){var y=p(),x=d("NAME")||"",v=d("PATTERN")||"",_=p();f("CLOSE"),s.push({name:x||(v?l++:""),pattern:x&&!v?a:v,prefix:y,suffix:_,modifier:d("MODIFIER")||""});continue}f("END")}return s}function n(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,i=void 0===n?function(e){return e}:n,o=t.validate,s=void 0===o||o,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var o=e[n];if("string"==typeof o){r+=o;continue}var a=t?t[o.name]:void 0,u="?"===o.modifier||"*"===o.modifier,c="*"===o.modifier||"+"===o.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+o.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+o.name+'" to not be empty')}for(var d=0;d<a.length;d++){var f=i(a[d],o);if(s&&!l[n].test(f))throw TypeError('Expected all "'+o.name+'" to match "'+o.pattern+'", but got "'+f+'"');r+=o.prefix+f+o.suffix}continue}if("string"==typeof a||"number"==typeof a){var f=i(String(a),o);if(s&&!l[n].test(f))throw TypeError('Expected "'+o.name+'" to match "'+o.pattern+'", but got "'+f+'"');r+=o.prefix+f+o.suffix;continue}if(!u){var p=c?"an array":"a string";throw TypeError('Expected "'+o.name+'" to be '+p)}}return r}}function i(e,t,r){void 0===r&&(r={});var n=r.decode,i=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var o=n[0],a=n.index,s=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?s[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return i(e,r)}):s[r.name]=i(n[e],r)}}(l);return{path:o,index:a,params:s}}}function o(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function s(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,s=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+o(r.endsWith||"")+"]|$",f="["+o(r.delimiter||"/#?")+"]",p=void 0===s||s?"^":"",m=0;m<e.length;m++){var h=e[m];if("string"==typeof h)p+=o(c(h));else{var g=o(c(h.prefix)),y=o(c(h.suffix));if(h.pattern){if(t&&t.push(h),g||y){if("+"===h.modifier||"*"===h.modifier){var b="*"===h.modifier?"?":"";p+="(?:"+g+"((?:"+h.pattern+")(?:"+y+g+"(?:"+h.pattern+"))*)"+y+")"+b}else p+="(?:"+g+"("+h.pattern+")"+y+")"+h.modifier}else p+="("+h.pattern+")"+h.modifier}else p+="(?:"+g+y+")"+h.modifier}}if(void 0===l||l)i||(p+=f+"?"),p+=r.endsWith?"(?="+d+")":"$";else{var x=e[e.length-1],v="string"==typeof x?f.indexOf(x[x.length-1])>-1:void 0===x;i||(p+="(?:"+f+"(?="+d+"))?"),v||(p+="(?="+f+"|"+d+")")}return new RegExp(p,a(r))}function l(e,t,n){return e instanceof RegExp?function(e,t){if(!t)return e;var r=e.source.match(/\((?!\?)/g);if(r)for(var n=0;n<r.length;n++)t.push({name:n,prefix:"",suffix:"",modifier:"",pattern:""});return e}(e,t):Array.isArray(e)?RegExp("(?:"+e.map(function(e){return l(e,t,n).source}).join("|")+")",a(n)):s(r(e,n),t,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=r,t.compile=function(e,t){return n(r(e,t),t)},t.tokensToFunction=n,t.match=function(e,t){var r=[];return i(l(e,r,t),r,t)},t.regexpToFunction=i,t.tokensToRegexp=s,t.pathToRegexp=l},337:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return d},normalizeMetadataRoute:function(){return f}});let n=r(8488),i=function(e){return e&&e.__esModule?e:{default:e}}(r(4084)),o=r(9017),a=r(4654),s=r(6325),l=r(9393),u=r(9716);function c(e){let t="";return(e.includes("(")&&e.includes(")")||e.includes("@"))&&(t=(0,s.djb2Hash)(e).toString(36).slice(0,6)),t}function d(e,t,r){let n=(0,l.normalizeAppPath)(e),s=(0,a.getNamedRouteRegex)(n,!1),d=(0,o.interpolateDynamicPath)(n,t,s),f=c(e),p=f?`-${f}`:"",{name:m,ext:h}=i.default.parse(r);return(0,u.normalizePathSep)(i.default.join(d,`${m}${p}${h}`))}function f(e){if(!(0,n.isMetadataRoute)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":e.endsWith("/sitemap")?t+=".xml":r=c(e.slice(0,-(i.default.basename(e).length+1))),!t.endsWith("/route")){let{dir:o,name:a,ext:s}=i.default.parse(t),l=(0,n.isStaticMetadataRoute)(e);t=i.default.posix.join(o,`${a}${r?`-${r}`:""}${s}`,l?"":"[[...__metadata_id__]]","route")}return t}},8488:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{STATIC_METADATA_IMAGES:function(){return i},isMetadataRouteFile:function(){return s},isStaticMetadataRouteFile:function(){return l},isStaticMetadataRoute:function(){return u},isMetadataRoute:function(){return c}});let n=r(9716),i={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],a=e=>`(?:${e.join("|")})`;function s(e,t,r){let o=[RegExp(`^[\\\\/]robots${r?`\\.${a(t.concat("txt"))}$`:""}`),RegExp(`^[\\\\/]manifest${r?`\\.${a(t.concat("webmanifest","json"))}$`:""}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${r?`\\.${a(t.concat("xml"))}$`:""}`),RegExp(`[\\\\/]${i.icon.filename}\\d?${r?`\\.${a(t.concat(i.icon.extensions))}$`:""}`),RegExp(`[\\\\/]${i.apple.filename}\\d?${r?`\\.${a(t.concat(i.apple.extensions))}$`:""}`),RegExp(`[\\\\/]${i.openGraph.filename}\\d?${r?`\\.${a(t.concat(i.openGraph.extensions))}$`:""}`),RegExp(`[\\\\/]${i.twitter.filename}\\d?${r?`\\.${a(t.concat(i.twitter.extensions))}$`:""}`)],s=(0,n.normalizePathSep)(e);return o.some(e=>e.test(s))}function l(e){return s(e,[],!0)}function u(e){return"/robots"===e||"/manifest"===e||l(e)}function c(e){let t=e.replace(/^\/?app\//,"").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),!t.endsWith("/page")&&s(t,o,!1)}},892:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(7363);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8988:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return i},isInterceptionRouteAppPath:function(){return o},extractInterceptionRouteInformation:function(){return a}});let n=r(9393),i=["(..)(..)","(.)","(..)","(...)"];function o(e){return void 0!==e.split("/").find(e=>i.find(t=>e.startsWith(t)))}function a(e){let t,r,o;for(let n of e.split("/"))if(r=i.find(e=>n.startsWith(e))){[t,o]=e.split(r,2);break}if(!t||!r||!o)throw Error(`Invalid interception route: ${e}. Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>`);switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":o="/"===t?`/${o}`:t+"/"+o;break;case"(..)":if("/"===t)throw Error(`Invalid interception route: ${e}. Cannot use (..) marker at the root level, use (.) instead.`);o=t.split("/").slice(0,-1).concat(o).join("/");break;case"(...)":o="/"+o;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Error(`Invalid interception route: ${e}. Cannot use (..)(..) marker at the root level or one level up.`);o=a.slice(0,-2).concat(o).join("/");break;default:throw Error("Invariant: unexpected marker")}return{interceptingRoute:t,interceptedRoute:o}}},9017:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeVercelUrl:function(){return f},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return m},getUtils:function(){return h}});let n=r(7310),i=r(3935),o=r(8237),a=r(4654),s=r(7158),l=r(2762),u=r(5545),c=r(9393),d=r(2740);function f(e,t,r,i,o){if(i&&t&&o){let t=(0,n.parse)(e.url,!0);for(let e of(delete t.search,Object.keys(t.query)))(e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX)||(r||Object.keys(o.groups)).includes(e))&&delete t.query[e];e.url=(0,n.format)(t)}}function p(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let{optional:i,repeat:o}=r.groups[n],a=`[${o?"...":""}${n}]`;i&&(a=`[${a}]`);let s=e.indexOf(a);if(s>-1){let r;let i=t[n];r=Array.isArray(i)?i.map(e=>e&&encodeURIComponent(e)).join("/"):i?encodeURIComponent(i):"",e=e.slice(0,s)+r+e.slice(s+a.length)}}return e}function m(e,t,r,n){let i=!0;return r?{params:e=Object.keys(r.groups).reduce((o,a)=>{let s=e[a];"string"==typeof s&&(s=(0,c.normalizeRscURL)(s)),Array.isArray(s)&&(s=s.map(e=>("string"==typeof e&&(e=(0,c.normalizeRscURL)(e)),e)));let l=n[a],u=r.groups[a].optional;return((Array.isArray(l)?l.some(e=>Array.isArray(s)?s.some(t=>t.includes(e)):null==s?void 0:s.includes(e)):null==s?void 0:s.includes(l))||void 0===s&&!(u&&t))&&(i=!1),u&&(!s||Array.isArray(s)&&1===s.length&&("index"===s[0]||s[0]===`[[...${a}]]`))&&(s=void 0,delete e[a]),s&&"string"==typeof s&&r.groups[a].repeat&&(s=s.split("/")),s&&(o[a]=s),o},{}),hasValidParams:i}:{params:e,hasValidParams:!1}}function h({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:c,trailingSlash:h,caseSensitive:g}){let y,b,x;return c&&(y=(0,a.getNamedRouteRegex)(e,!1),x=(b=(0,s.getRouteMatcher)(y))(e)),{handleRewrites:function(a,s){let d={},f=s.pathname,p=n=>{let u=(0,o.getPathMatch)(n.source+(h?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!g})(s.pathname);if((n.has||n.missing)&&u){let e=(0,l.matchHas)(a,s.query,n.has,n.missing);e?Object.assign(u,e):u=!1}if(u){let{parsedDestination:o,destQuery:a}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:u,query:s.query});if(o.protocol)return!0;if(Object.assign(d,a,u),Object.assign(s.query,o.query),delete o.query,Object.assign(s,o),f=s.pathname,r&&(f=f.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,i.normalizeLocalePath)(f,t.locales);f=e.pathname,s.query.nextInternalLocale=e.detectedLocale||u.nextInternalLocale}if(f===e)return!0;if(c&&b){let e=b(f);if(e)return s.query={...s.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(f!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,u.removeTrailingSlash)(f||"");return t===(0,u.removeTrailingSlash)(e)||(null==b?void 0:b(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return d},defaultRouteRegex:y,dynamicRouteMatcher:b,defaultRouteMatches:x,getParamsFromRouteMatches:function(e,r,n){return(0,s.getRouteMatcher)(function(){let{groups:e,routeKeys:i}=y;return{re:{exec:o=>{let a=Object.fromEntries(new URLSearchParams(o)),s=t&&n&&a["1"]===n;for(let e of Object.keys(a)){let t=a[e];e!==d.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(d.NEXT_QUERY_PARAM_PREFIX)&&(a[e.substring(d.NEXT_QUERY_PARAM_PREFIX.length)]=t,delete a[e])}let l=Object.keys(i||{}),u=e=>{if(t){let i=Array.isArray(e),o=i?e[0]:e;if("string"==typeof o&&t.locales.some(e=>e.toLowerCase()===o.toLowerCase()&&(n=e,r.locale=n,!0)))return i&&e.splice(0,1),!i||0===e.length}return!1};return l.every(e=>a[e])?l.reduce((t,r)=>{let n=null==i?void 0:i[r];return n&&!u(a[r])&&(t[e[n].pos]=a[r]),t},{}):Object.keys(a).reduce((e,t)=>{if(!u(a[t])){let r=t;return s&&(r=parseInt(t,10)-1+""),Object.assign(e,{[r]:a[t]})}return e},{})}},groups:e}}())(e.headers["x-now-route-matches"])},normalizeDynamicRouteParams:(e,t)=>m(e,t,y,x),normalizeVercelUrl:(e,t,r)=>f(e,t,r,c,y),interpolateDynamicPath:(e,t)=>p(e,t,y)}}},7436:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return i}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function i(e){return r.test(e)?e.replace(n,"\\$&"):e}},4470:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return s}}),r(6031);let n=r(6184),i=r(4180);function o(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function s(e,t){var r;let s,l,u,{src:c,sizes:d,unoptimized:f=!1,priority:p=!1,loading:m,className:h,quality:g,width:y,height:b,fill:x=!1,style:v,onLoad:_,onLoadingComplete:E,placeholder:w="empty",blurDataURL:j,fetchPriority:P,layout:R,objectFit:O,objectPosition:S,lazyBoundary:C,lazyRoot:N,...A}=e,{imgConf:M,showAltText:k,blurComplete:T,defaultLoader:I}=t,D=M||i.imageConfigDefault;if("allSizes"in D)s=D;else{let e=[...D.deviceSizes,...D.imageSizes].sort((e,t)=>e-t),t=D.deviceSizes.sort((e,t)=>e-t);s={...D,allSizes:e,deviceSizes:t}}let U=A.loader||I;delete A.loader,delete A.srcSet;let z="__next_img_default"in U;if(z){if("custom"===s.loader)throw Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=U;U=t=>{let{config:r,...n}=t;return e(n)}}if(R){"fill"===R&&(x=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[R];e&&(v={...v,...e});let t={responsive:"100vw",fill:"100vw"}[R];t&&!d&&(d=t)}let L="",$=a(y),F=a(b);if("object"==typeof(r=c)&&(o(r)||void 0!==r.src)){let e=o(c)?c.default:c;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(l=e.blurWidth,u=e.blurHeight,j=j||e.blurDataURL,L=e.src,!x){if($||F){if($&&!F){let t=$/e.width;F=Math.round(e.height*t)}else if(!$&&F){let t=F/e.height;$=Math.round(e.width*t)}}else $=e.width,F=e.height}}let q=!p&&("lazy"===m||void 0===m);(!(c="string"==typeof c?c:L)||c.startsWith("data:")||c.startsWith("blob:"))&&(f=!0,q=!1),s.unoptimized&&(f=!0),z&&c.endsWith(".svg")&&!s.dangerouslyAllowSVG&&(f=!0),p&&(P="high");let G=a(g),W=Object.assign(x?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:O,objectPosition:S}:{},k?{}:{color:"transparent"},v),H=T||"empty"===w?null:"blur"===w?'url("data:image/svg+xml;charset=utf-8,'+(0,n.getImageBlurSvg)({widthInt:$,heightInt:F,blurWidth:l,blurHeight:u,blurDataURL:j||"",objectFit:W.objectFit})+'")':'url("'+w+'")',B=H?{backgroundSize:W.objectFit||"cover",backgroundPosition:W.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:H}:{},X=function(e){let{config:t,src:r,unoptimized:n,width:i,quality:o,sizes:a,loader:s}=e;if(n)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,r){let{deviceSizes:n,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let n;n=e.exec(r);n)t.push(parseInt(n[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=n[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:n,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,a),c=l.length-1;return{sizes:a||"w"!==u?a:"100vw",srcSet:l.map((e,n)=>s({config:t,src:r,quality:o,width:e})+" "+("w"===u?e:n+1)+u).join(", "),src:s({config:t,src:r,quality:o,width:l[c]})}}({config:s,src:c,unoptimized:f,width:$,quality:G,sizes:d,loader:U});return{props:{...A,loading:q?"lazy":m,fetchPriority:P,width:$,height:F,decoding:"async",className:h,style:{...W,...B},sizes:X.sizes,srcSet:X.srcSet,src:X.src},meta:{unoptimized:f,priority:p,placeholder:w,fill:x}}}},6325:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)&4294967295;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},3935:(e,t)=>{"use strict";function r(e,t){let r;let n=e.split("/");return(t||[]).some(t=>!!n[1]&&n[1].toLowerCase()===t.toLowerCase()&&(r=t,n.splice(1,1),e=n.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return r}})},6184:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:n,blurHeight:i,blurDataURL:o,objectFit:a}=e,s=n?40*n:t,l=i?40*i:r,u=s&&l?"viewBox='0 0 "+s+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===a?"xMidYMid":"cover"===a?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},4180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return n}});let r=["default","imgix","cloudinary","akamai","custom"],n={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",remotePatterns:[],unoptimized:!1}},7751:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getImageProps:function(){return s},default:function(){return l}});let n=r(6783),i=r(4470),o=r(6987),a=n._(r(4534)),s=e=>{let{props:t}=(0,i.getImgProps)(e,{defaultLoader:a.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}},l=o.Image},4534:(e,t)=>{"use strict";function r(e){let{config:t,src:r,width:n,quality:i}=e;return t.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+(i||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}}),r.__next_img_default=!0;let n=r},4084:(e,t,r)=>{"use strict";let n;n=r(1017),e.exports=n},5940:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},9716:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},9393:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return o},normalizeRscURL:function(){return a}});let n=r(5940),i=r(5458);function o(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,i.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},713:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return i}}),r(2881);let n=r(1662);function i(e,t){let r=new URL("http://n"),i=t?new URL(t,r):e.startsWith(".")?new URL("http://n"):r,{pathname:o,searchParams:a,search:s,hash:l,href:u,origin:c}=new URL(e,i);if(c!==r.origin)throw Error("invariant: invalid relative URL, router received "+e);return{pathname:o,query:(0,n.searchParamsToUrlQuery)(a),search:s,hash:l,href:u.slice(r.origin.length)}}},8223:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return o}});let n=r(1662),i=r(713);function o(e){if(e.startsWith("/"))return(0,i.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},8237:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return i}});let n=r(6358);function i(e,t){let r=[],i=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),o=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(i.source),i.flags):i,r);return(e,n)=>{if("string"!=typeof e)return!1;let i=o(e);if(!i)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete i.params[e.name];return{...n,...i.params}}}},2762:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{matchHas:function(){return c},compileNonPath:function(){return d},prepareDestination:function(){return f}});let n=r(6358),i=r(7436),o=r(8223),a=r(8988),s=r(4409),l=r(892);function u(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let i={},o=r=>{let n;let o=r.key;switch(r.type){case"header":o=o.toLowerCase(),n=e.headers[o];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,l.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[o];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return i[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(o)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{i[e]=t.groups[e]}):"host"===r.type&&t[0]&&(i.host=t[0])),!0}return!1};return!!r.every(e=>o(e))&&!n.some(e=>o(e))&&i}function d(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function f(e){let t;let r=Object.assign({},e.query);delete r.__nextLocale,delete r.__nextDefaultLocale,delete r.__nextDataReq,delete r.__nextInferredLocaleFromDefault,delete r[s.NEXT_RSC_UNION_QUERY];let l=e.destination;for(let t of Object.keys({...e.params,...r}))l=l.replace(RegExp(":"+(0,i.escapeStringRegexp)(t),"g"),"__ESC_COLON_"+t);let c=(0,o.parseUrl)(l),f=c.query,p=u(""+c.pathname+(c.hash||"")),m=u(c.hostname||""),h=[],g=[];(0,n.pathToRegexp)(p,h),(0,n.pathToRegexp)(m,g);let y=[];h.forEach(e=>y.push(e.name)),g.forEach(e=>y.push(e.name));let b=(0,n.compile)(p,{validate:!1}),x=(0,n.compile)(m,{validate:!1});for(let[t,r]of Object.entries(f))Array.isArray(r)?f[t]=r.map(t=>d(u(t),e.params)):"string"==typeof r&&(f[t]=d(u(r),e.params));let v=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!v.some(e=>y.includes(e)))for(let t of v)t in f||(f[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(p))for(let t of p.split("/")){let r=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){e.params["0"]=r;break}}try{let[r,n]=(t=b(e.params)).split("#",2);c.hostname=x(e.params),c.pathname=r,c.hash=(n?"#":"")+(n||""),delete c.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match");throw e}return c.query={...r,...c.query},{newUrl:t,destQuery:f,parsedDestination:c}}},1662:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,i]=e;Array.isArray(i)?i.forEach(e=>t.append(r,n(e))):t.set(r,n(i))}),t}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i},assign:function(){return o}})},5545:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},7158:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return i}});let n=r(2881);function i(e){let{re:t,groups:r}=e;return e=>{let i=t.exec(e);if(!i)return!1;let o=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},a={};return Object.keys(r).forEach(e=>{let t=r[e],n=i[t.pos];void 0!==n&&(a[e]=~n.indexOf("/")?n.split("/").map(e=>o(e)):t.repeat?[o(n)]:o(n))}),a}}},4654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRouteRegex:function(){return l},getNamedRouteRegex:function(){return d},getNamedMiddlewareRegex:function(){return f}});let n=r(8988),i=r(7436),o=r(5545);function a(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function s(e){let t=(0,o.removeTrailingSlash)(e).slice(1).split("/"),r={},s=1;return{parameterizedRoute:t.map(e=>{let t=n.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&o){let{key:e,optional:n,repeat:l}=a(o[1]);return r[e]={pos:s++,repeat:l,optional:n},"/"+(0,i.escapeStringRegexp)(t)+"([^/]+?)"}if(!o)return"/"+(0,i.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:n}=a(o[1]);return r[e]={pos:s++,repeat:t,optional:n},t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function l(e){let{parameterizedRoute:t,groups:r}=s(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function u(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:o,keyPrefix:s}=e,{key:l,optional:u,repeat:c}=a(n),d=l.replace(/\W/g,"");s&&(d=""+s+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),s?o[d]=""+s+l:o[d]=l;let p=t?(0,i.escapeStringRegexp)(t):"";return c?u?"(?:/"+p+"(?<"+d+">.+?))?":"/"+p+"(?<"+d+">.+?)":"/"+p+"(?<"+d+">[^/]+?)"}function c(e,t){let r;let a=(0,o.removeTrailingSlash)(e).slice(1).split("/"),s=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),l={};return{namedParameterizedRoute:a.map(e=>{let r=n.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),o=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&o){let[r]=e.split(o[0]);return u({getSafeRouteKey:s,interceptionMarker:r,segment:o[1],routeKeys:l,keyPrefix:t?"nxtI":void 0})}return o?u({getSafeRouteKey:s,segment:o[1],routeKeys:l,keyPrefix:t?"nxtP":void 0}):"/"+(0,i.escapeStringRegexp)(e)}).join(""),routeKeys:l}}function d(e,t){let r=c(e,t);return{...l(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function f(e,t){let{parameterizedRoute:r}=s(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:i}=c(e,!1);return{namedRegex:"^"+i+(n?"(?:(/.*)?)":"")+"$"}}},5458:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isGroupSegment:function(){return r},PAGE_SEGMENT_KEY:function(){return n},DEFAULT_SEGMENT_KEY:function(){return i}});let n="__PAGE__",i="__DEFAULT__"},2881:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{WEB_VITALS:function(){return r},execOnce:function(){return n},isAbsoluteUrl:function(){return o},getLocationOrigin:function(){return a},getURL:function(){return s},getDisplayName:function(){return l},isResSent:function(){return u},normalizeRepeatedSlashes:function(){return c},loadGetInitialProps:function(){return d},SP:function(){return f},ST:function(){return p},DecodeError:function(){return m},NormalizeError:function(){return h},PageNotFoundError:function(){return g},MissingStaticPage:function(){return y},MiddlewareNotFoundError:function(){return b},stringifyError:function(){return x}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return r||(r=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,o=e=>i.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&u(r))return n;if(!n)throw Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class h extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},6031:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},48:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g,metadata:()=>h});var n=r(5036),i=r(2195),o=r.n(i);r(5023);var a=r(6843);let s=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx`),{__esModule:l,$$typeof:u}=s;s.default;let c=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/lib/providers/query-provider.tsx#QueryProvider`),d=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx`),{__esModule:f,$$typeof:p}=d;d.default;let m=(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#ErrorBoundary`);(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#DefaultErrorFallback`),(0,a.createProxy)(String.raw`/home/<USER>/FECMS-sport/src/components/ui/error-boundary.tsx#useErrorHandler`);let h={title:"APISportsGame CMS",description:"Content Management System for APISportsGame API"};function g({children:e}){return n.jsx("html",{lang:"en",children:n.jsx("body",{className:o().className,children:n.jsx(m,{children:n.jsx(c,{children:e})})})})}},8075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(5036),i=r(7751),o=r.n(i);function a(){return(0,n.jsxs)("main",{className:"flex min-h-screen flex-col items-center justify-between p-24",children:[(0,n.jsxs)("div",{className:"z-10 max-w-5xl w-full items-center justify-between font-mono text-sm lg:flex",children:[(0,n.jsxs)("p",{className:"fixed left-0 top-0 flex w-full justify-center border-b border-gray-300 bg-gradient-to-b from-zinc-200 pb-6 pt-8 backdrop-blur-2xl dark:border-neutral-800 dark:bg-zinc-800/30 dark:from-inherit lg:static lg:w-auto  lg:rounded-xl lg:border lg:bg-gray-200 lg:p-4 lg:dark:bg-zinc-800/30",children:["Get started by editing\xa0",n.jsx("code",{className:"font-mono font-bold",children:"src/app/page.tsx"})]}),n.jsx("div",{className:"fixed bottom-0 left-0 flex h-48 w-full items-end justify-center bg-gradient-to-t from-white via-white dark:from-black dark:via-black lg:static lg:h-auto lg:w-auto lg:bg-none",children:(0,n.jsxs)("a",{className:"pointer-events-none flex place-items-center gap-2 p-8 lg:pointer-events-auto lg:p-0",href:"https://vercel.com?utm_source=create-next-app&utm_medium=appdir-template&utm_campaign=create-next-app",target:"_blank",rel:"noopener noreferrer",children:["By"," ",n.jsx(o(),{src:"/vercel.svg",alt:"Vercel Logo",className:"dark:invert",width:100,height:24,priority:!0})]})})]}),n.jsx("div",{className:"relative flex place-items-center before:absolute before:h-[300px] before:w-full sm:before:w-[480px] before:-translate-x-1/2 before:rounded-full before:bg-gradient-radial before:from-white before:to-transparent before:blur-2xl before:content-[''] after:absolute after:-z-20 after:h-[180px] after:w-full sm:after:w-[240px] after:translate-x-1/3 after:bg-gradient-conic after:from-sky-200 after:via-blue-200 after:blur-2xl after:content-[''] before:dark:bg-gradient-to-br before:dark:from-transparent before:dark:to-blue-700 before:dark:opacity-10 after:dark:from-sky-900 after:dark:via-[#0141ff] after:dark:opacity-40 before:lg:h-[360px] z-[-1]",children:n.jsx(o(),{className:"relative dark:drop-shadow-[0_0_0.3rem_#ffffff70] dark:invert",src:"/next.svg",alt:"Next.js Logo",width:180,height:37,priority:!0})}),(0,n.jsxs)("div",{className:"mb-32 grid text-center lg:max-w-5xl lg:w-full lg:mb-0 lg:grid-cols-4 lg:text-left",children:[(0,n.jsxs)("a",{href:"https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template&utm_campaign=create-next-app",className:"group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30",target:"_blank",rel:"noopener noreferrer",children:[(0,n.jsxs)("h2",{className:"mb-3 text-2xl font-semibold",children:["Docs"," ",n.jsx("span",{className:"inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none",children:"->"})]}),n.jsx("p",{className:"m-0 max-w-[30ch] text-sm opacity-50",children:"Find in-depth information about Next.js features and API."})]}),(0,n.jsxs)("a",{href:"https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app",className:"group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30",target:"_blank",rel:"noopener noreferrer",children:[(0,n.jsxs)("h2",{className:"mb-3 text-2xl font-semibold",children:["Learn"," ",n.jsx("span",{className:"inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none",children:"->"})]}),n.jsx("p",{className:"m-0 max-w-[30ch] text-sm opacity-50",children:"Learn about Next.js in an interactive course with\xa0quizzes!"})]}),(0,n.jsxs)("a",{href:"https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template&utm_campaign=create-next-app",className:"group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30",target:"_blank",rel:"noopener noreferrer",children:[(0,n.jsxs)("h2",{className:"mb-3 text-2xl font-semibold",children:["Templates"," ",n.jsx("span",{className:"inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none",children:"->"})]}),n.jsx("p",{className:"m-0 max-w-[30ch] text-sm opacity-50",children:"Explore starter templates for Next.js."})]}),(0,n.jsxs)("a",{href:"https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template&utm_campaign=create-next-app",className:"group rounded-lg border border-transparent px-5 py-4 transition-colors hover:border-gray-300 hover:bg-gray-100 hover:dark:border-neutral-700 hover:dark:bg-neutral-800/30",target:"_blank",rel:"noopener noreferrer",children:[(0,n.jsxs)("h2",{className:"mb-3 text-2xl font-semibold",children:["Deploy"," ",n.jsx("span",{className:"inline-block transition-transform group-hover:translate-x-1 motion-reduce:transform-none",children:"->"})]}),n.jsx("p",{className:"m-0 max-w-[30ch] text-sm opacity-50 text-balance",children:"Instantly deploy your Next.js site to a shareable URL with Vercel."})]})]})]})}},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var n=r(337);let i=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[638,783],()=>r(8527));module.exports=n})();