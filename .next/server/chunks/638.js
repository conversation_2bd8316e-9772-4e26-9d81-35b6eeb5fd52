exports.id=638,exports.ids=[638],exports.modules={1359:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{prefixes:function(){return a},bootstrap:function(){return l},wait:function(){return s},error:function(){return u},warn:function(){return c},ready:function(){return d},info:function(){return g},event:function(){return p},trace:function(){return f},warnOnce:function(){return v}});let n=r(1191),a={wait:(0,n.white)((0,n.bold)("○")),error:(0,n.red)((0,n.bold)("⨯")),warn:(0,n.yellow)((0,n.bold)("⚠")),ready:"▲",info:(0,n.white)((0,n.bold)(" ")),event:(0,n.green)((0,n.bold)("✓")),trace:(0,n.magenta)((0,n.bold)("\xbb"))},o={log:"log",warn:"warn",error:"error"};function i(e,...t){(""===t[0]||void 0===t[0])&&1===t.length&&t.shift();let r=e in o?o[e]:"log",n=a[e];0===t.length?console[r](""):console[r](" "+n,...t)}function l(...e){console.log(" ",...e)}function s(...e){i("wait",...e)}function u(...e){i("error",...e)}function c(...e){i("warn",...e)}function d(...e){i("ready",...e)}function g(...e){i("info",...e)}function p(...e){i("event",...e)}function f(...e){i("trace",...e)}let _=new Set;function v(...e){_.has(e[0])||(_.add(e.join(" ")),c(...e))}},5407:e=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),a=r(172),o=r(930),i="context",l=new n.NoopContextManager;class s{constructor(){}static getInstance(){return this._instance||(this._instance=new s),this._instance}setGlobalContextManager(e){return(0,a.registerGlobal)(i,e,o.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,a.getGlobal)(i)||l}disable(){this._getContextManager().disable(),(0,a.unregisterGlobal)(i,o.DiagAPI.instance())}}t.ContextAPI=s},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),a=r(912),o=r(957),i=r(172);class l{constructor(){function e(e){return function(...t){let r=(0,i.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:o.DiagLogLevel.INFO})=>{var n,l,s;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(n=e.stack)&&void 0!==n?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let u=(0,i.getGlobal)("diag"),c=(0,a.createLogLevelDiagLogger)(null!==(l=r.logLevel)&&void 0!==l?l:o.DiagLogLevel.INFO,e);if(u&&!r.suppressOverrideMessage){let e=null!==(s=Error().stack)&&void 0!==s?s:"<failed to generate stacktrace>";u.warn(`Current logger will be overwritten from ${e}`),c.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,i.registerGlobal)("diag",c,t,!0)},t.disable=()=>{(0,i.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new l),this._instance}}t.DiagAPI=l},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),a=r(172),o=r(930),i="metrics";class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalMeterProvider(e){return(0,a.registerGlobal)(i,e,o.DiagAPI.instance())}getMeterProvider(){return(0,a.getGlobal)(i)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,a.unregisterGlobal)(i,o.DiagAPI.instance())}}t.MetricsAPI=l},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),a=r(874),o=r(194),i=r(277),l=r(369),s=r(930),u="propagation",c=new a.NoopTextMapPropagator;class d{constructor(){this.createBaggage=l.createBaggage,this.getBaggage=i.getBaggage,this.getActiveBaggage=i.getActiveBaggage,this.setBaggage=i.setBaggage,this.deleteBaggage=i.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(u,e,s.DiagAPI.instance())}inject(e,t,r=o.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=o.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(u,s.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(u)||c}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),a=r(846),o=r(139),i=r(607),l=r(930),s="trace";class u{constructor(){this._proxyTracerProvider=new a.ProxyTracerProvider,this.wrapSpanContext=o.wrapSpanContext,this.isSpanContextValid=o.isSpanContextValid,this.deleteSpan=i.deleteSpan,this.getSpan=i.getSpan,this.getActiveSpan=i.getActiveSpan,this.getSpanContext=i.getSpanContext,this.setSpan=i.setSpan,this.setSpanContext=i.setSpanContext}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(s,this._proxyTracerProvider,l.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(s)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(s,l.DiagAPI.instance()),this._proxyTracerProvider=new a.ProxyTracerProvider}}t.TraceAPI=u},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),a=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function o(e){return e.getValue(a)||void 0}t.getBaggage=o,t.getActiveBaggage=function(){return o(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(a,t)},t.deleteBaggage=function(e){return e.deleteValue(a)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),a=r(993),o=r(830),i=n.DiagAPI.instance();t.createBaggage=function(e={}){return new a.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(i.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:o.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0;let n=r(491);t.context=n.ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class a{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=a},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let a=new r(t._currentContext);return a._currentContext.set(e,n),a},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0;let n=r(930);t.diag=n.DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class a{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return o("debug",this._namespace,e)}error(...e){return o("error",this._namespace,e)}info(...e){return o("info",this._namespace,e)}warn(...e){return o("warn",this._namespace,e)}verbose(...e){return o("verbose",this._namespace,e)}}function o(e,t,r){let a=(0,n.getGlobal)("diag");if(a)return r.unshift(t),a[e](...r)}t.DiagComponentLogger=a},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let a=t[r];return"function"==typeof a&&e>=n?a.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),a=r(521),o=r(130),i=a.VERSION.split(".")[0],l=Symbol.for(`opentelemetry.js.api.${i}`),s=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var o;let i=s[l]=null!==(o=s[l])&&void 0!==o?o:{version:a.VERSION};if(!n&&i[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(i.version!==a.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${i.version} for ${e} does not match previously registered API v${a.VERSION}`);return r.error(t.stack||t.message),!1}return i[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${a.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null===(t=s[l])||void 0===t?void 0:t.version;if(n&&(0,o.isCompatible)(n))return null===(r=s[l])||void 0===r?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${a.VERSION}.`);let r=s[l];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),a=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function o(e){let t=new Set([e]),r=new Set,n=e.match(a);if(!n)return()=>!1;let o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=o.prerelease)return function(t){return t===e};function i(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(a);if(!n)return i(e);let l={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};return null!=l.prerelease||o.major!==l.major?i(e):0===o.major?o.minor===l.minor&&o.patch<=l.patch?(t.add(e),!0):i(e):o.minor<=l.minor?(t.add(e),!0):i(e)}}t._makeCompatibilityCheck=o,t.isCompatible=o(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0;let n=r(653);t.metrics=n.MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class a extends n{add(e,t){}}t.NoopCounterMetric=a;class o extends n{add(e,t){}}t.NoopUpDownCounterMetric=o;class i extends n{record(e,t){}}t.NoopHistogramMetric=i;class l{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=l;class s extends l{}t.NoopObservableCounterMetric=s;class u extends l{}t.NoopObservableGaugeMetric=u;class c extends l{}t.NoopObservableUpDownCounterMetric=c,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new a,t.NOOP_HISTOGRAM_METRIC=new i,t.NOOP_UP_DOWN_COUNTER_METRIC=new o,t.NOOP_OBSERVABLE_COUNTER_METRIC=new s,t.NOOP_OBSERVABLE_GAUGE_METRIC=new u,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new c,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class a{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=a,t.NOOP_METER_PROVIDER=new a},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:global},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),a=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),a(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0;let n=r(181);t.propagation=n.PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0;let n=r(997);t.trace=n.TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class a{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=a},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),a=r(607),o=r(403),i=r(139),l=n.ContextAPI.getInstance();class s{startSpan(e,t,r=l.active()){if(null==t?void 0:t.root)return new o.NonRecordingSpan;let n=r&&(0,a.getSpanContext)(r);return"object"==typeof n&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,i.isSpanContextValid)(n)?new o.NonRecordingSpan(n):new o.NonRecordingSpan}startActiveSpan(e,t,r,n){let o,i,s;if(arguments.length<2)return;2==arguments.length?s=t:3==arguments.length?(o=t,s=r):(o=t,i=r,s=n);let u=null!=i?i:l.active(),c=this.startSpan(e,o,u),d=(0,a.setSpan)(u,c);return l.with(d,s,void 0,c)}}t.NoopTracer=s},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class a{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=a},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class a{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let a=this._getTracer();return Reflect.apply(a.startActiveSpan,a,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=a},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),a=new(r(124)).NoopTracerProvider;class o{getTracer(e,t,r){var a;return null!==(a=this.getDelegateTracer(e,t,r))&&void 0!==a?a:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!==(e=this._delegate)&&void 0!==e?e:a}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null===(n=this._delegate)||void 0===n?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=o},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),a=r(403),o=r(491),i=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function l(e){return e.getValue(i)||void 0}function s(e,t){return e.setValue(i,t)}t.getSpan=l,t.getActiveSpan=function(){return l(o.ContextAPI.getInstance().active())},t.setSpan=s,t.deleteSpan=function(e){return e.deleteValue(i)},t.setSpanContext=function(e,t){return s(e,new a.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null===(t=l(e))||void 0===t?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class a{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),a=r.indexOf("=");if(-1!==a){let o=r.slice(0,a),i=r.slice(a+1,t.length);(0,n.validateKey)(o)&&(0,n.validateValue)(i)&&e.set(o,i)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new a;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=a},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,a=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,o=RegExp(`^(?:${n}|${a})$`),i=/^[ -~]{0,255}[!-~]$/,l=/,|=/;t.validateKey=function(e){return o.test(e)},t.validateValue=function(e){return i.test(e)&&!l.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),a=r(403),o=/^([0-9a-f]{32})$/i,i=/^[0-9a-f]{16}$/i;function l(e){return o.test(e)&&e!==n.INVALID_TRACEID}function s(e){return i.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=l,t.isValidSpanId=s,t.isSpanContextValid=function(e){return l(e.traceId)&&s(e.spanId)},t.wrapSpanContext=function(e){return new a.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var o=r[e]={exports:{}},i=!0;try{t[e].call(o.exports,o,o.exports,n),i=!1}finally{i&&delete r[e]}return o.exports}n.ab=__dirname+"/";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=n(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=n(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=n(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var o=n(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return o.DiagLogLevel}});var i=n(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return i.createNoopMeter}});var l=n(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return l.ValueType}});var s=n(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return s.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return s.defaultTextMapSetter}});var u=n(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return u.ProxyTracer}});var c=n(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return c.ProxyTracerProvider}});var d=n(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var g=n(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return g.SpanKind}});var p=n(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var f=n(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var _=n(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return _.createTraceState}});var v=n(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return v.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return v.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return v.isValidSpanId}});var S=n(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return S.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return S.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return S.INVALID_SPAN_CONTEXT}});let b=n(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return b.context}});let h=n(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return h.diag}});let E=n(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return E.metrics}});let O=n(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return O.propagation}});let P=n(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return P.trace}}),a.default={context:b.context,diag:h.diag,metrics:E.metrics,propagation:O.propagation,trace:P.trace}})(),e.exports=a})()},2740:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXT_QUERY_PARAM_PREFIX:function(){return r},PRERENDER_REVALIDATE_HEADER:function(){return n},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return a},RSC_PREFETCH_SUFFIX:function(){return o},RSC_SUFFIX:function(){return i},NEXT_DATA_SUFFIX:function(){return l},NEXT_META_SUFFIX:function(){return s},NEXT_BODY_SUFFIX:function(){return u},NEXT_CACHE_TAGS_HEADER:function(){return c},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return d},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return g},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return p},NEXT_CACHE_TAG_MAX_LENGTH:function(){return f},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return _},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return v},CACHE_ONE_YEAR:function(){return S},MIDDLEWARE_FILENAME:function(){return b},MIDDLEWARE_LOCATION_REGEXP:function(){return h},INSTRUMENTATION_HOOK_FILENAME:function(){return E},PAGES_DIR_ALIAS:function(){return O},DOT_NEXT_ALIAS:function(){return P},ROOT_DIR_ALIAS:function(){return R},APP_DIR_ALIAS:function(){return T},RSC_MOD_REF_PROXY_ALIAS:function(){return m},RSC_ACTION_VALIDATE_ALIAS:function(){return N},RSC_ACTION_PROXY_ALIAS:function(){return x},RSC_ACTION_ENCRYPTION_ALIAS:function(){return y},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return C},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return A},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return I},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return M},SERVER_PROPS_SSG_CONFLICT:function(){return D},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return L},SERVER_PROPS_EXPORT_ERROR:function(){return w},GSP_NO_RETURNED_VALUE:function(){return j},GSSP_NO_RETURNED_VALUE:function(){return V},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return B},GSSP_COMPONENT_MEMBER_ERROR:function(){return G},NON_STANDARD_NODE_ENV:function(){return U},SSG_FALLBACK_EXPORT_ERROR:function(){return H},ESLINT_DEFAULT_DIRS:function(){return F},ESLINT_PROMPT_VALUES:function(){return $},SERVER_RUNTIME:function(){return X},WEBPACK_LAYERS:function(){return K},WEBPACK_RESOURCE_QUERIES:function(){return W}});let r="nxtP",n="x-prerender-revalidate",a="x-prerender-revalidate-if-generated",o=".prefetch.rsc",i=".rsc",l=".json",s=".meta",u=".body",c="x-next-cache-tags",d="x-next-cache-soft-tags",g="x-next-revalidated-tags",p="x-next-revalidate-tag-token",f=256,_=1024,v="_N_T_",S=31536e3,b="middleware",h=`(?:src/)?${b}`,E="instrumentation",O="private-next-pages",P="private-dot-next",R="private-next-root-dir",T="private-next-app-dir",m="next/dist/build/webpack/loaders/next-flight-loader/module-proxy",N="private-next-rsc-action-validate",x="private-next-rsc-server-reference",y="private-next-rsc-action-encryption",C="private-next-rsc-action-client-wrapper",A="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",I="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",M="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",D="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",L="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",w="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",j="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",V="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",B="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",G="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",U='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',H="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",F=["app","pages","components","lib","src"],$=[{title:"Strict",recommended:!0,config:{extends:"next/core-web-vitals"}},{title:"Base",config:{extends:"next"}},{title:"Cancel",config:null}],X={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},k={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},K={...k,GROUP:{server:[k.reactServerComponents,k.actionBrowser,k.appMetadataRoute,k.appRouteHandler],nonClientServerTarget:[k.middleware,k.api],app:[k.reactServerComponents,k.actionBrowser,k.appMetadataRoute,k.appRouteHandler,k.serverSideRendering,k.appPagesBrowser,k.shared]}},W={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},1191:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{reset:function(){return s},bold:function(){return u},dim:function(){return c},italic:function(){return d},underline:function(){return g},inverse:function(){return p},hidden:function(){return f},strikethrough:function(){return _},black:function(){return v},red:function(){return S},green:function(){return b},yellow:function(){return h},blue:function(){return E},magenta:function(){return O},purple:function(){return P},cyan:function(){return R},white:function(){return T},gray:function(){return m},bgBlack:function(){return N},bgRed:function(){return x},bgGreen:function(){return y},bgYellow:function(){return C},bgBlue:function(){return A},bgMagenta:function(){return I},bgCyan:function(){return M},bgWhite:function(){return D}});let{env:n,stdout:a}=(null==(r=globalThis)?void 0:r.process)??{},o=n&&!n.NO_COLOR&&(n.FORCE_COLOR||(null==a?void 0:a.isTTY)&&!n.CI&&"dumb"!==n.TERM),i=(e,t,r,n)=>{let a=e.substring(0,n)+r,o=e.substring(n+t.length),l=o.indexOf(t);return~l?a+i(o,t,r,l):a+o},l=(e,t,r=e)=>o?n=>{let a=""+n,o=a.indexOf(t,e.length);return~o?e+i(a,t,r,o)+t:e+a+t}:String,s=o?e=>`\x1b[0m${e}\x1b[0m`:String,u=l("\x1b[1m","\x1b[22m","\x1b[22m\x1b[1m"),c=l("\x1b[2m","\x1b[22m","\x1b[22m\x1b[2m"),d=l("\x1b[3m","\x1b[23m"),g=l("\x1b[4m","\x1b[24m"),p=l("\x1b[7m","\x1b[27m"),f=l("\x1b[8m","\x1b[28m"),_=l("\x1b[9m","\x1b[29m"),v=l("\x1b[30m","\x1b[39m"),S=l("\x1b[31m","\x1b[39m"),b=l("\x1b[32m","\x1b[39m"),h=l("\x1b[33m","\x1b[39m"),E=l("\x1b[34m","\x1b[39m"),O=l("\x1b[35m","\x1b[39m"),P=l("\x1b[38;2;173;127;168m","\x1b[39m"),R=l("\x1b[36m","\x1b[39m"),T=l("\x1b[37m","\x1b[39m"),m=l("\x1b[90m","\x1b[39m"),N=l("\x1b[40m","\x1b[49m"),x=l("\x1b[41m","\x1b[49m"),y=l("\x1b[42m","\x1b[49m"),C=l("\x1b[43m","\x1b[49m"),A=l("\x1b[44m","\x1b[49m"),I=l("\x1b[45m","\x1b[49m"),M=l("\x1b[46m","\x1b[49m"),D=l("\x1b[47m","\x1b[49m")},9108:(e,t)=>{"use strict";var r;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return r}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(r||(r={}))},9678:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{validateRevalidate:function(){return s},validateTags:function(){return u},addImplicitTags:function(){return d},patchFetch:function(){return p}});let n=r(5237),a=r(7636),o=r(2740),i=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=l(t);if(r&&r.has(e))return r.get(e);var n={},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var i=a?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(n,o,i):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}(r(1359));function l(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(l=function(e){return e?r:t})(e)}function s(e,t){try{let r;if(!1===e)r=e;else if("number"==typeof e&&!isNaN(e)&&e>-1)r=e;else if(void 0!==e)throw Error(`Invalid revalidate value "${e}" on "${t}", must be a non-negative number or "false"`);return r}catch(e){if(e instanceof Error&&e.message.includes("Invalid revalidate"))throw e;return}}function u(e,t){let r=[],n=[];for(let t of e)"string"!=typeof t?n.push({tag:t,reason:"invalid type, must be a string"}):t.length>o.NEXT_CACHE_TAG_MAX_LENGTH?n.push({tag:t,reason:`exceeded max length of ${o.NEXT_CACHE_TAG_MAX_LENGTH}`}):r.push(t);if(n.length>0)for(let{tag:e,reason:r}of(console.warn(`Warning: invalid tags passed to ${t}: `),n))console.log(`tag: "${e}" ${r}`);return r}let c=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${n.endsWith("/")?"":"/"}layout`),t.push(n))}}return t};function d(e){var t,r;let n=[],{pagePath:a,urlPathname:i}=e;if(Array.isArray(e.tags)||(e.tags=[]),a)for(let r of c(a))r=`${o.NEXT_CACHE_IMPLICIT_TAG_ID}${r}`,(null==(t=e.tags)?void 0:t.includes(r))||e.tags.push(r),n.push(r);if(i){let t=new URL(i,"http://n").pathname,a=`${o.NEXT_CACHE_IMPLICIT_TAG_ID}${t}`;(null==(r=e.tags)?void 0:r.includes(a))||e.tags.push(a),n.push(a)}return n}function g(e,t){if(!e)return;e.fetchMetrics||(e.fetchMetrics=[]);let r=["url","status","method"];e.fetchMetrics.some(e=>r.every(r=>e[r]===t[r]))||e.fetchMetrics.push({url:t.url,cacheStatus:t.cacheStatus,cacheReason:t.cacheReason,status:t.status,method:t.method,start:t.start,end:Date.now(),idx:e.nextFetchId||0})}function p({serverHooks:e,staticGenerationAsyncStorage:t}){if(globalThis._nextOriginalFetch||(globalThis._nextOriginalFetch=globalThis.fetch),globalThis.fetch.__nextPatched)return;let{DynamicServerError:r}=e,l=globalThis._nextOriginalFetch;globalThis.fetch=async(e,c)=>{var p,f;let _;try{(_=new URL(e instanceof Request?e.url:e)).username="",_.password=""}catch{_=void 0}let v=(null==_?void 0:_.href)??"",S=Date.now(),b=(null==c?void 0:null==(p=c.method)?void 0:p.toUpperCase())||"GET",h=(null==(f=null==c?void 0:c.next)?void 0:f.internal)===!0,E="1"===process.env.NEXT_OTEL_FETCH_DISABLED;return await (0,a.getTracer)().trace(h?n.NextNodeServerSpan.internalFetch:n.AppRenderSpan.fetch,{hideSpan:E,kind:a.SpanKind.CLIENT,spanName:["fetch",b,v].filter(Boolean).join(" "),attributes:{"http.url":v,"http.method":b,"net.peer.name":null==_?void 0:_.hostname,"net.peer.port":(null==_?void 0:_.port)||void 0}},async()=>{var n;let a,p,f;let _=t.getStore()||(null==fetch.__nextGetStaticStore?void 0:fetch.__nextGetStaticStore.call(fetch)),b=e&&"object"==typeof e&&"string"==typeof e.method,E=t=>(null==c?void 0:c[t])||(b?e[t]:null);if(!_||h||_.isDraftMode)return l(e,c);let O=t=>{var r,n,a;return void 0!==(null==c?void 0:null==(r=c.next)?void 0:r[t])?null==c?void 0:null==(n=c.next)?void 0:n[t]:b?null==(a=e.next)?void 0:a[t]:void 0},P=O("revalidate"),R=u(O("tags")||[],`fetch ${e.toString()}`);if(Array.isArray(R))for(let e of(_.tags||(_.tags=[]),R))_.tags.includes(e)||_.tags.push(e);let T=d(_),m="only-cache"===_.fetchCache,N="force-cache"===_.fetchCache,x="default-cache"===_.fetchCache,y="default-no-store"===_.fetchCache,C="only-no-store"===_.fetchCache,A="force-no-store"===_.fetchCache,I=!!_.isUnstableNoStore,M=E("cache"),D="";"string"==typeof M&&void 0!==P&&(b&&"default"===M||i.warn(`fetch for ${v} on ${_.urlPathname} specified "cache: ${M}" and "revalidate: ${P}", only one should be specified.`),M=void 0),"force-cache"===M?P=!1:("no-cache"===M||"no-store"===M||A||C)&&(P=0),("no-cache"===M||"no-store"===M)&&(D=`cache: ${M}`),f=s(P,_.urlPathname);let L=E("headers"),w="function"==typeof(null==L?void 0:L.get)?L:new Headers(L||{}),j=w.get("authorization")||w.get("cookie"),V=!["get","head"].includes((null==(n=E("method"))?void 0:n.toLowerCase())||"get"),B=(j||V)&&0===_.revalidate;if(A&&(D="fetchCache = force-no-store"),C){if("force-cache"===M||void 0!==f&&(!1===f||f>0))throw Error(`cache: 'force-cache' used on fetch for ${v} with 'export const fetchCache = 'only-no-store'`);D="fetchCache = only-no-store"}if(m&&"no-store"===M)throw Error(`cache: 'no-store' used on fetch for ${v} with 'export const fetchCache = 'only-cache'`);N&&(void 0===P||0===P)&&(D="fetchCache = force-cache",f=!1),void 0===f?x?(f=!1,D="fetchCache = default-cache"):B?(f=0,D="auto no cache"):y?(f=0,D="fetchCache = default-no-store"):I?(f=0,D="noStore call"):(D="auto cache",f="boolean"!=typeof _.revalidate&&void 0!==_.revalidate&&_.revalidate):D||(D=`revalidate: ${f}`),_.forceStatic&&0===f||B||void 0!==_.revalidate&&("number"!=typeof f||!1!==_.revalidate&&("number"!=typeof _.revalidate||!(f<_.revalidate)))||(0===f&&(null==_.postpone||_.postpone.call(_,"revalidate: 0")),_.revalidate=f);let G="number"==typeof f&&f>0||!1===f;if(_.incrementalCache&&G)try{a=await _.incrementalCache.fetchCacheKey(v,b?e:c)}catch(t){console.error("Failed to generate cache key for",e)}let U=_.nextFetchId??1;_.nextFetchId=U+1;let H="number"!=typeof f?o.CACHE_ONE_YEAR:f,F=async(t,r)=>{let n=["cache","credentials","headers","integrity","keepalive","method","mode","redirect","referrer","referrerPolicy","window","duplex",...t?[]:["signal"]];if(b){let t=e,r={body:t._ogBody||t.body};for(let e of n)r[e]=t[e];e=new Request(t.url,r)}else if(c){let e=c;for(let t of(c={body:c._ogBody||c.body},n))c[t]=e[t]}let o={...c,next:{...null==c?void 0:c.next,fetchType:"origin",fetchIdx:U}};return l(e,o).then(async n=>{if(t||g(_,{start:S,url:v,cacheReason:r||D,cacheStatus:0===f||r?"skip":"miss",status:n.status,method:o.method||"GET"}),200===n.status&&_.incrementalCache&&a&&G){let t=Buffer.from(await n.arrayBuffer());try{await _.incrementalCache.set(a,{kind:"FETCH",data:{headers:Object.fromEntries(n.headers.entries()),body:t.toString("base64"),status:n.status,url:n.url},revalidate:H},{fetchCache:!0,revalidate:f,fetchUrl:v,fetchIdx:U,tags:R})}catch(t){console.warn("Failed to set fetch cache",e,t)}let r=new Response(t,{headers:new Headers(n.headers),status:n.status});return Object.defineProperty(r,"url",{value:n.url}),r}return n})},$=()=>Promise.resolve();if(a&&_.incrementalCache){$=await _.incrementalCache.lock(a);let e=_.isOnDemandRevalidate?null:await _.incrementalCache.get(a,{kindHint:"fetch",revalidate:f,fetchUrl:v,fetchIdx:U,tags:R,softTags:T});if(e?await $():p="cache-control: no-cache (hard refresh)",(null==e?void 0:e.value)&&"FETCH"===e.value.kind&&!(_.isRevalidate&&e.isStale)){e.isStale&&(_.pendingRevalidates??={},_.pendingRevalidates[a]||(_.pendingRevalidates[a]=F(!0).catch(console.error)));let t=e.value.data;g(_,{start:S,url:v,cacheReason:D,cacheStatus:"hit",status:t.status||200,method:(null==c?void 0:c.method)||"GET"});let r=new Response(Buffer.from(t.body,"base64"),{headers:t.headers,status:t.status});return Object.defineProperty(r,"url",{value:e.value.data.url}),r}}if(_.isStaticGeneration&&c&&"object"==typeof c){let{cache:t}=c;if(!_.forceStatic&&"no-store"===t){let t=`no-store fetch ${e}${_.urlPathname?` ${_.urlPathname}`:""}`;null==_.postpone||_.postpone.call(_,t),_.revalidate=0;let n=new r(t);_.dynamicUsageErr=n,_.dynamicUsageDescription=t}let n="next"in c,{next:a={}}=c;if("number"==typeof a.revalidate&&(void 0===_.revalidate||"number"==typeof _.revalidate&&a.revalidate<_.revalidate)){if(!_.forceDynamic&&!_.forceStatic&&0===a.revalidate){let t=`revalidate: 0 fetch ${e}${_.urlPathname?` ${_.urlPathname}`:""}`;null==_.postpone||_.postpone.call(_,t);let n=new r(t);_.dynamicUsageErr=n,_.dynamicUsageDescription=t}_.forceStatic&&0===a.revalidate||(_.revalidate=a.revalidate)}n&&delete c.next}return F(!1,p).finally($)})},globalThis.fetch.__nextGetStaticStore=()=>t,globalThis.fetch.__nextPatched=!0}},5237:(e,t)=>{"use strict";var r,n,a,o,i,l,s,u,c,d,g;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NextVanillaSpanAllowlist:function(){return p},BaseServerSpan:function(){return r},LoadComponentsSpan:function(){return n},NextServerSpan:function(){return a},NextNodeServerSpan:function(){return o},StartServerSpan:function(){return i},RenderSpan:function(){return l},RouterSpan:function(){return u},AppRenderSpan:function(){return s},NodeSpan:function(){return c},AppRouteRouteHandlersSpan:function(){return d},ResolveMetadataSpan:function(){return g}}),function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(r||(r={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(n||(n={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(a||(a={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(o||(o={})),(i||(i={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(l||(l={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(s||(s={})),(u||(u={})).executeRoute="Router.executeRoute",(c||(c={})).runHandler="Node.runHandler",(d||(d={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(g||(g={}));let p=["BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule"]},7636:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTracer:function(){return b},SpanStatusCode:function(){return s},SpanKind:function(){return u}});let a=r(5237);try{n=r(5407)}catch(e){n=r(5407)}let{context:o,propagation:i,trace:l,SpanStatusCode:s,SpanKind:u,ROOT_CONTEXT:c}=n,d=e=>null!==e&&"object"==typeof e&&"function"==typeof e.then,g=(e,t)=>{(null==t?void 0:t.bubble)===!0?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:s.ERROR,message:null==t?void 0:t.message})),e.end()},p=new Map,f=n.createContextKey("next.rootSpanId"),_=0,v=()=>_++;class S{getTracerInstance(){return l.getTracer("next.js","0.0.1")}getContext(){return o}getActiveScopeSpan(){return l.getSpan(null==o?void 0:o.active())}withPropagatedContext(e,t,r){let n=o.active();if(l.getSpanContext(n))return t();let a=i.extract(n,e,r);return o.with(a,t)}trace(...e){var t;let[r,n,i]=e,{fn:s,options:u}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}};if(!a.NextVanillaSpanAllowlist.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||u.hideSpan)return s();let _=u.spanName??r,S=this.getSpanContext((null==u?void 0:u.parentSpan)??this.getActiveScopeSpan()),b=!1;S?(null==(t=l.getSpanContext(S))?void 0:t.isRemote)&&(b=!0):(S=c,b=!0);let h=v();return u.attributes={"next.span_name":_,"next.span_type":r,...u.attributes},o.with(S.setValue(f,h),()=>this.getTracerInstance().startActiveSpan(_,u,e=>{let t=()=>{p.delete(h)};b&&p.set(h,new Map(Object.entries(u.attributes??{})));try{if(s.length>1)return s(e,t=>g(e,t));let r=s(e);if(d(r))return r.then(t=>(e.end(),t)).catch(t=>{throw g(e,t),t}).finally(t);return e.end(),t(),r}catch(r){throw g(e,r),t(),r}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return a.NextVanillaSpanAllowlist.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let a=arguments.length-1,l=arguments[a];if("function"!=typeof l)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(o.active(),l);return t.trace(r,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?l.setSpan(o.active(),e):void 0}getRootSpanAttributes(){let e=o.active().getValue(f);return p.get(e)}}let b=(()=>{let e=new S;return()=>e})()}};