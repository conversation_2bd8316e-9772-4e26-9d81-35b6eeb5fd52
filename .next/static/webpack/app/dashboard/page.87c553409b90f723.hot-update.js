"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/hooks/useAuth */ \"(app-pages-browser)/./src/lib/hooks/useAuth.ts\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Radio,Shield,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Radio,Shield,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Radio,Shield,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Radio,Shield,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Radio,Shield,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Clock,Radio,Shield,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    var _allFixtures_meta_totalItems, _allFixtures_meta, _liveFixtures_data, _leagues_meta_totalItems, _leagues_meta, _liveFixtures_data1;\n    _s();\n    const { user } = (0,_lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const { isAdmin, isEditor, isModerator } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_2__.usePermissions)();\n    // Fetch real data\n    const { data: liveFixtures } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"live-upcoming\"\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_5__.fixturesApi.getUpcomingAndLive({\n                limit: 5\n            }),\n        refetchInterval: 30000\n    });\n    const { data: allFixtures } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_5__.fixturesApi.getFixtures({\n                limit: 1\n            })\n    });\n    const { data: leagues } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            \"leagues\",\n            \"all\"\n        ],\n        queryFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_6__.leaguesApi.getLeagues({\n                limit: 1\n            })\n    });\n    const getRoleColor = (role)=>{\n        switch(role){\n            case \"admin\":\n                return \"bg-red-100 text-red-800\";\n            case \"editor\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"moderator\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getWelcomeMessage = ()=>{\n        const hour = new Date().getHours();\n        if (hour < 12) return \"Good morning\";\n        if (hour < 18) return \"Good afternoon\";\n        return \"Good evening\";\n    };\n    const quickStats = [\n        {\n            title: \"Total Fixtures\",\n            value: (allFixtures === null || allFixtures === void 0 ? void 0 : (_allFixtures_meta = allFixtures.meta) === null || _allFixtures_meta === void 0 ? void 0 : (_allFixtures_meta_totalItems = _allFixtures_meta.totalItems) === null || _allFixtures_meta_totalItems === void 0 ? void 0 : _allFixtures_meta_totalItems.toLocaleString()) || \"Loading...\",\n            icon: _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Active fixtures in database\",\n            loading: !allFixtures\n        },\n        {\n            title: \"Live Matches\",\n            value: (liveFixtures === null || liveFixtures === void 0 ? void 0 : (_liveFixtures_data = liveFixtures.data) === null || _liveFixtures_data === void 0 ? void 0 : _liveFixtures_data.filter((f)=>[\n                    \"1H\",\n                    \"2H\",\n                    \"HT\"\n                ].includes(f.status)).length) || \"0\",\n            icon: _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Currently live matches\",\n            loading: !liveFixtures\n        },\n        {\n            title: \"Active Leagues\",\n            value: (leagues === null || leagues === void 0 ? void 0 : (_leagues_meta = leagues.meta) === null || _leagues_meta === void 0 ? void 0 : (_leagues_meta_totalItems = _leagues_meta.totalItems) === null || _leagues_meta_totalItems === void 0 ? void 0 : _leagues_meta_totalItems.toLocaleString()) || \"Loading...\",\n            icon: _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Currently active leagues\",\n            loading: !leagues\n        },\n        {\n            title: \"Upcoming Today\",\n            value: (liveFixtures === null || liveFixtures === void 0 ? void 0 : (_liveFixtures_data1 = liveFixtures.data) === null || _liveFixtures_data1 === void 0 ? void 0 : _liveFixtures_data1.filter((f)=>f.status === \"NS\").length) || \"0\",\n            icon: _barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Matches scheduled today\",\n            loading: !liveFixtures\n        }\n    ];\n    const recentActivities = [\n        {\n            action: \"Fixture sync completed\",\n            time: \"2 minutes ago\",\n            type: \"sync\"\n        },\n        {\n            action: \"New broadcast link added\",\n            time: \"15 minutes ago\",\n            type: \"broadcast\"\n        },\n        {\n            action: \"League updated: Premier League\",\n            time: \"1 hour ago\",\n            type: \"league\"\n        },\n        {\n            action: \"User tier upgraded\",\n            time: \"2 hours ago\",\n            type: \"user\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg border p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: [\n                                        getWelcomeMessage(),\n                                        \", \",\n                                        (user === null || user === void 0 ? void 0 : user.fullName) || (user === null || user === void 0 ? void 0 : user.username),\n                                        \"!\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: \"Welcome to the APISportsGame Content Management System\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    className: \"\".concat(getRoleColor((user === null || user === void 0 ? void 0 : user.role) || \"\"), \" flex items-center space-x-1\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-3 w-3\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"capitalize\",\n                                            children: user === null || user === void 0 ? void 0 : user.role\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-right text-sm text-gray-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Last login\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-medium\",\n                                            children: (user === null || user === void 0 ? void 0 : user.lastLoginAt) ? new Date(user.lastLoginAt).toLocaleDateString() : \"First time\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: quickStats.map((stat, index)=>{\n                    const Icon = stat.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-gray-600\",\n                                                children: stat.title\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-gray-900\",\n                                                children: stat.value\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-1\",\n                                                children: stat.description\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-blue-100 p-3 rounded-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 15\n                        }, this)\n                    }, index, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Recent Activities\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"Latest system activities and updates\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: recentActivities.map((activity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-blue-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm font-medium text-gray-900\",\n                                                            children: activity.action\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 181,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: activity.time\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                        children: \"Common tasks and shortcuts\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 204,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"View Live Fixtures\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 206,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Check ongoing matches\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 15\n                                        }, this),\n                                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Add Broadcast Link\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Add new streaming link\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this),\n                                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Clock_Radio_Shield_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"h-5 w-5 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Manage Users\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"User administration\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/page.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"6sK9jwKhwMWTvsP1mZkBDMSg91I=\", false, function() {\n    return [\n        _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.useAuth,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_2__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});