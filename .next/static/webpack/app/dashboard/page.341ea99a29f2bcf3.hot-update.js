"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/lib/api/leagues.ts":
/*!********************************!*\
  !*** ./src/lib/api/leagues.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   leaguesApi: function() { return /* binding */ leaguesApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst leaguesApi = {\n    // Public endpoint via proxy\n    getLeagues: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await fetch(\"/api/leagues?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch leagues\");\n        }\n        return await response.json();\n    },\n    // Requires authentication\n    getLeagueById: async (externalId, season)=>{\n        const params = season ? \"?season=\".concat(season) : \"\";\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/leagues/\".concat(externalId).concat(params));\n        return response;\n    },\n    // Editor+ access required\n    createLeague: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/football/leagues\", data);\n        return response;\n    },\n    // Editor+ access required\n    updateLeague: async (id, data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(\"/football/leagues/\".concat(id), data);\n        return response;\n    },\n    // Helper methods for common operations\n    getActiveLeagues: async ()=>{\n        return leaguesApi.getLeagues({\n            active: true\n        });\n    },\n    getLeaguesByCountry: async (country)=>{\n        return leaguesApi.getLeagues({\n            country\n        });\n    },\n    toggleLeagueStatus: async (id, active)=>{\n        return leaguesApi.updateLeague(id, {\n            active\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/leagues.ts\n"));

/***/ })

});