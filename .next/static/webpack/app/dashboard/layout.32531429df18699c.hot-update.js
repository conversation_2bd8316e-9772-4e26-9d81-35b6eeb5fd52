"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        title: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        title: \"Fixtures\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        children: [\n            {\n                title: \"All Fixtures\",\n                href: \"/dashboard/fixtures\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n            },\n            {\n                title: \"Live & Upcoming\",\n                href: \"/dashboard/fixtures/live\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            },\n            {\n                title: \"Sync Data\",\n                href: \"/dashboard/fixtures/sync\",\n                icon: Sync,\n                requiredRole: \"admin\"\n            }\n        ]\n    },\n    {\n        title: \"Leagues\",\n        href: \"/dashboard/leagues\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        title: \"Teams\",\n        href: \"/dashboard/teams\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        title: \"Broadcast Links\",\n        href: \"/dashboard/broadcast\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        requiredRole: \"editor\"\n    },\n    {\n        title: \"User Management\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        requiredRole: \"admin\",\n        children: [\n            {\n                title: \"System Users\",\n                href: \"/dashboard/users/system\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n            },\n            {\n                title: \"Registered Users\",\n                href: \"/dashboard/users/registered\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n            },\n            {\n                title: \"Tier Statistics\",\n                href: \"/dashboard/users/tiers\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Settings\",\n        href: \"/dashboard/settings\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n    }\n];\nconst Sidebar = (param)=>{\n    let { isOpen = true, onClose, className } = param;\n    var _this = undefined;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { hasRole } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Close sidebar on mobile when route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && onClose) {\n            onClose();\n        }\n    }, [\n        pathname,\n        isMobile,\n        onClose\n    ]);\n    const toggleExpanded = (title)=>{\n        setExpandedItems((prev)=>prev.includes(title) ? prev.filter((item)=>item !== title) : [\n                ...prev,\n                title\n            ]);\n    };\n    const isActive = (href)=>{\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    const canAccessItem = (item)=>{\n        if (!item.requiredRole) return true;\n        return hasRole(item.requiredRole);\n    };\n    const renderNavItem = function(item) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if (!canAccessItem(item)) return null;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.title);\n        const Icon = item.icon;\n        if (hasChildren) {\n            var _item_children;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>toggleExpanded(item.title),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors\", \"text-gray-700 hover:text-gray-900 hover:bg-gray-100\", level > 0 && \"ml-4\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"mr-3 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, _this),\n                                    item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2 text-xs\",\n                                        children: item.badge\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, _this),\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 15\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, _this),\n                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 space-y-1\",\n                        children: (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>renderNavItem(child, level + 1))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, _this)\n                ]\n            }, item.title, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, _this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            href: item.href,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors\", level > 0 && \"ml-4\", isActive(item.href) ? \"bg-blue-100 text-blue-700\" : \"text-gray-700 hover:text-gray-900 hover:bg-gray-100\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"mr-3 h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: item.title\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, _this),\n                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    className: \"ml-2 text-xs\",\n                    children: item.badge\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, item.title, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n            lineNumber: 176,\n            columnNumber: 7\n        }, _this);\n    };\n    // Mobile overlay\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 204,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out md:hidden\", isOpen ? \"translate-x-0\" : \"-translate-x-full\", className),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Menu\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: onClose,\n                                    className: \"h-8 w-8 p-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"p-4 space-y-2 overflow-y-auto h-full pb-20\",\n                            children: navigation.map((item)=>renderNavItem(item))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    }\n    // Desktop Sidebar\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-64 bg-white border-r border-gray-200 min-h-screen\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"p-4 space-y-2\",\n            children: navigation.map((item)=>renderNavItem(item))\n        }, void 0, false, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n            lineNumber: 246,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"9MQ9jhctDLonO1t0WNHWSFwr3bQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_5__.usePermissions\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Sidebar.tsx\n"));

/***/ })

});