"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/Sidebar.tsx":
/*!*******************************************!*\
  !*** ./src/components/layout/Sidebar.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-check.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,ChevronDown,ChevronRight,LayoutDashboard,Radio,RefreshCw,Settings,Trophy,UserCheck,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst navigation = [\n    {\n        title: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        title: \"Fixtures\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        children: [\n            {\n                title: \"All Fixtures\",\n                href: \"/dashboard/fixtures\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n            },\n            {\n                title: \"Live & Upcoming\",\n                href: \"/dashboard/fixtures/live\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n            },\n            {\n                title: \"Sync Data\",\n                href: \"/dashboard/fixtures/sync\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                requiredRole: \"admin\"\n            }\n        ]\n    },\n    {\n        title: \"Leagues\",\n        href: \"/dashboard/leagues\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    },\n    {\n        title: \"Teams\",\n        href: \"/dashboard/teams\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n    },\n    {\n        title: \"Broadcast Links\",\n        href: \"/dashboard/broadcast\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        requiredRole: \"editor\"\n    },\n    {\n        title: \"User Management\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        requiredRole: \"admin\",\n        children: [\n            {\n                title: \"System Users\",\n                href: \"/dashboard/users/system\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n            },\n            {\n                title: \"Registered Users\",\n                href: \"/dashboard/users/registered\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n            },\n            {\n                title: \"Tier Statistics\",\n                href: \"/dashboard/users/tiers\",\n                icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n            }\n        ]\n    },\n    {\n        title: \"Settings\",\n        href: \"/dashboard/settings\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n    },\n    {\n        title: \"API Test\",\n        href: \"/dashboard/api-test\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        requiredRole: \"admin\"\n    },\n    {\n        title: \"Components Demo\",\n        href: \"/dashboard/components-demo\",\n        icon: _barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        requiredRole: \"admin\"\n    }\n];\nconst Sidebar = (param)=>{\n    let { isOpen = true, onClose, className } = param;\n    var _this = undefined;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { hasRole } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_5__.usePermissions)();\n    const [expandedItems, setExpandedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Check if mobile\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checkMobile = ()=>{\n            setIsMobile(window.innerWidth < 768);\n        };\n        checkMobile();\n        window.addEventListener(\"resize\", checkMobile);\n        return ()=>window.removeEventListener(\"resize\", checkMobile);\n    }, []);\n    // Close sidebar on mobile when route changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isMobile && onClose) {\n            onClose();\n        }\n    }, [\n        pathname,\n        isMobile,\n        onClose\n    ]);\n    const toggleExpanded = (title)=>{\n        setExpandedItems((prev)=>prev.includes(title) ? prev.filter((item)=>item !== title) : [\n                ...prev,\n                title\n            ]);\n    };\n    const isActive = (href)=>{\n        return pathname === href || pathname.startsWith(href + \"/\");\n    };\n    const canAccessItem = (item)=>{\n        if (!item.requiredRole) return true;\n        return hasRole(item.requiredRole);\n    };\n    const renderNavItem = function(item) {\n        let level = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0;\n        if (!canAccessItem(item)) return null;\n        const hasChildren = item.children && item.children.length > 0;\n        const isExpanded = expandedItems.includes(item.title);\n        const Icon = item.icon;\n        if (hasChildren) {\n            var _item_children;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>toggleExpanded(item.title),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors\", \"text-gray-700 hover:text-gray-900 hover:bg-gray-100\", level > 0 && \"ml-4\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"mr-3 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, _this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, _this),\n                                    item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2 text-xs\",\n                                        children: item.badge\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, _this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 13\n                            }, _this),\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, _this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, _this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, _this),\n                    isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 space-y-1\",\n                        children: (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>renderNavItem(child, level + 1))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, _this)\n                ]\n            }, item.title, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, _this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            href: item.href,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors\", level > 0 && \"ml-4\", isActive(item.href) ? \"bg-blue-100 text-blue-700\" : \"text-gray-700 hover:text-gray-900 hover:bg-gray-100\"),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"mr-3 h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, _this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    children: item.title\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, _this),\n                item.badge && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    variant: \"secondary\",\n                    className: \"ml-2 text-xs\",\n                    children: item.badge\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 11\n                }, _this)\n            ]\n        }, item.title, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n            lineNumber: 188,\n            columnNumber: 7\n        }, _this);\n    };\n    // Mobile overlay\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\",\n                    onClick: onClose\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out md:hidden\", isOpen ? \"translate-x-0\" : \"-translate-x-full\", className),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-4 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-lg font-semibold text-gray-900\",\n                                    children: \"Menu\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"sm\",\n                                    onClick: onClose,\n                                    className: \"h-8 w-8 p-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_ChevronDown_ChevronRight_LayoutDashboard_Radio_RefreshCw_Settings_Trophy_UserCheck_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                                    lineNumber: 233,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"p-4 space-y-2 overflow-y-auto h-full pb-20\",\n                            children: navigation.map((item)=>renderNavItem(item))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true);\n    }\n    // Desktop Sidebar\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-64 bg-white border-r border-gray-200 min-h-screen\", className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"p-4 space-y-2\",\n            children: navigation.map((item)=>renderNavItem(item))\n        }, void 0, false, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n            lineNumber: 258,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/layout/Sidebar.tsx\",\n        lineNumber: 254,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Sidebar, \"9MQ9jhctDLonO1t0WNHWSFwr3bQ=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_5__.usePermissions\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/Sidebar.tsx\n"));

/***/ })

});