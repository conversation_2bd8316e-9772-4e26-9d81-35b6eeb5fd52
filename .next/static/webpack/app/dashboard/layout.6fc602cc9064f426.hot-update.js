"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/lib/api/auth.ts":
/*!*****************************!*\
  !*** ./src/lib/api/auth.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: function() { return /* binding */ authApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _lib_stores_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stores/auth */ \"(app-pages-browser)/./src/lib/stores/auth.ts\");\n\n\nconst authApi = {\n    // System Authentication\n    login: async (credentials)=>{\n        console.log(\"\\uD83D\\uDD10 Attempting login via proxy...\");\n        try {\n            // Use proxy endpoint instead of direct API call\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify(credentials)\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || \"Login failed\");\n            }\n            const loginData = await response.json();\n            console.log(\"✅ Login successful via proxy\");\n            // Get user profile with the token via proxy\n            const profileResponse = await fetch(\"/api/auth/profile\", {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(loginData.accessToken)\n                }\n            });\n            if (!profileResponse.ok) {\n                const errorData = await profileResponse.json();\n                throw new Error(errorData.message || \"Failed to fetch profile\");\n            }\n            const userProfile = await profileResponse.json();\n            return {\n                user: userProfile,\n                accessToken: loginData.accessToken,\n                refreshToken: loginData.refreshToken\n            };\n        } catch (error) {\n            console.error(\"❌ Login failed via proxy:\", error.message);\n            // Only use mock as absolute fallback for network errors\n            if (error.message.includes(\"fetch\") || error.message.includes(\"network\")) {\n                console.warn(\"⚠️ Network error, using mock data\");\n                if (credentials.username === \"admin\" && credentials.password === \"admin123456\") {\n                    const mockResponse = {\n                        user: {\n                            id: 1,\n                            username: \"admin\",\n                            email: \"<EMAIL>\",\n                            fullName: \"System Administrator\",\n                            role: \"admin\",\n                            isActive: true,\n                            lastLoginAt: new Date().toISOString(),\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        },\n                        accessToken: \"mock-access-token-\" + Date.now(),\n                        refreshToken: \"mock-refresh-token-\" + Date.now()\n                    };\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                    return mockResponse;\n                }\n            }\n            // Re-throw API errors (invalid credentials, etc.)\n            throw error;\n        }\n    },\n    logout: async (refreshToken)=>{\n        const response = await fetch(\"/api/auth/logout\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                refreshToken\n            })\n        });\n        if (!response.ok) {\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Logout failed\");\n        }\n        return await response.json();\n    },\n    logoutFromAllDevices: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/logout-all\");\n        return response;\n    },\n    refreshToken: async (refreshToken)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/refresh\", {\n            refreshToken\n        });\n        return response;\n    },\n    getProfile: async ()=>{\n        const authStore = _lib_stores_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore.getState();\n        const token = authStore.accessToken;\n        const response = await fetch(\"/api/auth/profile\", {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...token && {\n                    \"Authorization\": \"Bearer \".concat(token)\n                }\n            }\n        });\n        if (!response.ok) {\n            // If 401, token might be expired - force logout\n            if (response.status === 401) {\n                console.warn(\"⚠️ Token expired, forcing logout...\");\n                authStore.clearAuth();\n                window.location.href = \"/auth/login\";\n                throw new Error(\"Token expired, please login again\");\n            }\n            const errorData = await response.json();\n            throw new Error(errorData.message || \"Failed to fetch profile\");\n        }\n        return await response.json();\n    },\n    updateProfile: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/system-auth/profile\", data);\n        return response;\n    },\n    changePassword: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/change-password\", data);\n        return response;\n    },\n    // System User Management (Admin only)\n    createUser: async (userData)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/users\", userData);\n        return response;\n    },\n    updateUser: async (id, data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/system-auth/users/\".concat(id), data);\n        return response;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQXFDO0FBRVk7QUFFMUMsTUFBTUUsVUFBVTtJQUNyQix3QkFBd0I7SUFDeEJDLE9BQU8sT0FBT0M7UUFDWkMsUUFBUUMsR0FBRyxDQUFDO1FBRVosSUFBSTtZQUNGLGdEQUFnRDtZQUNoRCxNQUFNQyxXQUFXLE1BQU1DLE1BQU0sbUJBQW1CO2dCQUM5Q0MsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNUO1lBQ3ZCO1lBRUEsSUFBSSxDQUFDRyxTQUFTTyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1DLFlBQVksTUFBTVIsU0FBU1MsSUFBSTtnQkFDckMsTUFBTSxJQUFJQyxNQUFNRixVQUFVRyxPQUFPLElBQUk7WUFDdkM7WUFFQSxNQUFNQyxZQUFZLE1BQU1aLFNBQVNTLElBQUk7WUFDckNYLFFBQVFDLEdBQUcsQ0FBQztZQUVaLDRDQUE0QztZQUM1QyxNQUFNYyxrQkFBa0IsTUFBTVosTUFBTSxxQkFBcUI7Z0JBQ3ZEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtvQkFDaEIsaUJBQWlCLFVBQWdDLE9BQXRCUyxVQUFVRSxXQUFXO2dCQUNsRDtZQUNGO1lBRUEsSUFBSSxDQUFDRCxnQkFBZ0JOLEVBQUUsRUFBRTtnQkFDdkIsTUFBTUMsWUFBWSxNQUFNSyxnQkFBZ0JKLElBQUk7Z0JBQzVDLE1BQU0sSUFBSUMsTUFBTUYsVUFBVUcsT0FBTyxJQUFJO1lBQ3ZDO1lBRUEsTUFBTUksY0FBYyxNQUFNRixnQkFBZ0JKLElBQUk7WUFFOUMsT0FBTztnQkFDTE8sTUFBTUQ7Z0JBQ05ELGFBQWFGLFVBQVVFLFdBQVc7Z0JBQ2xDRyxjQUFjTCxVQUFVSyxZQUFZO1lBQ3RDO1FBQ0YsRUFBRSxPQUFPQyxPQUFZO1lBQ25CcEIsUUFBUW9CLEtBQUssQ0FBQyw2QkFBNkJBLE1BQU1QLE9BQU87WUFFeEQsd0RBQXdEO1lBQ3hELElBQUlPLE1BQU1QLE9BQU8sQ0FBQ1EsUUFBUSxDQUFDLFlBQVlELE1BQU1QLE9BQU8sQ0FBQ1EsUUFBUSxDQUFDLFlBQVk7Z0JBQ3hFckIsUUFBUXNCLElBQUksQ0FBQztnQkFFYixJQUFJdkIsWUFBWXdCLFFBQVEsS0FBSyxXQUFXeEIsWUFBWXlCLFFBQVEsS0FBSyxlQUFlO29CQUM5RSxNQUFNQyxlQUE2Qjt3QkFDakNQLE1BQU07NEJBQ0pRLElBQUk7NEJBQ0pILFVBQVU7NEJBQ1ZJLE9BQU87NEJBQ1BDLFVBQVU7NEJBQ1ZDLE1BQU07NEJBQ05DLFVBQVU7NEJBQ1ZDLGFBQWEsSUFBSUMsT0FBT0MsV0FBVzs0QkFDbkNDLFdBQVcsSUFBSUYsT0FBT0MsV0FBVzs0QkFDakNFLFdBQVcsSUFBSUgsT0FBT0MsV0FBVzt3QkFDbkM7d0JBQ0FqQixhQUFhLHVCQUF1QmdCLEtBQUtJLEdBQUc7d0JBQzVDakIsY0FBYyx3QkFBd0JhLEtBQUtJLEdBQUc7b0JBQ2hEO29CQUVBLE1BQU0sSUFBSUMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztvQkFDakQsT0FBT2I7Z0JBQ1Q7WUFDRjtZQUVBLGtEQUFrRDtZQUNsRCxNQUFNTDtRQUNSO0lBQ0Y7SUFFQW9CLFFBQVEsT0FBT3JCO1FBQ2IsTUFBTWpCLFdBQVcsTUFBTUMsTUFBTSxvQkFBb0I7WUFDL0NDLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7WUFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO2dCQUFFVztZQUFhO1FBQ3RDO1FBRUEsSUFBSSxDQUFDakIsU0FBU08sRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTVIsU0FBU1MsSUFBSTtZQUNyQyxNQUFNLElBQUlDLE1BQU1GLFVBQVVHLE9BQU8sSUFBSTtRQUN2QztRQUVBLE9BQU8sTUFBTVgsU0FBU1MsSUFBSTtJQUM1QjtJQUVBOEIsc0JBQXNCO1FBQ3BCLE1BQU12QyxXQUFXLE1BQU1QLDhDQUFTQSxDQUFDK0MsSUFBSSxDQUFzQjtRQUMzRCxPQUFPeEM7SUFDVDtJQUVBaUIsY0FBYyxPQUFPQTtRQUNuQixNQUFNakIsV0FBVyxNQUFNUCw4Q0FBU0EsQ0FBQytDLElBQUksQ0FBMEIsd0JBQXdCO1lBQ3JGdkI7UUFDRjtRQUNBLE9BQU9qQjtJQUNUO0lBRUF5QyxZQUFZO1FBQ1YsTUFBTUMsWUFBWWhELDBEQUFZQSxDQUFDaUQsUUFBUTtRQUN2QyxNQUFNQyxRQUFRRixVQUFVNUIsV0FBVztRQUVuQyxNQUFNZCxXQUFXLE1BQU1DLE1BQU0scUJBQXFCO1lBQ2hEQyxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixHQUFJeUMsU0FBUztvQkFBRSxpQkFBaUIsVUFBZ0IsT0FBTkE7Z0JBQVEsQ0FBQztZQUNyRDtRQUNGO1FBRUEsSUFBSSxDQUFDNUMsU0FBU08sRUFBRSxFQUFFO1lBQ2hCLGdEQUFnRDtZQUNoRCxJQUFJUCxTQUFTNkMsTUFBTSxLQUFLLEtBQUs7Z0JBQzNCL0MsUUFBUXNCLElBQUksQ0FBQztnQkFDYnNCLFVBQVVJLFNBQVM7Z0JBQ25CQyxPQUFPQyxRQUFRLENBQUNDLElBQUksR0FBRztnQkFDdkIsTUFBTSxJQUFJdkMsTUFBTTtZQUNsQjtZQUVBLE1BQU1GLFlBQVksTUFBTVIsU0FBU1MsSUFBSTtZQUNyQyxNQUFNLElBQUlDLE1BQU1GLFVBQVVHLE9BQU8sSUFBSTtRQUN2QztRQUVBLE9BQU8sTUFBTVgsU0FBU1MsSUFBSTtJQUM1QjtJQUVBeUMsZUFBZSxPQUFPQztRQUNwQixNQUFNbkQsV0FBVyxNQUFNUCw4Q0FBU0EsQ0FBQzJELEdBQUcsQ0FBYSx3QkFBd0JEO1FBQ3pFLE9BQU9uRDtJQUNUO0lBRUFxRCxnQkFBZ0IsT0FBT0Y7UUFLckIsTUFBTW5ELFdBQVcsTUFBTVAsOENBQVNBLENBQUMrQyxJQUFJLENBQXNCLGdDQUFnQ1c7UUFDM0YsT0FBT25EO0lBQ1Q7SUFFQSxzQ0FBc0M7SUFDdENzRCxZQUFZLE9BQU9DO1FBT2pCLE1BQU12RCxXQUFXLE1BQU1QLDhDQUFTQSxDQUFDK0MsSUFBSSxDQUFhLHNCQUFzQmU7UUFDeEUsT0FBT3ZEO0lBQ1Q7SUFFQXdELFlBQVksT0FBT2hDLElBQVkyQjtRQUM3QixNQUFNbkQsV0FBVyxNQUFNUCw4Q0FBU0EsQ0FBQzJELEdBQUcsQ0FBYSxzQkFBeUIsT0FBSDVCLEtBQU0yQjtRQUM3RSxPQUFPbkQ7SUFDVDtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9hcGkvYXV0aC50cz84ODNjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFwaUNsaWVudCB9IGZyb20gJy4vY2xpZW50JztcbmltcG9ydCB7IEF1dGhSZXNwb25zZSwgTG9naW5DcmVkZW50aWFscywgU3lzdGVtVXNlciB9IGZyb20gJ0AvbGliL3R5cGVzL2FwaSc7XG5pbXBvcnQgeyB1c2VBdXRoU3RvcmUgfSBmcm9tICdAL2xpYi9zdG9yZXMvYXV0aCc7XG5cbmV4cG9ydCBjb25zdCBhdXRoQXBpID0ge1xuICAvLyBTeXN0ZW0gQXV0aGVudGljYXRpb25cbiAgbG9naW46IGFzeW5jIChjcmVkZW50aWFsczogTG9naW5DcmVkZW50aWFscyk6IFByb21pc2U8QXV0aFJlc3BvbnNlPiA9PiB7XG4gICAgY29uc29sZS5sb2coJ/CflJAgQXR0ZW1wdGluZyBsb2dpbiB2aWEgcHJveHkuLi4nKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBVc2UgcHJveHkgZW5kcG9pbnQgaW5zdGVhZCBvZiBkaXJlY3QgQVBJIGNhbGxcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9sb2dpbicsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShjcmVkZW50aWFscyksXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCAnTG9naW4gZmFpbGVkJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGxvZ2luRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKCfinIUgTG9naW4gc3VjY2Vzc2Z1bCB2aWEgcHJveHknKTtcblxuICAgICAgLy8gR2V0IHVzZXIgcHJvZmlsZSB3aXRoIHRoZSB0b2tlbiB2aWEgcHJveHlcbiAgICAgIGNvbnN0IHByb2ZpbGVSZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvcHJvZmlsZScsIHtcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAgICAgJ0F1dGhvcml6YXRpb24nOiBgQmVhcmVyICR7bG9naW5EYXRhLmFjY2Vzc1Rva2VufWBcbiAgICAgICAgfVxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcHJvZmlsZVJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHByb2ZpbGVSZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGZldGNoIHByb2ZpbGUnKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgdXNlclByb2ZpbGUgPSBhd2FpdCBwcm9maWxlUmVzcG9uc2UuanNvbigpO1xuXG4gICAgICByZXR1cm4ge1xuICAgICAgICB1c2VyOiB1c2VyUHJvZmlsZSxcbiAgICAgICAgYWNjZXNzVG9rZW46IGxvZ2luRGF0YS5hY2Nlc3NUb2tlbixcbiAgICAgICAgcmVmcmVzaFRva2VuOiBsb2dpbkRhdGEucmVmcmVzaFRva2VuLFxuICAgICAgfTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCfinYwgTG9naW4gZmFpbGVkIHZpYSBwcm94eTonLCBlcnJvci5tZXNzYWdlKTtcblxuICAgICAgLy8gT25seSB1c2UgbW9jayBhcyBhYnNvbHV0ZSBmYWxsYmFjayBmb3IgbmV0d29yayBlcnJvcnNcbiAgICAgIGlmIChlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdmZXRjaCcpIHx8IGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ25ldHdvcmsnKSkge1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBOZXR3b3JrIGVycm9yLCB1c2luZyBtb2NrIGRhdGEnKTtcblxuICAgICAgICBpZiAoY3JlZGVudGlhbHMudXNlcm5hbWUgPT09ICdhZG1pbicgJiYgY3JlZGVudGlhbHMucGFzc3dvcmQgPT09ICdhZG1pbjEyMzQ1NicpIHtcbiAgICAgICAgICBjb25zdCBtb2NrUmVzcG9uc2U6IEF1dGhSZXNwb25zZSA9IHtcbiAgICAgICAgICAgIHVzZXI6IHtcbiAgICAgICAgICAgICAgaWQ6IDEsXG4gICAgICAgICAgICAgIHVzZXJuYW1lOiAnYWRtaW4nLFxuICAgICAgICAgICAgICBlbWFpbDogJ2FkbWluQGxvY2FsaG9zdC5jb20nLFxuICAgICAgICAgICAgICBmdWxsTmFtZTogJ1N5c3RlbSBBZG1pbmlzdHJhdG9yJyxcbiAgICAgICAgICAgICAgcm9sZTogJ2FkbWluJyxcbiAgICAgICAgICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICAgICAgICAgIGxhc3RMb2dpbkF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLFxuICAgICAgICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBhY2Nlc3NUb2tlbjogJ21vY2stYWNjZXNzLXRva2VuLScgKyBEYXRlLm5vdygpLFxuICAgICAgICAgICAgcmVmcmVzaFRva2VuOiAnbW9jay1yZWZyZXNoLXRva2VuLScgKyBEYXRlLm5vdygpLFxuICAgICAgICAgIH07XG5cbiAgICAgICAgICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgNTAwKSk7XG4gICAgICAgICAgcmV0dXJuIG1vY2tSZXNwb25zZTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBSZS10aHJvdyBBUEkgZXJyb3JzIChpbnZhbGlkIGNyZWRlbnRpYWxzLCBldGMuKVxuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9LFxuXG4gIGxvZ291dDogYXN5bmMgKHJlZnJlc2hUb2tlbjogc3RyaW5nKTogUHJvbWlzZTx7IG1lc3NhZ2U6IHN0cmluZyB9PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL2xvZ291dCcsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgcmVmcmVzaFRva2VuIH0pLFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8ICdMb2dvdXQgZmFpbGVkJyk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgfSxcblxuICBsb2dvdXRGcm9tQWxsRGV2aWNlczogYXN5bmMgKCk6IFByb21pc2U8eyBtZXNzYWdlOiBzdHJpbmcgfT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3Q8eyBtZXNzYWdlOiBzdHJpbmcgfT4oJy9zeXN0ZW0tYXV0aC9sb2dvdXQtYWxsJyk7XG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9LFxuXG4gIHJlZnJlc2hUb2tlbjogYXN5bmMgKHJlZnJlc2hUb2tlbjogc3RyaW5nKTogUHJvbWlzZTx7IGFjY2Vzc1Rva2VuOiBzdHJpbmcgfT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnBvc3Q8eyBhY2Nlc3NUb2tlbjogc3RyaW5nIH0+KCcvc3lzdGVtLWF1dGgvcmVmcmVzaCcsIHtcbiAgICAgIHJlZnJlc2hUb2tlbixcbiAgICB9KTtcbiAgICByZXR1cm4gcmVzcG9uc2U7XG4gIH0sXG5cbiAgZ2V0UHJvZmlsZTogYXN5bmMgKCk6IFByb21pc2U8U3lzdGVtVXNlcj4gPT4ge1xuICAgIGNvbnN0IGF1dGhTdG9yZSA9IHVzZUF1dGhTdG9yZS5nZXRTdGF0ZSgpO1xuICAgIGNvbnN0IHRva2VuID0gYXV0aFN0b3JlLmFjY2Vzc1Rva2VuO1xuXG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL3Byb2ZpbGUnLCB7XG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAuLi4odG9rZW4gJiYgeyAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHt0b2tlbn1gIH0pXG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAvLyBJZiA0MDEsIHRva2VuIG1pZ2h0IGJlIGV4cGlyZWQgLSBmb3JjZSBsb2dvdXRcbiAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICBjb25zb2xlLndhcm4oJ+KaoO+4jyBUb2tlbiBleHBpcmVkLCBmb3JjaW5nIGxvZ291dC4uLicpO1xuICAgICAgICBhdXRoU3RvcmUuY2xlYXJBdXRoKCk7XG4gICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9hdXRoL2xvZ2luJztcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdUb2tlbiBleHBpcmVkLCBwbGVhc2UgbG9naW4gYWdhaW4nKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gZmV0Y2ggcHJvZmlsZScpO1xuICAgIH1cblxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gIH0sXG5cbiAgdXBkYXRlUHJvZmlsZTogYXN5bmMgKGRhdGE6IFBhcnRpYWw8U3lzdGVtVXNlcj4pOiBQcm9taXNlPFN5c3RlbVVzZXI+ID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wdXQ8U3lzdGVtVXNlcj4oJy9zeXN0ZW0tYXV0aC9wcm9maWxlJywgZGF0YSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9LFxuXG4gIGNoYW5nZVBhc3N3b3JkOiBhc3luYyAoZGF0YToge1xuICAgIGN1cnJlbnRQYXNzd29yZDogc3RyaW5nO1xuICAgIG5ld1Bhc3N3b3JkOiBzdHJpbmc7XG4gICAgY29uZmlybVBhc3N3b3JkOiBzdHJpbmc7XG4gIH0pOiBQcm9taXNlPHsgbWVzc2FnZTogc3RyaW5nIH0+ID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGFwaUNsaWVudC5wb3N0PHsgbWVzc2FnZTogc3RyaW5nIH0+KCcvc3lzdGVtLWF1dGgvY2hhbmdlLXBhc3N3b3JkJywgZGF0YSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9LFxuXG4gIC8vIFN5c3RlbSBVc2VyIE1hbmFnZW1lbnQgKEFkbWluIG9ubHkpXG4gIGNyZWF0ZVVzZXI6IGFzeW5jICh1c2VyRGF0YToge1xuICAgIHVzZXJuYW1lOiBzdHJpbmc7XG4gICAgZW1haWw6IHN0cmluZztcbiAgICBwYXNzd29yZDogc3RyaW5nO1xuICAgIHJvbGU6ICdhZG1pbicgfCAnZWRpdG9yJyB8ICdtb2RlcmF0b3InO1xuICAgIGZ1bGxOYW1lPzogc3RyaW5nO1xuICB9KTogUHJvbWlzZTxTeXN0ZW1Vc2VyPiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhcGlDbGllbnQucG9zdDxTeXN0ZW1Vc2VyPignL3N5c3RlbS1hdXRoL3VzZXJzJywgdXNlckRhdGEpO1xuICAgIHJldHVybiByZXNwb25zZTtcbiAgfSxcblxuICB1cGRhdGVVc2VyOiBhc3luYyAoaWQ6IG51bWJlciwgZGF0YTogUGFydGlhbDxTeXN0ZW1Vc2VyPik6IFByb21pc2U8U3lzdGVtVXNlcj4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXBpQ2xpZW50LnB1dDxTeXN0ZW1Vc2VyPihgL3N5c3RlbS1hdXRoL3VzZXJzLyR7aWR9YCwgZGF0YSk7XG4gICAgcmV0dXJuIHJlc3BvbnNlO1xuICB9LFxufTtcbiJdLCJuYW1lcyI6WyJhcGlDbGllbnQiLCJ1c2VBdXRoU3RvcmUiLCJhdXRoQXBpIiwibG9naW4iLCJjcmVkZW50aWFscyIsImNvbnNvbGUiLCJsb2ciLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwib2siLCJlcnJvckRhdGEiLCJqc29uIiwiRXJyb3IiLCJtZXNzYWdlIiwibG9naW5EYXRhIiwicHJvZmlsZVJlc3BvbnNlIiwiYWNjZXNzVG9rZW4iLCJ1c2VyUHJvZmlsZSIsInVzZXIiLCJyZWZyZXNoVG9rZW4iLCJlcnJvciIsImluY2x1ZGVzIiwid2FybiIsInVzZXJuYW1lIiwicGFzc3dvcmQiLCJtb2NrUmVzcG9uc2UiLCJpZCIsImVtYWlsIiwiZnVsbE5hbWUiLCJyb2xlIiwiaXNBY3RpdmUiLCJsYXN0TG9naW5BdCIsIkRhdGUiLCJ0b0lTT1N0cmluZyIsImNyZWF0ZWRBdCIsInVwZGF0ZWRBdCIsIm5vdyIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImxvZ291dCIsImxvZ291dEZyb21BbGxEZXZpY2VzIiwicG9zdCIsImdldFByb2ZpbGUiLCJhdXRoU3RvcmUiLCJnZXRTdGF0ZSIsInRva2VuIiwic3RhdHVzIiwiY2xlYXJBdXRoIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwidXBkYXRlUHJvZmlsZSIsImRhdGEiLCJwdXQiLCJjaGFuZ2VQYXNzd29yZCIsImNyZWF0ZVVzZXIiLCJ1c2VyRGF0YSIsInVwZGF0ZVVzZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/auth.ts\n"));

/***/ })

});