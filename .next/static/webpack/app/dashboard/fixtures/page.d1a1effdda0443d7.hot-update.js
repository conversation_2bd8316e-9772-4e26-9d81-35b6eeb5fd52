"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,Plus,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction FixturesPage() {\n    var _data_meta_totalItems, _data_meta, _data_data, _data_data1, _data_data2, _data_meta1;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [limit, setLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leagueFilter, setLeagueFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixture, setSelectedFixture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { isAdmin, isEditor } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQueryClient)();\n    // Fetch fixtures data\n    const { data, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\",\n            page,\n            limit,\n            searchQuery,\n            statusFilter,\n            leagueFilter\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixtures({\n                page,\n                limit,\n                ...searchQuery && {\n                    search: searchQuery\n                },\n                ...statusFilter && {\n                    status: statusFilter\n                },\n                ...leagueFilter && {\n                    league: leagueFilter\n                }\n            }),\n        staleTime: 30000\n    });\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useMutation)({\n        mutationFn: (fixtureId)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.deleteFixture(fixtureId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            console.log(\"Fixture deleted successfully\");\n            setDeleteModalOpen(false);\n            setSelectedFixture(null);\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete fixture:\", error.message);\n        }\n    });\n    // Define table columns\n    const columns = [\n        {\n            key: \"date\",\n            title: \"Date & Time\",\n            sortable: true,\n            render: (value)=>{\n                const date = new Date(value);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium\",\n                            children: date.toLocaleDateString()\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500\",\n                            children: date.toLocaleTimeString([], {\n                                hour: \"2-digit\",\n                                minute: \"2-digit\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"homeTeamName\",\n            title: \"Home Team\",\n            sortable: true,\n            render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"awayTeamName\",\n            title: \"Away Team\",\n            sortable: true,\n            render: (value, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium\",\n                        children: value\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            title: \"Score\",\n            align: \"center\",\n            render: (_, row)=>/*#__PURE__*/ {\n                var _row_goalsHome, _row_goalsAway;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                (_row_goalsHome = row.goalsHome) !== null && _row_goalsHome !== void 0 ? _row_goalsHome : \"-\",\n                                \" - \",\n                                (_row_goalsAway = row.goalsAway) !== null && _row_goalsAway !== void 0 ? _row_goalsAway : \"-\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"HT: \",\n                                row.scoreHalftimeHome,\n                                \" - \",\n                                row.scoreHalftimeAway\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            filterable: true,\n            render: (value, row)=>{\n                const getStatusColor = (status)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                        case \"HT\":\n                            return \"bg-green-100 text-green-800\";\n                        case \"FT\":\n                            return \"bg-gray-100 text-gray-800\";\n                        case \"NS\":\n                            return \"bg-blue-100 text-blue-800\";\n                        case \"CANC\":\n                        case \"PST\":\n                            return \"bg-red-100 text-red-800\";\n                        default:\n                            return \"bg-yellow-100 text-yellow-800\";\n                    }\n                };\n                const getStatusText = (status, elapsed)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                            return \"\".concat(elapsed, \"'\");\n                        case \"HT\":\n                            return \"Half Time\";\n                        case \"FT\":\n                            return \"Full Time\";\n                        case \"NS\":\n                            return \"Not Started\";\n                        case \"CANC\":\n                            return \"Cancelled\";\n                        case \"PST\":\n                            return \"Postponed\";\n                        default:\n                            return status;\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: getStatusColor(value),\n                    children: getStatusText(value, row.elapsed)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"leagueName\",\n            title: \"League\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 182,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit\",\n                            onClick: ()=>handleEditFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete\",\n                            onClick: ()=>handleDeleteFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Handler functions\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n        setPage(1); // Reset to first page when searching\n    };\n    const handleViewFixture = (fixture)=>{\n        // Navigate to fixture detail page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id), \"_blank\");\n    };\n    const handleEditFixture = (fixture)=>{\n        // Navigate to edit page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id, \"/edit\"), \"_blank\");\n    };\n    const handleDeleteFixture = (fixture)=>{\n        setSelectedFixture(fixture);\n        setDeleteModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        if (selectedFixture) {\n            deleteMutation.mutate(selectedFixture.id);\n        }\n    };\n    const handleBulkSync = async ()=>{\n        try {\n            console.log(\"Starting fixtures sync...\");\n            // This would call the sync API\n            // await fixturesApi.syncFixtures();\n            console.log(\"Fixtures sync completed\");\n            refetch();\n        } catch (error) {\n            console.error(\"Sync failed:\", error.message);\n        }\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Fixtures Management\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage football fixtures and match data\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 253,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 259,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 257,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fixtures Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage football fixtures and match data\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkSync,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Export functionality\n                                    console.log(\"Export feature coming soon\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>window.open(\"/dashboard/fixtures/create\", \"_blank\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Fixture\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 274,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_meta = data.meta) === null || _data_meta === void 0 ? void 0 : (_data_meta_totalItems = _data_meta.totalItems) === null || _data_meta_totalItems === void 0 ? void 0 : _data_meta_totalItems.toLocaleString()) || \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.filter((f)=>[\n                                            \"1H\",\n                                            \"2H\",\n                                            \"HT\"\n                                        ].includes(f.status)).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Live Matches\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.filter((f)=>f.status === \"NS\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Upcoming\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.filter((f)=>f.status === \"FT\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_Plus_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"All Fixtures\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Complete list of football fixtures with real-time updates\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                            rows: 10,\n                            columns: 7\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n                            data: (data === null || data === void 0 ? void 0 : data.data) || [],\n                            columns: columns,\n                            loading: isLoading,\n                            searchable: true,\n                            searchPlaceholder: \"Search fixtures...\",\n                            onSearch: handleSearch,\n                            pagination: {\n                                page,\n                                limit,\n                                total: (data === null || data === void 0 ? void 0 : (_data_meta1 = data.meta) === null || _data_meta1 === void 0 ? void 0 : _data_meta1.totalItems) || 0,\n                                onPageChange: setPage,\n                                onLimitChange: setLimit\n                            },\n                            emptyMessage: \"No fixtures found\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 361,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>{\n                    setDeleteModalOpen(false);\n                    setSelectedFixture(null);\n                },\n                onConfirm: confirmDelete,\n                title: \"Delete Fixture\",\n                message: selectedFixture ? 'Are you sure you want to delete the fixture \"'.concat(selectedFixture.homeTeamName, \" vs \").concat(selectedFixture.awayTeamName, '\"? This action cannot be undone.') : \"Are you sure you want to delete this fixture?\",\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                variant: \"destructive\",\n                loading: deleteMutation.isPending\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 272,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesPage, \"iktP6OjuPcgb6yHwMYGIPzr+vdA=\", false, function() {\n    return [\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useMutation\n    ];\n});\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ })

});