"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/date-picker */ \"(app-pages-browser)/./src/components/ui/date-picker.tsx\");\n/* harmony import */ var _components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/date-time-display */ \"(app-pages-browser)/./src/components/ui/date-time-display.tsx\");\n/* harmony import */ var _components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/date-filter-modal */ \"(app-pages-browser)/./src/components/ui/date-filter-modal.tsx\");\n/* harmony import */ var _lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils/date-time */ \"(app-pages-browser)/./src/lib/utils/date-time.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// import { BroadcastLinksModal } from '@/components/fixtures/BroadcastLinksModal';\n\n\n\n\nfunction FixturesPage() {\n    var _data_meta_totalItems, _data_meta, _data_data, _data_data1, _data_data2, _fixturesData_meta;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [limit, setLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Input value\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Actual search query for API\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leagueFilter, setLeagueFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixture, setSelectedFixture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [dateFilterModalOpen, setDateFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAdmin, isEditor } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient)();\n    // Mock data for testing when API is down\n    const mockFixtures = {\n        data: [\n            {\n                id: 1,\n                homeTeamName: \"Manchester United\",\n                awayTeamName: \"Liverpool\",\n                homeTeamLogo: \"/images/teams/1.png\",\n                awayTeamLogo: \"/images/teams/2.png\",\n                date: \"2024-12-19T14:30:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Old Trafford\"\n            },\n            {\n                id: 2,\n                homeTeamName: \"Arsenal\",\n                awayTeamName: \"Chelsea\",\n                homeTeamLogo: \"/images/teams/3.png\",\n                awayTeamLogo: \"/images/teams/4.png\",\n                date: \"2024-12-20T16:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Emirates Stadium\"\n            },\n            {\n                id: 3,\n                homeTeamName: \"Barcelona\",\n                awayTeamName: \"Real Madrid\",\n                homeTeamLogo: \"/images/teams/5.png\",\n                awayTeamLogo: \"/images/teams/6.png\",\n                date: \"2024-12-21T20:00:00Z\",\n                status: \"LIVE\",\n                leagueName: \"La Liga\",\n                venue: \"Camp Nou\"\n            },\n            {\n                id: 4,\n                homeTeamName: \"Bayern Munich\",\n                awayTeamName: \"Borussia Dortmund\",\n                homeTeamLogo: \"/images/teams/7.png\",\n                awayTeamLogo: \"/images/teams/8.png\",\n                date: \"2024-12-18T18:30:00Z\",\n                status: \"FT\",\n                leagueName: \"Bundesliga\",\n                venue: \"Allianz Arena\"\n            },\n            {\n                id: 5,\n                homeTeamName: \"PSG\",\n                awayTeamName: \"Marseille\",\n                homeTeamLogo: \"/images/teams/9.png\",\n                awayTeamLogo: \"/images/teams/10.png\",\n                date: \"2024-12-22T21:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Ligue 1\",\n                venue: \"Parc des Princes\"\n            }\n        ],\n        totalItems: 5,\n        totalPages: 1,\n        currentPage: 1,\n        limit: 25\n    };\n    // Fetch fixtures data with fallback to mock data\n    const { data, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\",\n            page,\n            limit,\n            searchQuery,\n            statusFilter,\n            leagueFilter,\n            selectedDate\n        ],\n        queryFn: ()=>{\n            const filters = {\n                page,\n                limit\n            };\n            // Add search query if provided\n            if (searchQuery && searchQuery.trim()) {\n                // For API, we might need to search by team names or other fields\n                // Since API doesn't have a generic search, we'll filter client-side for now\n                filters.search = searchQuery.trim();\n            }\n            if (statusFilter) filters.status = statusFilter;\n            if (leagueFilter) filters.league = leagueFilter;\n            if (selectedDate) filters.date = (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__.convertLocalDateToUTC)(selectedDate);\n            return _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixtures(filters);\n        },\n        staleTime: 30000,\n        retry: false,\n        onError: (error)=>{\n            console.log(\"API is down, using mock data:\", error.message);\n        }\n    });\n    // Use mock data if API fails or no data\n    const rawData = data || mockFixtures;\n    const isUsingMockData = !data;\n    // Apply client-side filtering for mock data when search is active\n    const fixturesData = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        if (!isUsingMockData || !searchQuery.trim()) {\n            return rawData;\n        }\n        // Filter mock data based on search query\n        const filteredData = rawData.data.filter((fixture)=>{\n            var _fixture_homeTeamName, _fixture_awayTeamName, _fixture_leagueName, _fixture_venue, _fixture_status;\n            const searchLower = searchQuery.toLowerCase();\n            return ((_fixture_homeTeamName = fixture.homeTeamName) === null || _fixture_homeTeamName === void 0 ? void 0 : _fixture_homeTeamName.toLowerCase().includes(searchLower)) || ((_fixture_awayTeamName = fixture.awayTeamName) === null || _fixture_awayTeamName === void 0 ? void 0 : _fixture_awayTeamName.toLowerCase().includes(searchLower)) || ((_fixture_leagueName = fixture.leagueName) === null || _fixture_leagueName === void 0 ? void 0 : _fixture_leagueName.toLowerCase().includes(searchLower)) || ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.toLowerCase().includes(searchLower)) || ((_fixture_status = fixture.status) === null || _fixture_status === void 0 ? void 0 : _fixture_status.toLowerCase().includes(searchLower));\n        });\n        return {\n            ...rawData,\n            data: filteredData,\n            meta: {\n                ...rawData.meta,\n                totalItems: filteredData.length,\n                totalPages: Math.ceil(filteredData.length / limit)\n            },\n            // For backward compatibility with mock structure\n            totalItems: filteredData.length,\n            totalPages: Math.ceil(filteredData.length / limit)\n        };\n    }, [\n        rawData,\n        searchQuery,\n        isUsingMockData,\n        limit\n    ]);\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation)({\n        mutationFn: (fixtureId)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.deleteFixture(fixtureId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            console.log(\"Fixture deleted successfully\");\n            setDeleteModalOpen(false);\n            setSelectedFixture(null);\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete fixture:\", error.message);\n        }\n    });\n    // Define table columns\n    const columns = [\n        {\n            key: \"date\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Date & Time\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-4 w-4 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this),\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__.DateTimeDisplay, {\n                        dateTime: value,\n                        showDate: true,\n                        showTime: true,\n                        isClickable: true,\n                        onClick: ()=>{\n                            const clickedDate = new Date(value);\n                            setSelectedDate(clickedDate);\n                            setDateFilterModalOpen(true);\n                        },\n                        className: \"min-w-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"match\",\n            title: \"Match\",\n            sortable: false,\n            headerClassName: \"text-center\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-4 py-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-2 min-w-[80px]\",\n                            children: [\n                                row.homeTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"\".concat(\"http://172.31.213.61\", \"/\").concat(row.homeTeamLogo),\n                                    alt: row.homeTeamName,\n                                    className: \"w-8 h-8 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-center leading-tight max-w-[80px] break-words\",\n                                    children: row.homeTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 font-bold text-sm\",\n                                children: \"VS\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-2 min-w-[80px]\",\n                            children: [\n                                row.awayTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"\".concat(\"http://172.31.213.61\", \"/\").concat(row.awayTeamLogo),\n                                    alt: row.awayTeamName,\n                                    className: \"w-8 h-8 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-center leading-tight max-w-[80px] break-words\",\n                                    children: row.awayTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            title: \"Score\",\n            align: \"center\",\n            render: (_, row)=>/*#__PURE__*/ {\n                var _row_goalsHome, _row_goalsAway;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                (_row_goalsHome = row.goalsHome) !== null && _row_goalsHome !== void 0 ? _row_goalsHome : \"-\",\n                                \" - \",\n                                (_row_goalsAway = row.goalsAway) !== null && _row_goalsAway !== void 0 ? _row_goalsAway : \"-\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"HT: \",\n                                row.scoreHalftimeHome,\n                                \" - \",\n                                row.scoreHalftimeAway\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            filterable: true,\n            render: (value, row)=>{\n                const getStatusColor = (status)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                        case \"HT\":\n                            return \"bg-green-100 text-green-800\";\n                        case \"FT\":\n                            return \"bg-gray-100 text-gray-800\";\n                        case \"NS\":\n                            return \"bg-blue-100 text-blue-800\";\n                        case \"CANC\":\n                        case \"PST\":\n                            return \"bg-red-100 text-red-800\";\n                        default:\n                            return \"bg-yellow-100 text-yellow-800\";\n                    }\n                };\n                const getStatusText = (status, elapsed)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                            return \"\".concat(elapsed, \"'\");\n                        case \"HT\":\n                            return \"Half Time\";\n                        case \"FT\":\n                            return \"Full Time\";\n                        case \"NS\":\n                            return \"Not Started\";\n                        case \"CANC\":\n                            return \"Cancelled\";\n                        case \"PST\":\n                            return \"Postponed\";\n                        default:\n                            return status;\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: getStatusColor(value),\n                    children: getStatusText(value, row.elapsed)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"leagueName\",\n            title: \"League\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Broadcast Links\",\n                            onClick: ()=>handleBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Broadcast Links\",\n                            onClick: ()=>handleViewBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit\",\n                            onClick: ()=>handleEditFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete\",\n                            onClick: ()=>handleDeleteFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Handler functions\n    const handleSearch = ()=>{\n        setSearchQuery(searchInput.trim());\n        setPage(1); // Reset to first page when searching\n    };\n    const handleSearchKeyPress = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearch();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setSearchInput(\"\");\n        setSearchQuery(\"\");\n        setPage(1);\n    };\n    const handleViewFixture = (fixture)=>{\n        // Navigate to fixture detail page using externalId\n        const fixtureId = fixture.externalId || fixture.id;\n        window.open(\"/dashboard/fixtures/\".concat(fixtureId), \"_blank\");\n    };\n    const handleEditFixture = (fixture)=>{\n        // Navigate to edit page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id, \"/edit\"), \"_blank\");\n    };\n    const handleDeleteFixture = (fixture)=>{\n        setSelectedFixture(fixture);\n        setDeleteModalOpen(true);\n    };\n    const handleBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const handleViewBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        if (selectedFixture) {\n            deleteMutation.mutate(selectedFixture.id);\n        }\n    };\n    const handleBulkSync = async ()=>{\n        try {\n            console.log(\"Starting fixtures sync...\");\n            // This would call the sync API\n            // await fixturesApi.syncFixtures();\n            console.log(\"Fixtures sync completed\");\n            refetch();\n        } catch (error) {\n            console.error(\"Sync failed:\", error.message);\n        }\n    };\n    // Date filter handlers\n    const handleApplyDateFilter = (date)=>{\n        setSelectedDate(date);\n        setPage(1); // Reset to first page when filtering\n    };\n    const handleResetDateFilter = ()=>{\n        setSelectedDate(undefined);\n        setPage(1); // Reset to first page when clearing filter\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Fixtures Management\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage football fixtures and match data\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 465,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 474,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n            lineNumber: 464,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fixtures Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage football fixtures and match data\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 11\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkSync,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 505,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Export functionality\n                                    console.log(\"Export feature coming soon\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 515,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>window.open(\"/dashboard/fixtures/create\", \"_blank\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Fixture\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 494,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_meta = data.meta) === null || _data_meta === void 0 ? void 0 : (_data_meta_totalItems = _data_meta.totalItems) === null || _data_meta_totalItems === void 0 ? void 0 : _data_meta_totalItems.toLocaleString()) || \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 539,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 542,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 538,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 537,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.filter((f)=>[\n                                            \"1H\",\n                                            \"2H\",\n                                            \"HT\"\n                                        ].includes(f.status)).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Live Matches\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 547,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 546,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.filter((f)=>f.status === \"NS\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Upcoming\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 555,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.filter((f)=>f.status === \"FT\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 566,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 565,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 564,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 536,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 580,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"All Fixtures\",\n                                                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-normal\",\n                                                    children: \"Demo Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 579,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: isUsingMockData ? \"Showing demo data - API backend is not available\" : \"Complete list of football fixtures with real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__.DatePicker, {\n                                            date: selectedDate,\n                                            onDateChange: setSelectedDate,\n                                            placeholder: \"Filter by date\",\n                                            className: \"w-[200px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedDate(undefined),\n                                            className: \"px-2\",\n                                            title: \"Clear date filter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 576,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                            rows: 10,\n                            columns: 7\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n                            data: (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.data) || [],\n                            columns: columns,\n                            loading: isLoading && !isUsingMockData,\n                            searchable: true,\n                            searchPlaceholder: \"Search fixtures...\",\n                            showSearchButton: true,\n                            searchValue: searchInput,\n                            onSearchChange: setSearchInput,\n                            onSearchSubmit: handleSearch,\n                            onSearchClear: handleClearSearch,\n                            pagination: {\n                                page,\n                                limit,\n                                total: (fixturesData === null || fixturesData === void 0 ? void 0 : (_fixturesData_meta = fixturesData.meta) === null || _fixturesData_meta === void 0 ? void 0 : _fixturesData_meta.totalItems) || (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.totalItems) || 0,\n                                onPageChange: setPage,\n                                onLimitChange: (newLimit)=>{\n                                    setLimit(newLimit);\n                                    setPage(1); // Reset to first page when changing limit\n                                }\n                            },\n                            emptyMessage: \"No fixtures found\",\n                            error: error && !isUsingMockData ? error : null\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 616,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 575,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>{\n                    setDeleteModalOpen(false);\n                    setSelectedFixture(null);\n                },\n                onConfirm: confirmDelete,\n                title: \"Delete Fixture\",\n                message: selectedFixture ? 'Are you sure you want to delete the fixture \"'.concat(selectedFixture.homeTeamName, \" vs \").concat(selectedFixture.awayTeamName, '\"? This action cannot be undone.') : \"Are you sure you want to delete this fixture?\",\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                variant: \"destructive\",\n                loading: deleteMutation.isPending\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 649,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__.DateFilterModal, {\n                isOpen: dateFilterModalOpen,\n                onClose: ()=>setDateFilterModalOpen(false),\n                selectedDate: selectedDate,\n                onDateSelect: setSelectedDate,\n                onApplyFilter: handleApplyDateFilter,\n                onResetFilter: handleResetDateFilter\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 486,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesPage, \"I3nI8t96W3QSaTr1m7GNDLV7lbA=\", false, function() {\n    return [\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation\n    ];\n});\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ })

});