"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/date-picker */ \"(app-pages-browser)/./src/components/ui/date-picker.tsx\");\n/* harmony import */ var _components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/date-time-display */ \"(app-pages-browser)/./src/components/ui/date-time-display.tsx\");\n/* harmony import */ var _components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/date-filter-modal */ \"(app-pages-browser)/./src/components/ui/date-filter-modal.tsx\");\n/* harmony import */ var _lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils/date-time */ \"(app-pages-browser)/./src/lib/utils/date-time.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// import { BroadcastLinksModal } from '@/components/fixtures/BroadcastLinksModal';\n\n\n\n\nfunction FixturesPage() {\n    var _data_meta_totalItems, _data_meta, _data_data, _data_data1, _data_data2;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [limit, setLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leagueFilter, setLeagueFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixture, setSelectedFixture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [dateFilterModalOpen, setDateFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAdmin, isEditor } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient)();\n    // Mock data for testing when API is down\n    const mockFixtures = {\n        data: [\n            {\n                id: 1,\n                homeTeamName: \"Manchester United\",\n                awayTeamName: \"Liverpool\",\n                homeTeamLogo: \"/images/teams/1.png\",\n                awayTeamLogo: \"/images/teams/2.png\",\n                date: \"2024-12-19T14:30:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Old Trafford\"\n            },\n            {\n                id: 2,\n                homeTeamName: \"Arsenal\",\n                awayTeamName: \"Chelsea\",\n                homeTeamLogo: \"/images/teams/3.png\",\n                awayTeamLogo: \"/images/teams/4.png\",\n                date: \"2024-12-20T16:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Emirates Stadium\"\n            },\n            {\n                id: 3,\n                homeTeamName: \"Barcelona\",\n                awayTeamName: \"Real Madrid\",\n                homeTeamLogo: \"/images/teams/5.png\",\n                awayTeamLogo: \"/images/teams/6.png\",\n                date: \"2024-12-21T20:00:00Z\",\n                status: \"LIVE\",\n                leagueName: \"La Liga\",\n                venue: \"Camp Nou\"\n            },\n            {\n                id: 4,\n                homeTeamName: \"Bayern Munich\",\n                awayTeamName: \"Borussia Dortmund\",\n                homeTeamLogo: \"/images/teams/7.png\",\n                awayTeamLogo: \"/images/teams/8.png\",\n                date: \"2024-12-18T18:30:00Z\",\n                status: \"FT\",\n                leagueName: \"Bundesliga\",\n                venue: \"Allianz Arena\"\n            },\n            {\n                id: 5,\n                homeTeamName: \"PSG\",\n                awayTeamName: \"Marseille\",\n                homeTeamLogo: \"/images/teams/9.png\",\n                awayTeamLogo: \"/images/teams/10.png\",\n                date: \"2024-12-22T21:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Ligue 1\",\n                venue: \"Parc des Princes\"\n            }\n        ],\n        totalItems: 5,\n        totalPages: 1,\n        currentPage: 1,\n        limit: 25\n    };\n    // Fetch fixtures data with fallback to mock data\n    const { data, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\",\n            page,\n            limit,\n            searchQuery,\n            statusFilter,\n            leagueFilter,\n            selectedDate\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixtures({\n                page,\n                limit,\n                ...searchQuery && {\n                    search: searchQuery\n                },\n                ...statusFilter && {\n                    status: statusFilter\n                },\n                ...leagueFilter && {\n                    league: leagueFilter\n                },\n                ...selectedDate && {\n                    date: (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__.convertLocalDateToUTC)(selectedDate)\n                }\n            }),\n        staleTime: 30000,\n        retry: false,\n        onError: (error)=>{\n            console.log(\"API is down, using mock data:\", error.message);\n        }\n    });\n    // Use mock data if API fails or no data\n    const fixturesData = data || mockFixtures;\n    const isUsingMockData = !data;\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation)({\n        mutationFn: (fixtureId)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.deleteFixture(fixtureId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            console.log(\"Fixture deleted successfully\");\n            setDeleteModalOpen(false);\n            setSelectedFixture(null);\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete fixture:\", error.message);\n        }\n    });\n    // Define table columns\n    const columns = [\n        {\n            key: \"date\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Date & Time\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-4 w-4 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this),\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__.DateTimeDisplay, {\n                        dateTime: value,\n                        showDate: true,\n                        showTime: true,\n                        isClickable: true,\n                        onClick: ()=>{\n                            const clickedDate = new Date(value);\n                            setSelectedDate(clickedDate);\n                            setDateFilterModalOpen(true);\n                        },\n                        className: \"min-w-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"match\",\n            title: \"Match\",\n            sortable: false,\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1 justify-end\",\n                            children: [\n                                row.homeTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/api/images/\".concat(row.homeTeamLogo),\n                                    alt: row.homeTeamName,\n                                    className: \"w-6 h-6 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-right\",\n                                    children: row.homeTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 font-bold\",\n                                children: \"vs\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-left\",\n                                    children: row.awayTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this),\n                                row.awayTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"/api/images/\".concat(row.awayTeamLogo),\n                                    alt: row.awayTeamName,\n                                    className: \"w-6 h-6 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            title: \"Score\",\n            align: \"center\",\n            render: (_, row)=>/*#__PURE__*/ {\n                var _row_goalsHome, _row_goalsAway;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                (_row_goalsHome = row.goalsHome) !== null && _row_goalsHome !== void 0 ? _row_goalsHome : \"-\",\n                                \" - \",\n                                (_row_goalsAway = row.goalsAway) !== null && _row_goalsAway !== void 0 ? _row_goalsAway : \"-\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 11\n                        }, this),\n                        row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"HT: \",\n                                row.scoreHalftimeHome,\n                                \" - \",\n                                row.scoreHalftimeAway\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            filterable: true,\n            render: (value, row)=>{\n                const getStatusColor = (status)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                        case \"HT\":\n                            return \"bg-green-100 text-green-800\";\n                        case \"FT\":\n                            return \"bg-gray-100 text-gray-800\";\n                        case \"NS\":\n                            return \"bg-blue-100 text-blue-800\";\n                        case \"CANC\":\n                        case \"PST\":\n                            return \"bg-red-100 text-red-800\";\n                        default:\n                            return \"bg-yellow-100 text-yellow-800\";\n                    }\n                };\n                const getStatusText = (status, elapsed)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                            return \"\".concat(elapsed, \"'\");\n                        case \"HT\":\n                            return \"Half Time\";\n                        case \"FT\":\n                            return \"Full Time\";\n                        case \"NS\":\n                            return \"Not Started\";\n                        case \"CANC\":\n                            return \"Cancelled\";\n                        case \"PST\":\n                            return \"Postponed\";\n                        default:\n                            return status;\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: getStatusColor(value),\n                    children: getStatusText(value, row.elapsed)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"leagueName\",\n            title: \"League\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Broadcast Links\",\n                            onClick: ()=>handleBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Broadcast Links\",\n                            onClick: ()=>handleViewBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 302,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit\",\n                            onClick: ()=>handleEditFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete\",\n                            onClick: ()=>handleDeleteFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Handler functions\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n        setPage(1); // Reset to first page when searching\n    };\n    const handleViewFixture = (fixture)=>{\n        // Navigate to fixture detail page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id), \"_blank\");\n    };\n    const handleEditFixture = (fixture)=>{\n        // Navigate to edit page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id, \"/edit\"), \"_blank\");\n    };\n    const handleDeleteFixture = (fixture)=>{\n        setSelectedFixture(fixture);\n        setDeleteModalOpen(true);\n    };\n    const handleBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const handleViewBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        if (selectedFixture) {\n            deleteMutation.mutate(selectedFixture.id);\n        }\n    };\n    const handleBulkSync = async ()=>{\n        try {\n            console.log(\"Starting fixtures sync...\");\n            // This would call the sync API\n            // await fixturesApi.syncFixtures();\n            console.log(\"Fixtures sync completed\");\n            refetch();\n        } catch (error) {\n            console.error(\"Sync failed:\", error.message);\n        }\n    };\n    // Date filter handlers\n    const handleApplyDateFilter = (date)=>{\n        setSelectedDate(date);\n        setPage(1); // Reset to first page when filtering\n    };\n    const handleResetDateFilter = ()=>{\n        setSelectedDate(undefined);\n        setPage(1); // Reset to first page when clearing filter\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Fixtures Management\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage football fixtures and match data\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 398,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 403,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n            lineNumber: 397,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fixtures Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 423,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage football fixtures and match data\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 424,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 428,\n                                columnNumber: 11\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkSync,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Export functionality\n                                    console.log(\"Export feature coming soon\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>window.open(\"/dashboard/fixtures/create\", \"_blank\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Fixture\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_meta = data.meta) === null || _data_meta === void 0 ? void 0 : (_data_meta_totalItems = _data_meta.totalItems) === null || _data_meta_totalItems === void 0 ? void 0 : _data_meta_totalItems.toLocaleString()) || \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.filter((f)=>[\n                                            \"1H\",\n                                            \"2H\",\n                                            \"HT\"\n                                        ].includes(f.status)).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Live Matches\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 479,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.filter((f)=>f.status === \"NS\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Upcoming\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 493,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.filter((f)=>f.status === \"FT\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 497,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 469,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"All Fixtures\",\n                                                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-normal\",\n                                                    children: \"Demo Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: isUsingMockData ? \"Showing demo data - API backend is not available\" : \"Complete list of football fixtures with real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__.DatePicker, {\n                                            date: selectedDate,\n                                            onDateChange: setSelectedDate,\n                                            placeholder: \"Filter by date\",\n                                            className: \"w-[200px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedDate(undefined),\n                                            className: \"px-2\",\n                                            title: \"Clear date filter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                lineNumber: 543,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 528,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                            rows: 10,\n                            columns: 7\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 551,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n                            data: (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.data) || [],\n                            columns: columns,\n                            loading: isLoading && !isUsingMockData,\n                            searchable: true,\n                            searchPlaceholder: \"Search fixtures...\",\n                            onSearch: handleSearch,\n                            pagination: {\n                                page,\n                                limit,\n                                total: (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.totalItems) || 0,\n                                onPageChange: setPage,\n                                onLimitChange: setLimit\n                            },\n                            emptyMessage: \"No fixtures found\",\n                            error: error && !isUsingMockData ? error : null\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 553,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 549,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>{\n                    setDeleteModalOpen(false);\n                    setSelectedFixture(null);\n                },\n                onConfirm: confirmDelete,\n                title: \"Delete Fixture\",\n                message: selectedFixture ? 'Are you sure you want to delete the fixture \"'.concat(selectedFixture.homeTeamName, \" vs \").concat(selectedFixture.awayTeamName, '\"? This action cannot be undone.') : \"Are you sure you want to delete this fixture?\",\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                variant: \"destructive\",\n                loading: deleteMutation.isPending\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 575,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__.DateFilterModal, {\n                isOpen: dateFilterModalOpen,\n                onClose: ()=>setDateFilterModalOpen(false),\n                selectedDate: selectedDate,\n                onDateSelect: setSelectedDate,\n                onApplyFilter: handleApplyDateFilter,\n                onResetFilter: handleResetDateFilter\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 607,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 419,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesPage, \"HEn9/pn95++An6EaTwW27XWX8nk=\", false, function() {\n    return [\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation\n    ];\n});\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ })

});