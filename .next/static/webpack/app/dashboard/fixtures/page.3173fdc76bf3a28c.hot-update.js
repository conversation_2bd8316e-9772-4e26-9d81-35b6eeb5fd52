"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/date-picker */ \"(app-pages-browser)/./src/components/ui/date-picker.tsx\");\n/* harmony import */ var _components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/date-time-display */ \"(app-pages-browser)/./src/components/ui/date-time-display.tsx\");\n/* harmony import */ var _components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/date-filter-modal */ \"(app-pages-browser)/./src/components/ui/date-filter-modal.tsx\");\n/* harmony import */ var _lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils/date-time */ \"(app-pages-browser)/./src/lib/utils/date-time.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// import { BroadcastLinksModal } from '@/components/fixtures/BroadcastLinksModal';\n\n\n\n\nfunction FixturesPage() {\n    var _data_meta_totalItems, _data_meta, _data_data, _data_data1, _data_data2;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [limit, setLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leagueFilter, setLeagueFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixture, setSelectedFixture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [dateFilterModalOpen, setDateFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAdmin, isEditor } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient)();\n    // Mock data for testing when API is down\n    const mockFixtures = {\n        data: [\n            {\n                id: 1,\n                homeTeamName: \"Manchester United\",\n                awayTeamName: \"Liverpool\",\n                homeTeamLogo: \"/images/teams/1.png\",\n                awayTeamLogo: \"/images/teams/2.png\",\n                date: \"2024-12-19T14:30:00Z\",\n                status: \"scheduled\",\n                leagueName: \"Premier League\",\n                venue: \"Old Trafford\"\n            },\n            {\n                id: 2,\n                homeTeamName: \"Arsenal\",\n                awayTeamName: \"Chelsea\",\n                homeTeamLogo: \"/images/teams/3.png\",\n                awayTeamLogo: \"/images/teams/4.png\",\n                date: \"2024-12-20T16:00:00Z\",\n                status: \"scheduled\",\n                leagueName: \"Premier League\",\n                venue: \"Emirates Stadium\"\n            },\n            {\n                id: 3,\n                homeTeamName: \"Barcelona\",\n                awayTeamName: \"Real Madrid\",\n                homeTeamLogo: \"/images/teams/5.png\",\n                awayTeamLogo: \"/images/teams/6.png\",\n                date: \"2024-12-21T20:00:00Z\",\n                status: \"scheduled\",\n                leagueName: \"La Liga\",\n                venue: \"Camp Nou\"\n            }\n        ],\n        totalItems: 3,\n        totalPages: 1,\n        currentPage: 1,\n        limit: 25\n    };\n    // Fetch fixtures data with fallback to mock data\n    const { data, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\",\n            page,\n            limit,\n            searchQuery,\n            statusFilter,\n            leagueFilter,\n            selectedDate\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixtures({\n                page,\n                limit,\n                ...searchQuery && {\n                    search: searchQuery\n                },\n                ...statusFilter && {\n                    status: statusFilter\n                },\n                ...leagueFilter && {\n                    league: leagueFilter\n                },\n                ...selectedDate && {\n                    date: (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__.convertLocalDateToUTC)(selectedDate)\n                }\n            }),\n        staleTime: 30000,\n        retry: false,\n        onError: (error)=>{\n            console.log(\"API is down, using mock data:\", error.message);\n        }\n    });\n    // Use mock data if API fails or no data\n    const fixturesData = data || mockFixtures;\n    const isUsingMockData = !data;\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation)({\n        mutationFn: (fixtureId)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.deleteFixture(fixtureId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            console.log(\"Fixture deleted successfully\");\n            setDeleteModalOpen(false);\n            setSelectedFixture(null);\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete fixture:\", error.message);\n        }\n    });\n    // Define table columns\n    const columns = [\n        {\n            key: \"date\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Date & Time\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-4 w-4 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, this),\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__.DateTimeDisplay, {\n                        dateTime: value,\n                        showDate: true,\n                        showTime: true,\n                        isClickable: true,\n                        onClick: ()=>{\n                            const clickedDate = new Date(value);\n                            setSelectedDate(clickedDate);\n                            setDateFilterModalOpen(true);\n                        },\n                        className: \"min-w-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"match\",\n            title: \"Match\",\n            sortable: false,\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1 justify-end\",\n                            children: [\n                                row.homeTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: row.homeTeamLogo.replace(\"public/\", \"/api/images/\"),\n                                    alt: row.homeTeamName,\n                                    className: \"w-6 h-6 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-right\",\n                                    children: row.homeTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 font-bold\",\n                                children: \"vs\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-left\",\n                                    children: row.awayTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                row.awayTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: row.awayTeamLogo.replace(\"public/\", \"/api/images/\"),\n                                    alt: row.awayTeamName,\n                                    className: \"w-6 h-6 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            title: \"Score\",\n            align: \"center\",\n            render: (_, row)=>/*#__PURE__*/ {\n                var _row_goalsHome, _row_goalsAway;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                (_row_goalsHome = row.goalsHome) !== null && _row_goalsHome !== void 0 ? _row_goalsHome : \"-\",\n                                \" - \",\n                                (_row_goalsAway = row.goalsAway) !== null && _row_goalsAway !== void 0 ? _row_goalsAway : \"-\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, this),\n                        row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"HT: \",\n                                row.scoreHalftimeHome,\n                                \" - \",\n                                row.scoreHalftimeAway\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            filterable: true,\n            render: (value, row)=>{\n                const getStatusColor = (status)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                        case \"HT\":\n                            return \"bg-green-100 text-green-800\";\n                        case \"FT\":\n                            return \"bg-gray-100 text-gray-800\";\n                        case \"NS\":\n                            return \"bg-blue-100 text-blue-800\";\n                        case \"CANC\":\n                        case \"PST\":\n                            return \"bg-red-100 text-red-800\";\n                        default:\n                            return \"bg-yellow-100 text-yellow-800\";\n                    }\n                };\n                const getStatusText = (status, elapsed)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                            return \"\".concat(elapsed, \"'\");\n                        case \"HT\":\n                            return \"Half Time\";\n                        case \"FT\":\n                            return \"Full Time\";\n                        case \"NS\":\n                            return \"Not Started\";\n                        case \"CANC\":\n                            return \"Cancelled\";\n                        case \"PST\":\n                            return \"Postponed\";\n                        default:\n                            return status;\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: getStatusColor(value),\n                    children: getStatusText(value, row.elapsed)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"leagueName\",\n            title: \"League\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 256,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Broadcast Links\",\n                            onClick: ()=>handleBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Broadcast Links\",\n                            onClick: ()=>handleViewBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit\",\n                            onClick: ()=>handleEditFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete\",\n                            onClick: ()=>handleDeleteFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Handler functions\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n        setPage(1); // Reset to first page when searching\n    };\n    const handleViewFixture = (fixture)=>{\n        // Navigate to fixture detail page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id), \"_blank\");\n    };\n    const handleEditFixture = (fixture)=>{\n        // Navigate to edit page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id, \"/edit\"), \"_blank\");\n    };\n    const handleDeleteFixture = (fixture)=>{\n        setSelectedFixture(fixture);\n        setDeleteModalOpen(true);\n    };\n    const handleBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const handleViewBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        if (selectedFixture) {\n            deleteMutation.mutate(selectedFixture.id);\n        }\n    };\n    const handleBulkSync = async ()=>{\n        try {\n            console.log(\"Starting fixtures sync...\");\n            // This would call the sync API\n            // await fixturesApi.syncFixtures();\n            console.log(\"Fixtures sync completed\");\n            refetch();\n        } catch (error) {\n            console.error(\"Sync failed:\", error.message);\n        }\n    };\n    // Date filter handlers\n    const handleApplyDateFilter = (date)=>{\n        setSelectedDate(date);\n        setPage(1); // Reset to first page when filtering\n    };\n    const handleResetDateFilter = ()=>{\n        setSelectedDate(undefined);\n        setPage(1); // Reset to first page when clearing filter\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Fixtures Management\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage football fixtures and match data\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n            lineNumber: 375,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fixtures Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage football fixtures and match data\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 400,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkSync,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Export functionality\n                                    console.log(\"Export feature coming soon\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>window.open(\"/dashboard/fixtures/create\", \"_blank\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Fixture\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 399,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_meta = data.meta) === null || _data_meta === void 0 ? void 0 : (_data_meta_totalItems = _data_meta.totalItems) === null || _data_meta_totalItems === void 0 ? void 0 : _data_meta_totalItems.toLocaleString()) || \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 449,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.filter((f)=>[\n                                            \"1H\",\n                                            \"2H\",\n                                            \"HT\"\n                                        ].includes(f.status)).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Live Matches\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.filter((f)=>f.status === \"NS\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Upcoming\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.filter((f)=>f.status === \"FT\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 476,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 447,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"All Fixtures\",\n                                                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-normal\",\n                                                    children: \"Demo Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: isUsingMockData ? \"Showing demo data - API backend is not available\" : \"Complete list of football fixtures with real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 489,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__.DatePicker, {\n                                            date: selectedDate,\n                                            onDateChange: setSelectedDate,\n                                            placeholder: \"Filter by date\",\n                                            className: \"w-[200px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedDate(undefined),\n                                            className: \"px-2\",\n                                            title: \"Clear date filter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 514,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                            rows: 10,\n                            columns: 7\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 529,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n                            data: (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.data) || [],\n                            columns: columns,\n                            loading: isLoading && !isUsingMockData,\n                            searchable: true,\n                            searchPlaceholder: \"Search fixtures...\",\n                            onSearch: handleSearch,\n                            pagination: {\n                                page,\n                                limit,\n                                total: (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.totalItems) || 0,\n                                onPageChange: setPage,\n                                onLimitChange: setLimit\n                            },\n                            emptyMessage: \"No fixtures found\",\n                            error: error && !isUsingMockData ? error : null\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 527,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 486,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>{\n                    setDeleteModalOpen(false);\n                    setSelectedFixture(null);\n                },\n                onConfirm: confirmDelete,\n                title: \"Delete Fixture\",\n                message: selectedFixture ? 'Are you sure you want to delete the fixture \"'.concat(selectedFixture.homeTeamName, \" vs \").concat(selectedFixture.awayTeamName, '\"? This action cannot be undone.') : \"Are you sure you want to delete this fixture?\",\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                variant: \"destructive\",\n                loading: deleteMutation.isPending\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 553,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__.DateFilterModal, {\n                isOpen: dateFilterModalOpen,\n                onClose: ()=>setDateFilterModalOpen(false),\n                selectedDate: selectedDate,\n                onDateSelect: setSelectedDate,\n                onApplyFilter: handleApplyDateFilter,\n                onResetFilter: handleResetDateFilter\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 585,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 397,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesPage, \"HEn9/pn95++An6EaTwW27XWX8nk=\", false, function() {\n    return [\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation\n    ];\n});\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ })

});