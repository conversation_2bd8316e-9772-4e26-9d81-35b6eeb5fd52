"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/components/ui/date-time-display.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/date-time-display.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DateTimeDisplay: function() { return /* binding */ DateTimeDisplay; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _lib_utils_date_time__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils/date-time */ \"(app-pages-browser)/./src/lib/utils/date-time.ts\");\n/* __next_internal_client_entry_do_not_use__ DateTimeDisplay auto */ \n\n\n\nconst DateTimeDisplay = (param)=>{\n    let { dateTime, className = \"\", showDate = true, showTime = true, format } = param;\n    const localDate = (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_3__.formatToLocalTime)(dateTime, \"dd/MM/yyyy\");\n    const localTime = (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_3__.formatToLocalTime)(dateTime, \"HH:mm\");\n    const utcDate = (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_3__.formatToUTC)(dateTime, \"dd/MM/yyyy\");\n    const utcTime = (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_3__.formatToUTC)(dateTime, \"HH:mm\");\n    const timezoneInfo = (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_3__.getTimezoneDisplayName)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.Tooltip, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"cursor-help \".concat(className),\n                        children: [\n                            showDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium text-sm\",\n                                children: localDate\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 15\n                            }, undefined),\n                            showTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-600 text-sm\",\n                                children: localTime\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_2__.TooltipContent, {\n                    side: \"top\",\n                    className: \"max-w-xs\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-medium\",\n                                children: \"UTC Time:\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm\",\n                                children: [\n                                    utcDate,\n                                    \" \",\n                                    utcTime,\n                                    \" (GMT+0)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-gray-400 mt-2\",\n                                children: [\n                                    \"Local: \",\n                                    timezoneInfo\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/ui/date-time-display.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\n_c = DateTimeDisplay;\nvar _c;\n$RefreshReg$(_c, \"DateTimeDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/date-time-display.tsx\n"));

/***/ })

});