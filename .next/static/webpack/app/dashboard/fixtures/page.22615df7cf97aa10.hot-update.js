"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/date-picker */ \"(app-pages-browser)/./src/components/ui/date-picker.tsx\");\n/* harmony import */ var _components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/date-time-display */ \"(app-pages-browser)/./src/components/ui/date-time-display.tsx\");\n/* harmony import */ var _lib_utils_date_time__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils/date-time */ \"(app-pages-browser)/./src/lib/utils/date-time.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// import { BroadcastLinksModal } from '@/components/fixtures/BroadcastLinksModal';\n\n\n\nfunction FixturesPage() {\n    var _data_meta_totalItems, _data_meta, _data_data, _data_data1, _data_data2, _data_meta1;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [limit, setLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leagueFilter, setLeagueFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixture, setSelectedFixture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const { isAdmin, isEditor } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient)();\n    // Mock data for testing when API is down\n    const mockFixtures = {\n        data: [\n            {\n                id: 1,\n                homeTeamName: \"Manchester United\",\n                awayTeamName: \"Liverpool\",\n                homeTeamLogo: \"/images/teams/1.png\",\n                awayTeamLogo: \"/images/teams/2.png\",\n                date: \"2024-12-19T14:30:00Z\",\n                status: \"scheduled\",\n                leagueName: \"Premier League\",\n                venue: \"Old Trafford\"\n            },\n            {\n                id: 2,\n                homeTeamName: \"Arsenal\",\n                awayTeamName: \"Chelsea\",\n                homeTeamLogo: \"/images/teams/3.png\",\n                awayTeamLogo: \"/images/teams/4.png\",\n                date: \"2024-12-20T16:00:00Z\",\n                status: \"scheduled\",\n                leagueName: \"Premier League\",\n                venue: \"Emirates Stadium\"\n            },\n            {\n                id: 3,\n                homeTeamName: \"Barcelona\",\n                awayTeamName: \"Real Madrid\",\n                homeTeamLogo: \"/images/teams/5.png\",\n                awayTeamLogo: \"/images/teams/6.png\",\n                date: \"2024-12-21T20:00:00Z\",\n                status: \"scheduled\",\n                leagueName: \"La Liga\",\n                venue: \"Camp Nou\"\n            }\n        ],\n        totalItems: 3,\n        totalPages: 1,\n        currentPage: 1,\n        limit: 25\n    };\n    // Fetch fixtures data\n    const { data, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\",\n            page,\n            limit,\n            searchQuery,\n            statusFilter,\n            leagueFilter,\n            selectedDate\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixtures({\n                page,\n                limit,\n                ...searchQuery && {\n                    search: searchQuery\n                },\n                ...statusFilter && {\n                    status: statusFilter\n                },\n                ...leagueFilter && {\n                    league: leagueFilter\n                },\n                ...selectedDate && {\n                    date: (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_12__.convertLocalDateToUTC)(selectedDate)\n                }\n            }),\n        staleTime: 30000,\n        retry: false,\n        onError: ()=>{\n            console.log(\"API is down, using mock data\");\n        }\n    });\n    // Use mock data if API fails\n    const fixturesData = data || mockFixtures;\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useMutation)({\n        mutationFn: (fixtureId)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.deleteFixture(fixtureId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            console.log(\"Fixture deleted successfully\");\n            setDeleteModalOpen(false);\n            setSelectedFixture(null);\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete fixture:\", error.message);\n        }\n    });\n    // Define table columns\n    const columns = [\n        {\n            key: \"date\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Date & Time\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        className: \"h-4 w-4 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 118,\n                columnNumber: 9\n            }, this),\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__.DateTimeDisplay, {\n                        dateTime: value,\n                        showDate: true,\n                        showTime: true,\n                        className: \"\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"match\",\n            title: \"Match\",\n            sortable: false,\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1 justify-end\",\n                            children: [\n                                row.homeTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: row.homeTeamLogo.replace(\"public/\", \"/api/images/\"),\n                                    alt: row.homeTeamName,\n                                    className: \"w-6 h-6 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-right\",\n                                    children: row.homeTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 font-bold\",\n                                children: \"vs\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-left\",\n                                    children: row.awayTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this),\n                                row.awayTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: row.awayTeamLogo.replace(\"public/\", \"/api/images/\"),\n                                    alt: row.awayTeamName,\n                                    className: \"w-6 h-6 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            title: \"Score\",\n            align: \"center\",\n            render: (_, row)=>/*#__PURE__*/ {\n                var _row_goalsHome, _row_goalsAway;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                (_row_goalsHome = row.goalsHome) !== null && _row_goalsHome !== void 0 ? _row_goalsHome : \"-\",\n                                \" - \",\n                                (_row_goalsAway = row.goalsAway) !== null && _row_goalsAway !== void 0 ? _row_goalsAway : \"-\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this),\n                        row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"HT: \",\n                                row.scoreHalftimeHome,\n                                \" - \",\n                                row.scoreHalftimeAway\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            filterable: true,\n            render: (value, row)=>{\n                const getStatusColor = (status)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                        case \"HT\":\n                            return \"bg-green-100 text-green-800\";\n                        case \"FT\":\n                            return \"bg-gray-100 text-gray-800\";\n                        case \"NS\":\n                            return \"bg-blue-100 text-blue-800\";\n                        case \"CANC\":\n                        case \"PST\":\n                            return \"bg-red-100 text-red-800\";\n                        default:\n                            return \"bg-yellow-100 text-yellow-800\";\n                    }\n                };\n                const getStatusText = (status, elapsed)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                            return \"\".concat(elapsed, \"'\");\n                        case \"HT\":\n                            return \"Half Time\";\n                        case \"FT\":\n                            return \"Full Time\";\n                        case \"NS\":\n                            return \"Not Started\";\n                        case \"CANC\":\n                            return \"Cancelled\";\n                        case \"PST\":\n                            return \"Postponed\";\n                        default:\n                            return status;\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: getStatusColor(value),\n                    children: getStatusText(value, row.elapsed)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"leagueName\",\n            title: \"League\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Broadcast Links\",\n                            onClick: ()=>handleBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Broadcast Links\",\n                            onClick: ()=>handleViewBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit\",\n                            onClick: ()=>handleEditFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete\",\n                            onClick: ()=>handleDeleteFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Handler functions\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n        setPage(1); // Reset to first page when searching\n    };\n    const handleViewFixture = (fixture)=>{\n        // Navigate to fixture detail page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id), \"_blank\");\n    };\n    const handleEditFixture = (fixture)=>{\n        // Navigate to edit page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id, \"/edit\"), \"_blank\");\n    };\n    const handleDeleteFixture = (fixture)=>{\n        setSelectedFixture(fixture);\n        setDeleteModalOpen(true);\n    };\n    const handleBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const handleViewBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        if (selectedFixture) {\n            deleteMutation.mutate(selectedFixture.id);\n        }\n    };\n    const handleBulkSync = async ()=>{\n        try {\n            console.log(\"Starting fixtures sync...\");\n            // This would call the sync API\n            // await fixturesApi.syncFixtures();\n            console.log(\"Fixtures sync completed\");\n            refetch();\n        } catch (error) {\n            console.error(\"Sync failed:\", error.message);\n        }\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Fixtures Management\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage football fixtures and match data\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 363,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 362,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n            lineNumber: 355,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fixtures Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage football fixtures and match data\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 386,\n                                columnNumber: 11\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkSync,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 401,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Export functionality\n                                    console.log(\"Export feature coming soon\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 406,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>window.open(\"/dashboard/fixtures/create\", \"_blank\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Fixture\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 379,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_meta = data.meta) === null || _data_meta === void 0 ? void 0 : (_data_meta_totalItems = _data_meta.totalItems) === null || _data_meta_totalItems === void 0 ? void 0 : _data_meta_totalItems.toLocaleString()) || \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 430,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 433,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.filter((f)=>[\n                                            \"1H\",\n                                            \"2H\",\n                                            \"HT\"\n                                        ].includes(f.status)).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Live Matches\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 437,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.filter((f)=>f.status === \"NS\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Upcoming\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 451,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.filter((f)=>f.status === \"FT\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"All Fixtures\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: \"Complete list of football fixtures with real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__.DatePicker, {\n                                            date: selectedDate,\n                                            onDateChange: setSelectedDate,\n                                            placeholder: \"Filter by date\",\n                                            className: \"w-[200px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedDate(undefined),\n                                            className: \"px-2\",\n                                            title: \"Clear date filter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 486,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 478,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 467,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                            rows: 10,\n                            columns: 7\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 501,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n                            data: (data === null || data === void 0 ? void 0 : data.data) || [],\n                            columns: columns,\n                            loading: isLoading,\n                            searchable: true,\n                            searchPlaceholder: \"Search fixtures...\",\n                            onSearch: handleSearch,\n                            pagination: {\n                                page,\n                                limit,\n                                total: (data === null || data === void 0 ? void 0 : (_data_meta1 = data.meta) === null || _data_meta1 === void 0 ? void 0 : _data_meta1.totalItems) || 0,\n                                onPageChange: setPage,\n                                onLimitChange: setLimit\n                            },\n                            emptyMessage: \"No fixtures found\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 499,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 466,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>{\n                    setDeleteModalOpen(false);\n                    setSelectedFixture(null);\n                },\n                onConfirm: confirmDelete,\n                title: \"Delete Fixture\",\n                message: selectedFixture ? 'Are you sure you want to delete the fixture \"'.concat(selectedFixture.homeTeamName, \" vs \").concat(selectedFixture.awayTeamName, '\"? This action cannot be undone.') : \"Are you sure you want to delete this fixture?\",\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                variant: \"destructive\",\n                loading: deleteMutation.isPending\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 377,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesPage, \"GaxB8wZtBP/7aiHDBu+iIfVqzu4=\", false, function() {\n    return [\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useMutation\n    ];\n});\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ })

});