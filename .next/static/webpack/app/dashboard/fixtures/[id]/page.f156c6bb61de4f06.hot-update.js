"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/page",{

/***/ "(app-pages-browser)/./src/components/fixtures/BroadcastLinksModal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/fixtures/BroadcastLinksModal.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BroadcastLinksModal: function() { return /* binding */ BroadcastLinksModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ BroadcastLinksModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst BroadcastLinksModal = (param)=>{\n    let { isOpen, onClose, fixture } = param;\n    _s();\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingLink, setEditingLink] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        url: \"\",\n        language: \"English\",\n        quality: \"HD\"\n    });\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQueryClient)();\n    // Fetch broadcast links for this fixture\n    const { data: linksData, isLoading, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.useQuery)({\n        queryKey: [\n            \"broadcast-links\",\n            fixture.externalId || fixture.id\n        ],\n        queryFn: ()=>broadcastLinksApi.getBroadcastLinks({\n                fixtureId: fixture.externalId || fixture.id\n            }),\n        enabled: isOpen\n    });\n    const links = (linksData === null || linksData === void 0 ? void 0 : linksData.data) || [];\n    // Create mutation\n    const createMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useMutation)({\n        mutationFn: (data)=>broadcastLinksApi.createBroadcastLink(data),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"broadcast-links\"\n                ]\n            });\n            setShowAddForm(false);\n            resetForm();\n        },\n        onError: (error)=>{\n            console.error(\"Failed to create broadcast link:\", error.message);\n        }\n    });\n    // Update mutation\n    const updateMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useMutation)({\n        mutationFn: (param)=>{\n            let { id, data } = param;\n            return broadcastLinksApi.updateBroadcastLink(id, data);\n        },\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"broadcast-links\"\n                ]\n            });\n            setEditingLink(null);\n            resetForm();\n        },\n        onError: (error)=>{\n            console.error(\"Failed to update broadcast link:\", error.message);\n        }\n    });\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useMutation)({\n        mutationFn: (id)=>broadcastLinksApi.deleteBroadcastLink(id),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"broadcast-links\"\n                ]\n            });\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete broadcast link:\", error.message);\n        }\n    });\n    const resetForm = ()=>{\n        setFormData({\n            title: \"\",\n            url: \"\",\n            language: \"English\",\n            quality: \"HD\"\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!formData.title.trim() || !formData.url.trim()) {\n            return;\n        }\n        const submitData = {\n            fixtureId: fixture.externalId || fixture.id,\n            title: formData.title.trim(),\n            url: formData.url.trim(),\n            language: formData.language,\n            quality: formData.quality,\n            isActive: true\n        };\n        if (editingLink) {\n            updateMutation.mutate({\n                id: editingLink.id,\n                data: submitData\n            });\n        } else {\n            createMutation.mutate(submitData);\n        }\n    };\n    const handleEdit = (link)=>{\n        setEditingLink(link);\n        setFormData({\n            title: link.title,\n            url: link.url,\n            language: link.language || \"English\",\n            quality: link.quality || \"HD\"\n        });\n        setShowAddForm(true);\n    };\n    const handleDelete = (link)=>{\n        if (confirm('Are you sure you want to delete \"'.concat(link.title, '\"?'))) {\n            deleteMutation.mutate(link.id);\n        }\n    };\n    const handleCancel = ()=>{\n        setShowAddForm(false);\n        setEditingLink(null);\n        resetForm();\n    };\n    const getQualityColor = (quality)=>{\n        switch(quality.toLowerCase()){\n            case \"4k\":\n            case \"uhd\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"hd\":\n            case \"1080p\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"sd\":\n            case \"720p\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getLanguageFlag = (language)=>{\n        const flags = {\n            en: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            es: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n            fr: \"\\uD83C\\uDDEB\\uD83C\\uDDF7\",\n            de: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            it: \"\\uD83C\\uDDEE\\uD83C\\uDDF9\",\n            pt: \"\\uD83C\\uDDF5\\uD83C\\uDDF9\",\n            ar: \"\\uD83C\\uDDF8\\uD83C\\uDDE6\"\n        };\n        return flags[language] || \"\\uD83C\\uDF10\";\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 49\n                                            }, undefined),\n                                            \"Broadcast Links - \",\n                                            fixture.homeTeamName,\n                                            \" vs \",\n                                            fixture.awayTeamName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Manage streaming links for this fixture\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 43\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 37\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 43\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 37\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 31\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        !showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        links.length,\n                                        \" broadcast link\",\n                                        links.length !== 1 ? \"s\" : \"\",\n                                        \" available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setShowAddForm(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        \"Add Link\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 37\n                        }, undefined),\n                        showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: editingLink ? \"Edit Broadcast Link\" : \"Add New Broadcast Link\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 217,\n                                                columnNumber: 55\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleCancel,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 55\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 49\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"title\",\n                                                                children: \"Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"title\",\n                                                                value: formData.title,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        title: e.target.value\n                                                                    }),\n                                                                placeholder: \"e.g., ESPN HD Stream\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"url\",\n                                                                children: \"URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 239,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"url\",\n                                                                type: \"url\",\n                                                                value: formData.url,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        url: e.target.value\n                                                                    }),\n                                                                placeholder: \"https://...\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"language\",\n                                                                children: \"Language\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"language\",\n                                                                value: formData.language,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        language: e.target.value\n                                                                    }),\n                                                                className: \"w-full p-2 border rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"en\",\n                                                                        children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 English\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"es\",\n                                                                        children: \"\\uD83C\\uDDEA\\uD83C\\uDDF8 Spanish\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 258,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"fr\",\n                                                                        children: \"\\uD83C\\uDDEB\\uD83C\\uDDF7 French\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"de\",\n                                                                        children: \"\\uD83C\\uDDE9\\uD83C\\uDDEA German\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 260,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"it\",\n                                                                        children: \"\\uD83C\\uDDEE\\uD83C\\uDDF9 Italian\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 261,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"pt\",\n                                                                        children: \"\\uD83C\\uDDF5\\uD83C\\uDDF9 Portuguese\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ar\",\n                                                                        children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6 Arabic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 73\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"quality\",\n                                                                children: \"Quality\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"quality\",\n                                                                value: formData.quality,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        quality: e.target.value\n                                                                    }),\n                                                                className: \"w-full p-2 border rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"4K\",\n                                                                        children: \"4K Ultra HD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 274,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"HD\",\n                                                                        children: \"HD (1080p)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 275,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"720p\",\n                                                                        children: \"HD (720p)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SD\",\n                                                                        children: \"SD (480p)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 73\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 61\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 55\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end space-x-2 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        onClick: handleCancel,\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: createMutation.isLoading || updateMutation.isLoading,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            editingLink ? \"Update\" : \"Add\",\n                                                            \" Link\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 61\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 55\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 49\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 37\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_7__.TableSkeleton, {\n                                rows: 3,\n                                columns: 1\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 43\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 mb-4\",\n                                        children: \"Failed to load broadcast links\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>queryClient.invalidateQueries({\n                                                queryKey: [\n                                                    \"broadcast-links\"\n                                                ]\n                                            }),\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 49\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 43\n                            }, undefined) : links.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"No broadcast links added yet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Add a link to get started\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    !showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>setShowAddForm(true),\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 61\n                                            }, undefined),\n                                            \"Add First Link\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 55\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 43\n                            }, undefined) : links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        link.title.toLowerCase().includes(\"comment\") || link.title.toLowerCase().includes(\"chat\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 91\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 333,\n                                                                            columnNumber: 91\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: link.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 335,\n                                                                            columnNumber: 85\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 79\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: getQualityColor(link.quality),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"mr-1 h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 338,\n                                                                            columnNumber: 85\n                                                                        }, undefined),\n                                                                        link.quality\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 79\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: [\n                                                                        getLanguageFlag(link.language),\n                                                                        \" \",\n                                                                        link.language\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 79\n                                                                }, undefined),\n                                                                link.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-green-50 text-green-700\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 85\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-gray-50 text-gray-700\",\n                                                                    children: \"Inactive\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 85\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 73\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 flex items-center space-x-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: link.url,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-blue-600 hover:text-blue-800 flex items-center space-x-1 truncate max-w-[300px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: link.url\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 85\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-3 w-3 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 85\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 355,\n                                                                columnNumber: 79\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 73\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 67\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleEdit(link),\n                                                            disabled: (editingLink === null || editingLink === void 0 ? void 0 : editingLink.id) === link.id,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 373,\n                                                                columnNumber: 79\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 73\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleDelete(link),\n                                                            disabled: deleteMutation.isLoading,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 79\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 73\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 67\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 61\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 55\n                                    }, undefined)\n                                }, link.id, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 49\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                    lineNumber: 198,\n                    columnNumber: 25\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n            lineNumber: 180,\n            columnNumber: 19\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n        lineNumber: 179,\n        columnNumber: 13\n    }, undefined);\n};\n_s(BroadcastLinksModal, \"EhJJchonTrQhsc5TenuRyqon8/g=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useMutation\n    ];\n});\n_c = BroadcastLinksModal;\nvar _c;\n$RefreshReg$(_c, \"BroadcastLinksModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/fixtures/BroadcastLinksModal.tsx\n"));

/***/ })

});