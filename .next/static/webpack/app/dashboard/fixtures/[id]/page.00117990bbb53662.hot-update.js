"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/page",{

/***/ "(app-pages-browser)/./src/components/debug/AuthDebug.tsx":
/*!********************************************!*\
  !*** ./src/components/debug/AuthDebug.tsx ***!
  \********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthDebug: function() { return /* binding */ AuthDebug; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _lib_stores_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/stores/auth */ \"(app-pages-browser)/./src/lib/stores/auth.ts\");\n/* harmony import */ var _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/hooks/useAuth */ \"(app-pages-browser)/./src/lib/hooks/useAuth.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ AuthDebug auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AuthDebug = ()=>{\n    var _authStore_accessToken, _authStore_user, _authStore_accessToken1, _auth_user;\n    _s();\n    const authStore = (0,_lib_stores_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore)();\n    const auth = (0,_lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const testBroadcastAPI = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D Testing broadcast API...\");\n            const response = await fetch(\"/api/broadcast-links/fixture/1274453\", {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(authStore.accessToken)\n                }\n            });\n            const data = await response.json();\n            console.log(\"\\uD83D\\uDCE1 API Response:\", {\n                status: response.status,\n                data\n            });\n        } catch (error) {\n            console.error(\"❌ API Error:\", error);\n        }\n    };\n    const testDebugAPI = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD0D Testing debug API...\");\n            const response = await fetch(\"/api/debug-auth\", {\n                method: \"GET\",\n                headers: {\n                    \"Content-Type\": \"application/json\",\n                    \"Authorization\": \"Bearer \".concat(authStore.accessToken)\n                }\n            });\n            const data = await response.json();\n            console.log(\"\\uD83D\\uDCE1 Debug Response:\", {\n                status: response.status,\n                data\n            });\n        } catch (error) {\n            console.error(\"❌ Debug Error:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n        className: \"w-full max-w-2xl mx-auto mt-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                    children: \"Auth Debug Info\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                lineNumber: 54,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold\",\n                                children: \"Auth Store:\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-gray-100 p-2 rounded text-sm overflow-auto\",\n                                children: JSON.stringify({\n                                    isAuthenticated: authStore.isAuthenticated,\n                                    hasToken: !!authStore.accessToken,\n                                    tokenLength: ((_authStore_accessToken = authStore.accessToken) === null || _authStore_accessToken === void 0 ? void 0 : _authStore_accessToken.length) || 0,\n                                    user: ((_authStore_user = authStore.user) === null || _authStore_user === void 0 ? void 0 : _authStore_user.username) || \"No user\",\n                                    tokenPreview: ((_authStore_accessToken1 = authStore.accessToken) === null || _authStore_accessToken1 === void 0 ? void 0 : _authStore_accessToken1.substring(0, 20)) + \"...\" || 0\n                                }, null, 2)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-semibold\",\n                                children: \"Auth Hook:\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-gray-100 p-2 rounded text-sm overflow-auto\",\n                                children: JSON.stringify({\n                                    isAuthenticated: auth.isAuthenticated,\n                                    isLoading: auth.isLoading,\n                                    user: ((_auth_user = auth.user) === null || _auth_user === void 0 ? void 0 : _auth_user.username) || \"No user\"\n                                }, null, 2)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 flex-wrap\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: testDebugAPI,\n                                variant: \"outline\",\n                                children: \"Test Debug API\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: testBroadcastAPI,\n                                variant: \"outline\",\n                                children: \"Test Broadcast API\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    console.log(\"\\uD83D\\uDD04 Force logout clicked\");\n                                    authStore.clearAuth();\n                                    window.location.href = \"/auth/login\";\n                                },\n                                variant: \"destructive\",\n                                children: \"Force Logout\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/debug/AuthDebug.tsx\",\n        lineNumber: 53,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AuthDebug, \"+XFFciEM6MkRyGDGONLb0gCEgog=\", false, function() {\n    return [\n        _lib_stores_auth__WEBPACK_IMPORTED_MODULE_1__.useAuthStore,\n        _lib_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = AuthDebug;\nvar _c;\n$RefreshReg$(_c, \"AuthDebug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/debug/AuthDebug.tsx\n"));

/***/ })

});