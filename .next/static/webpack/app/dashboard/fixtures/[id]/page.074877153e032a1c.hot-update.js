"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/api/broadcast-links.ts":
/*!****************************************!*\
  !*** ./src/lib/api/broadcast-links.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   broadcastLinksApi: function() { return /* binding */ broadcastLinksApi; }\n/* harmony export */ });\n/* harmony import */ var _lib_stores_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/stores/auth */ \"(app-pages-browser)/./src/lib/stores/auth.ts\");\n\n// Helper function to get auth headers\nconst getAuthHeaders = ()=>{\n    const authState = _lib_stores_auth__WEBPACK_IMPORTED_MODULE_0__.useAuthStore.getState();\n    const token = authState.accessToken;\n    console.log(\"\\uD83D\\uDD11 Auth Debug:\", {\n        isAuthenticated: authState.isAuthenticated,\n        hasToken: !!token,\n        tokenLength: (token === null || token === void 0 ? void 0 : token.length) || 0,\n        tokenPreview: (token === null || token === void 0 ? void 0 : token.substring(0, 20)) + \"...\" || 0\n    });\n    if (!token) {\n        console.error(\"❌ No access token found in auth store!\");\n        // Try to get from localStorage as fallback\n        const fallbackToken = localStorage.getItem(\"accessToken\");\n        if (fallbackToken) {\n            console.log(\"\\uD83D\\uDD04 Using fallback token from localStorage\");\n            return {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": \"Bearer \".concat(fallbackToken)\n            };\n        }\n    }\n    return {\n        \"Content-Type\": \"application/json\",\n        ...token && {\n            \"Authorization\": \"Bearer \".concat(token)\n        }\n    };\n};\nconst broadcastLinksApi = {\n    // Get all broadcast links with filters\n    getBroadcastLinks: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await fetch(\"/api/broadcast-links?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch broadcast links: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Get broadcast links for a specific fixture\n    getBroadcastLinksByFixture: async (fixtureId)=>{\n        const response = await fetch(\"/api/broadcast-links/fixture/\".concat(fixtureId), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch broadcast links for fixture: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Get single broadcast link\n    getBroadcastLinkById: async (id)=>{\n        const response = await fetch(\"/api/broadcast-links/\".concat(id), {\n            method: \"GET\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch broadcast link: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Create new broadcast link\n    createBroadcastLink: async (data)=>{\n        const response = await fetch(\"/api/broadcast-links\", {\n            method: \"POST\",\n            headers: getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to create broadcast link: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Update broadcast link\n    updateBroadcastLink: async (id, data)=>{\n        const response = await fetch(\"/api/broadcast-links/\".concat(id), {\n            method: \"PUT\",\n            headers: getAuthHeaders(),\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to update broadcast link: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Delete broadcast link\n    deleteBroadcastLink: async (id)=>{\n        const response = await fetch(\"/api/broadcast-links/\".concat(id), {\n            method: \"DELETE\",\n            headers: getAuthHeaders()\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to delete broadcast link: \".concat(response.statusText));\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/broadcast-links.ts\n"));

/***/ })

});