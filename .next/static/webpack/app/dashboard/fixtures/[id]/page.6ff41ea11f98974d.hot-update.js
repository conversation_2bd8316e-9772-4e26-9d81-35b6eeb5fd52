"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/page",{

/***/ "(app-pages-browser)/./src/components/fixtures/BroadcastLinksModal.tsx":
/*!*********************************************************!*\
  !*** ./src/components/fixtures/BroadcastLinksModal.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BroadcastLinksModal: function() { return /* binding */ BroadcastLinksModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,ExternalLink,MessageCircle,Plus,Radio,Save,Trash2,X,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_api_broadcast_links__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/broadcast-links */ \"(app-pages-browser)/./src/lib/api/broadcast-links.ts\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ BroadcastLinksModal auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst BroadcastLinksModal = (param)=>{\n    let { isOpen, onClose, fixture } = param;\n    _s();\n    const [showAddForm, setShowAddForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingLink, setEditingLink] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        url: \"\",\n        language: \"English\",\n        quality: \"HD\"\n    });\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.useQueryClient)();\n    // Fetch broadcast links for this fixture\n    const { data: linksData, isLoading, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQuery)({\n        queryKey: [\n            \"broadcast-links\",\n            fixture.externalId || fixture.id\n        ],\n        queryFn: ()=>_lib_api_broadcast_links__WEBPACK_IMPORTED_MODULE_7__.broadcastLinksApi.getBroadcastLinksByFixture(fixture.externalId || fixture.id),\n        enabled: isOpen\n    });\n    const links = (linksData === null || linksData === void 0 ? void 0 : linksData.data) || [];\n    // Mock mutations - will be replaced with real API\n    const createMutation = {\n        mutate: (data)=>{\n            console.log(\"Mock: Creating broadcast link:\", data);\n            setShowAddForm(false);\n            resetForm();\n        },\n        isLoading: false\n    };\n    const updateMutation = {\n        mutate: (param)=>{\n            let { id, data } = param;\n            console.log(\"Mock: Updating broadcast link:\", id, data);\n            setEditingLink(null);\n            resetForm();\n        },\n        isLoading: false\n    };\n    const deleteMutation = {\n        mutate: (id)=>{\n            console.log(\"Mock: Deleting broadcast link:\", id);\n        },\n        isLoading: false\n    };\n    const resetForm = ()=>{\n        setFormData({\n            title: \"\",\n            url: \"\",\n            language: \"English\",\n            quality: \"HD\"\n        });\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!formData.title.trim() || !formData.url.trim()) {\n            return;\n        }\n        const submitData = {\n            fixtureId: fixture.externalId || fixture.id,\n            title: formData.title.trim(),\n            url: formData.url.trim(),\n            language: formData.language,\n            quality: formData.quality,\n            isActive: true\n        };\n        if (editingLink) {\n            updateMutation.mutate({\n                id: editingLink.id,\n                data: submitData\n            });\n        } else {\n            createMutation.mutate(submitData);\n        }\n    };\n    const handleEdit = (link)=>{\n        setEditingLink(link);\n        setFormData({\n            title: link.title,\n            url: link.url,\n            language: link.language || \"English\",\n            quality: link.quality || \"HD\"\n        });\n        setShowAddForm(true);\n    };\n    const handleDelete = (link)=>{\n        if (confirm('Are you sure you want to delete \"'.concat(link.title, '\"?'))) {\n            deleteMutation.mutate(link.id);\n        }\n    };\n    const handleCancel = ()=>{\n        setShowAddForm(false);\n        setEditingLink(null);\n        resetForm();\n    };\n    const getQualityColor = (quality)=>{\n        switch(quality.toLowerCase()){\n            case \"4k\":\n            case \"uhd\":\n                return \"bg-purple-100 text-purple-800\";\n            case \"hd\":\n            case \"1080p\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"sd\":\n            case \"720p\":\n                return \"bg-green-100 text-green-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getLanguageFlag = (language)=>{\n        const flags = {\n            en: \"\\uD83C\\uDDFA\\uD83C\\uDDF8\",\n            es: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\",\n            fr: \"\\uD83C\\uDDEB\\uD83C\\uDDF7\",\n            de: \"\\uD83C\\uDDE9\\uD83C\\uDDEA\",\n            it: \"\\uD83C\\uDDEE\\uD83C\\uDDF9\",\n            pt: \"\\uD83C\\uDDF5\\uD83C\\uDDF9\",\n            ar: \"\\uD83C\\uDDF8\\uD83C\\uDDE6\"\n        };\n        return flags[language] || \"\\uD83C\\uDF10\";\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 49\n                                            }, undefined),\n                                            \"Broadcast Links - \",\n                                            fixture.homeTeamName,\n                                            \" vs \",\n                                            fixture.awayTeamName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: \"Manage streaming links for this fixture\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 43\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 37\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: onClose,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 43\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 37\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 31\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        !showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: [\n                                        links.length,\n                                        \" broadcast link\",\n                                        links.length !== 1 ? \"s\" : \"\",\n                                        \" available\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    onClick: ()=>setShowAddForm(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        \"Add Link\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 37\n                        }, undefined),\n                        showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: editingLink ? \"Edit Broadcast Link\" : \"Add New Broadcast Link\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 55\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                onClick: handleCancel,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 55\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 49\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSubmit,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"title\",\n                                                                children: \"Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"title\",\n                                                                value: formData.title,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        title: e.target.value\n                                                                    }),\n                                                                placeholder: \"e.g., ESPN HD Stream\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"url\",\n                                                                children: \"URL\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                id: \"url\",\n                                                                type: \"url\",\n                                                                value: formData.url,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        url: e.target.value\n                                                                    }),\n                                                                placeholder: \"https://...\",\n                                                                required: true\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"language\",\n                                                                children: \"Language\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 230,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"language\",\n                                                                value: formData.language,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        language: e.target.value\n                                                                    }),\n                                                                className: \"w-full p-2 border rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"en\",\n                                                                        children: \"\\uD83C\\uDDFA\\uD83C\\uDDF8 English\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 237,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"es\",\n                                                                        children: \"\\uD83C\\uDDEA\\uD83C\\uDDF8 Spanish\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 238,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"fr\",\n                                                                        children: \"\\uD83C\\uDDEB\\uD83C\\uDDF7 French\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 239,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"de\",\n                                                                        children: \"\\uD83C\\uDDE9\\uD83C\\uDDEA German\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 240,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"it\",\n                                                                        children: \"\\uD83C\\uDDEE\\uD83C\\uDDF9 Italian\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 241,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"pt\",\n                                                                        children: \"\\uD83C\\uDDF5\\uD83C\\uDDF9 Portuguese\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"ar\",\n                                                                        children: \"\\uD83C\\uDDF8\\uD83C\\uDDE6 Arabic\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 243,\n                                                                        columnNumber: 73\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                                htmlFor: \"quality\",\n                                                                children: \"Quality\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"quality\",\n                                                                value: formData.quality,\n                                                                onChange: (e)=>setFormData({\n                                                                        ...formData,\n                                                                        quality: e.target.value\n                                                                    }),\n                                                                className: \"w-full p-2 border rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"4K\",\n                                                                        children: \"4K Ultra HD\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 254,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"HD\",\n                                                                        children: \"HD (1080p)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 255,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"720p\",\n                                                                        children: \"HD (720p)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 256,\n                                                                        columnNumber: 73\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"SD\",\n                                                                        children: \"SD (480p)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 257,\n                                                                        columnNumber: 73\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 248,\n                                                                columnNumber: 67\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 61\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 55\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-end space-x-2 pt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        onClick: handleCancel,\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 61\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"submit\",\n                                                        disabled: createMutation.isLoading || updateMutation.isLoading,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4 mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 67\n                                                            }, undefined),\n                                                            editingLink ? \"Update\" : \"Add\",\n                                                            \" Link\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 61\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 55\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 49\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 37\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                                rows: 3,\n                                columns: 1\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 43\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600 mb-4\",\n                                        children: \"Failed to load broadcast links\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>console.log(\"Mock: Retry loading\"),\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 49\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 43\n                            }, undefined) : links.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-gray-600\",\n                                        children: \"No broadcast links added yet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Add a link to get started\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 49\n                                    }, undefined),\n                                    !showAddForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        onClick: ()=>setShowAddForm(true),\n                                        className: \"mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 61\n                                            }, undefined),\n                                            \"Add First Link\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 55\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 43\n                            }, undefined) : links.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3 mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        link.title.toLowerCase().includes(\"comment\") || link.title.toLowerCase().includes(\"chat\") ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-blue-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 311,\n                                                                            columnNumber: 91\n                                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-600\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 91\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: link.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 85\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 309,\n                                                                    columnNumber: 79\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: getQualityColor(link.quality),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"mr-1 h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                            lineNumber: 318,\n                                                                            columnNumber: 85\n                                                                        }, undefined),\n                                                                        link.quality\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 317,\n                                                                    columnNumber: 79\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    children: [\n                                                                        getLanguageFlag(link.language),\n                                                                        \" \",\n                                                                        link.language\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 79\n                                                                }, undefined),\n                                                                link.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-green-50 text-green-700\",\n                                                                    children: \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 325,\n                                                                    columnNumber: 85\n                                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"outline\",\n                                                                    className: \"bg-gray-50 text-gray-700\",\n                                                                    children: \"Inactive\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                    lineNumber: 329,\n                                                                    columnNumber: 85\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 73\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 flex items-center space-x-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                                href: link.url,\n                                                                target: \"_blank\",\n                                                                rel: \"noopener noreferrer\",\n                                                                className: \"text-blue-600 hover:text-blue-800 flex items-center space-x-1 truncate max-w-[300px]\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: link.url\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 341,\n                                                                        columnNumber: 85\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-3 w-3 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                        lineNumber: 342,\n                                                                        columnNumber: 85\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 335,\n                                                                columnNumber: 79\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 73\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 67\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleEdit(link),\n                                                            disabled: (editingLink === null || editingLink === void 0 ? void 0 : editingLink.id) === link.id,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 353,\n                                                                columnNumber: 79\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 73\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleDelete(link),\n                                                            disabled: deleteMutation.isLoading,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_ExternalLink_MessageCircle_Plus_Radio_Save_Trash2_X_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 79\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                            lineNumber: 355,\n                                                            columnNumber: 73\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 67\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 61\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 55\n                                    }, undefined)\n                                }, link.id, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 49\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 25\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n            lineNumber: 160,\n            columnNumber: 19\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/fixtures/BroadcastLinksModal.tsx\",\n        lineNumber: 159,\n        columnNumber: 13\n    }, undefined);\n};\n_s(BroadcastLinksModal, \"Gsw5HQEPuwu8gfp4PAIRAlAdABY=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_9__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_10__.useQuery\n    ];\n});\n_c = BroadcastLinksModal;\nvar _c;\n$RefreshReg$(_c, \"BroadcastLinksModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/fixtures/BroadcastLinksModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/broadcast-links.ts":
/*!****************************************!*\
  !*** ./src/lib/api/broadcast-links.ts ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   broadcastLinksApi: function() { return /* binding */ broadcastLinksApi; }\n/* harmony export */ });\nconst broadcastLinksApi = {\n    // Get all broadcast links with filters\n    getBroadcastLinks: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await fetch(\"/api/broadcast-links?\".concat(params.toString()), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch broadcast links: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Get broadcast links for a specific fixture\n    getBroadcastLinksByFixture: async (fixtureId)=>{\n        const response = await fetch(\"/api/broadcast-links/fixture/\".concat(fixtureId), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch broadcast links for fixture: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Get single broadcast link\n    getBroadcastLinkById: async (id)=>{\n        const response = await fetch(\"/api/broadcast-links/\".concat(id), {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch broadcast link: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Create new broadcast link\n    createBroadcastLink: async (data)=>{\n        const response = await fetch(\"/api/broadcast-links\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to create broadcast link: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Update broadcast link\n    updateBroadcastLink: async (id, data)=>{\n        const response = await fetch(\"/api/broadcast-links/\".concat(id), {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(data)\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to update broadcast link: \".concat(response.statusText));\n        }\n        return await response.json();\n    },\n    // Delete broadcast link\n    deleteBroadcastLink: async (id)=>{\n        const response = await fetch(\"/api/broadcast-links/\".concat(id), {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(\"Failed to delete broadcast link: \".concat(response.statusText));\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvYXBpL2Jyb2FkY2FzdC1saW5rcy50cyIsIm1hcHBpbmdzIjoiOzs7O0FBMENPLE1BQU1BLG9CQUFvQjtJQUMvQix1Q0FBdUM7SUFDdkNDLG1CQUFtQjtZQUFPQywyRUFBZ0MsQ0FBQztRQUN6RCxNQUFNQyxTQUFTLElBQUlDO1FBQ25CQyxPQUFPQyxPQUFPLENBQUNKLFNBQVNLLE9BQU8sQ0FBQztnQkFBQyxDQUFDQyxLQUFLQyxNQUFNO1lBQzNDLElBQUlBLFVBQVVDLFdBQVc7Z0JBQ3ZCUCxPQUFPUSxNQUFNLENBQUNILEtBQUtDLE1BQU1HLFFBQVE7WUFDbkM7UUFDRjtRQUVBLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSx3QkFBMEMsT0FBbEJYLE9BQU9TLFFBQVEsS0FBTTtZQUN4RUcsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtRQUNGO1FBRUEsSUFBSSxDQUFDSCxTQUFTSSxFQUFFLEVBQUU7WUFDaEIsTUFBTSxJQUFJQyxNQUFNLG9DQUF3RCxPQUFwQkwsU0FBU00sVUFBVTtRQUN6RTtRQUVBLE9BQU8sTUFBTU4sU0FBU08sSUFBSTtJQUM1QjtJQUVBLDZDQUE2QztJQUM3Q0MsNEJBQTRCLE9BQU9DO1FBQ2pDLE1BQU1ULFdBQVcsTUFBTUMsTUFBTSxnQ0FBMEMsT0FBVlEsWUFBYTtZQUN4RVAsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtRQUNGO1FBRUEsSUFBSSxDQUFDSCxTQUFTSSxFQUFFLEVBQUU7WUFDaEIsTUFBTSxJQUFJQyxNQUFNLGdEQUFvRSxPQUFwQkwsU0FBU00sVUFBVTtRQUNyRjtRQUVBLE9BQU8sTUFBTU4sU0FBU08sSUFBSTtJQUM1QjtJQUVBLDRCQUE0QjtJQUM1Qkcsc0JBQXNCLE9BQU9DO1FBQzNCLE1BQU1YLFdBQVcsTUFBTUMsTUFBTSx3QkFBMkIsT0FBSFUsS0FBTTtZQUN6RFQsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtZQUNsQjtRQUNGO1FBRUEsSUFBSSxDQUFDSCxTQUFTSSxFQUFFLEVBQUU7WUFDaEIsTUFBTSxJQUFJQyxNQUFNLG1DQUF1RCxPQUFwQkwsU0FBU00sVUFBVTtRQUN4RTtRQUVBLE9BQU8sTUFBTU4sU0FBU08sSUFBSTtJQUM1QjtJQUVBLDRCQUE0QjtJQUM1QksscUJBQXFCLE9BQU9DO1FBQzFCLE1BQU1iLFdBQVcsTUFBTUMsTUFBTSx3QkFBd0I7WUFDbkRDLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7WUFDQVcsTUFBTUMsS0FBS0MsU0FBUyxDQUFDSDtRQUN2QjtRQUVBLElBQUksQ0FBQ2IsU0FBU0ksRUFBRSxFQUFFO1lBQ2hCLE1BQU0sSUFBSUMsTUFBTSxvQ0FBd0QsT0FBcEJMLFNBQVNNLFVBQVU7UUFDekU7UUFFQSxPQUFPLE1BQU1OLFNBQVNPLElBQUk7SUFDNUI7SUFFQSx3QkFBd0I7SUFDeEJVLHFCQUFxQixPQUFPTixJQUFZRTtRQUN0QyxNQUFNYixXQUFXLE1BQU1DLE1BQU0sd0JBQTJCLE9BQUhVLEtBQU07WUFDekRULFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7WUFDQVcsTUFBTUMsS0FBS0MsU0FBUyxDQUFDSDtRQUN2QjtRQUVBLElBQUksQ0FBQ2IsU0FBU0ksRUFBRSxFQUFFO1lBQ2hCLE1BQU0sSUFBSUMsTUFBTSxvQ0FBd0QsT0FBcEJMLFNBQVNNLFVBQVU7UUFDekU7UUFFQSxPQUFPLE1BQU1OLFNBQVNPLElBQUk7SUFDNUI7SUFFQSx3QkFBd0I7SUFDeEJXLHFCQUFxQixPQUFPUDtRQUMxQixNQUFNWCxXQUFXLE1BQU1DLE1BQU0sd0JBQTJCLE9BQUhVLEtBQU07WUFDekRULFFBQVE7WUFDUkMsU0FBUztnQkFDUCxnQkFBZ0I7WUFDbEI7UUFDRjtRQUVBLElBQUksQ0FBQ0gsU0FBU0ksRUFBRSxFQUFFO1lBQ2hCLE1BQU0sSUFBSUMsTUFBTSxvQ0FBd0QsT0FBcEJMLFNBQVNNLFVBQVU7UUFDekU7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9hcGkvYnJvYWRjYXN0LWxpbmtzLnRzPzU2YzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnaW5hdGVkUmVzcG9uc2UgfSBmcm9tICdAL2xpYi90eXBlcy9hcGknO1xuXG5leHBvcnQgaW50ZXJmYWNlIEJyb2FkY2FzdExpbmsge1xuICBpZDogbnVtYmVyO1xuICBmaXh0dXJlSWQ6IG51bWJlcjtcbiAgbGlua05hbWU6IHN0cmluZzsgICAgICAgIC8vIEFQSSB1c2VzIGxpbmtOYW1lXG4gIGxpbmtVcmw6IHN0cmluZzsgICAgICAgICAvLyBBUEkgdXNlcyBsaW5rVXJsXG4gIGxpbmtDb21tZW50OiBzdHJpbmc7ICAgICAvLyBBUEkgdXNlcyBsaW5rQ29tbWVudFxuICBsYW5ndWFnZT86IHN0cmluZztcbiAgcXVhbGl0eT86IHN0cmluZztcbiAgYWRkZWRCeTogbnVtYmVyO1xuICBjcmVhdGVkQXQ6IHN0cmluZztcbiAgdXBkYXRlZEF0OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQnJvYWRjYXN0TGlua0ZpbHRlcnMge1xuICBwYWdlPzogbnVtYmVyO1xuICBsaW1pdD86IG51bWJlcjtcbiAgZml4dHVyZUlkPzogbnVtYmVyO1xuICBsYW5ndWFnZT86IHN0cmluZztcbiAgcXVhbGl0eT86IHN0cmluZztcbiAgaXNBY3RpdmU/OiBib29sZWFuO1xuICBzZWFyY2g/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ3JlYXRlQnJvYWRjYXN0TGlua0RhdGEge1xuICBmaXh0dXJlSWQ6IG51bWJlcjtcbiAgbGlua05hbWU6IHN0cmluZzsgICAgICAgIC8vIEFQSSB1c2VzIGxpbmtOYW1lXG4gIGxpbmtVcmw6IHN0cmluZzsgICAgICAgICAvLyBBUEkgdXNlcyBsaW5rVXJsXG4gIGxpbmtDb21tZW50OiBzdHJpbmc7ICAgICAvLyBBUEkgdXNlcyBsaW5rQ29tbWVudCAocmVxdWlyZWQpXG4gIGxhbmd1YWdlPzogc3RyaW5nO1xuICBxdWFsaXR5Pzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVwZGF0ZUJyb2FkY2FzdExpbmtEYXRhIHtcbiAgbGlua05hbWU/OiBzdHJpbmc7ICAgICAgIC8vIEFQSSB1c2VzIGxpbmtOYW1lXG4gIGxpbmtVcmw/OiBzdHJpbmc7ICAgICAgICAvLyBBUEkgdXNlcyBsaW5rVXJsXG4gIGxpbmtDb21tZW50Pzogc3RyaW5nOyAgICAvLyBBUEkgdXNlcyBsaW5rQ29tbWVudFxuICBsYW5ndWFnZT86IHN0cmluZztcbiAgcXVhbGl0eT86IHN0cmluZztcbn1cblxuZXhwb3J0IGNvbnN0IGJyb2FkY2FzdExpbmtzQXBpID0ge1xuICAvLyBHZXQgYWxsIGJyb2FkY2FzdCBsaW5rcyB3aXRoIGZpbHRlcnNcbiAgZ2V0QnJvYWRjYXN0TGlua3M6IGFzeW5jIChmaWx0ZXJzOiBCcm9hZGNhc3RMaW5rRmlsdGVycyA9IHt9KTogUHJvbWlzZTxQYWdpbmF0ZWRSZXNwb25zZTxCcm9hZGNhc3RMaW5rPj4gPT4ge1xuICAgIGNvbnN0IHBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgICBPYmplY3QuZW50cmllcyhmaWx0ZXJzKS5mb3JFYWNoKChba2V5LCB2YWx1ZV0pID0+IHtcbiAgICAgIGlmICh2YWx1ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHBhcmFtcy5hcHBlbmQoa2V5LCB2YWx1ZS50b1N0cmluZygpKTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvYnJvYWRjYXN0LWxpbmtzPyR7cGFyYW1zLnRvU3RyaW5nKCl9YCwge1xuICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBicm9hZGNhc3QgbGlua3M6ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICB9XG5cbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9LFxuXG4gIC8vIEdldCBicm9hZGNhc3QgbGlua3MgZm9yIGEgc3BlY2lmaWMgZml4dHVyZVxuICBnZXRCcm9hZGNhc3RMaW5rc0J5Rml4dHVyZTogYXN5bmMgKGZpeHR1cmVJZDogbnVtYmVyKTogUHJvbWlzZTx7IGRhdGE6IEJyb2FkY2FzdExpbmtbXSB9PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9icm9hZGNhc3QtbGlua3MvZml4dHVyZS8ke2ZpeHR1cmVJZH1gLCB7XG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGJyb2FkY2FzdCBsaW5rcyBmb3IgZml4dHVyZTogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgIH1cblxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gIH0sXG5cbiAgLy8gR2V0IHNpbmdsZSBicm9hZGNhc3QgbGlua1xuICBnZXRCcm9hZGNhc3RMaW5rQnlJZDogYXN5bmMgKGlkOiBudW1iZXIpOiBQcm9taXNlPHsgZGF0YTogQnJvYWRjYXN0TGluayB9PiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9icm9hZGNhc3QtbGlua3MvJHtpZH1gLCB7XG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGJyb2FkY2FzdCBsaW5rOiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgfSxcblxuICAvLyBDcmVhdGUgbmV3IGJyb2FkY2FzdCBsaW5rXG4gIGNyZWF0ZUJyb2FkY2FzdExpbms6IGFzeW5jIChkYXRhOiBDcmVhdGVCcm9hZGNhc3RMaW5rRGF0YSk6IFByb21pc2U8eyBkYXRhOiBCcm9hZGNhc3RMaW5rIH0+ID0+IHtcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2Jyb2FkY2FzdC1saW5rcycsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGRhdGEpLFxuICAgIH0pO1xuXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gY3JlYXRlIGJyb2FkY2FzdCBsaW5rOiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgfSxcblxuICAvLyBVcGRhdGUgYnJvYWRjYXN0IGxpbmtcbiAgdXBkYXRlQnJvYWRjYXN0TGluazogYXN5bmMgKGlkOiBudW1iZXIsIGRhdGE6IFVwZGF0ZUJyb2FkY2FzdExpbmtEYXRhKTogUHJvbWlzZTx7IGRhdGE6IEJyb2FkY2FzdExpbmsgfT4gPT4ge1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYC9hcGkvYnJvYWRjYXN0LWxpbmtzLyR7aWR9YCwge1xuICAgICAgbWV0aG9kOiAnUFVUJyxcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIH0sXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkYXRhKSxcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHVwZGF0ZSBicm9hZGNhc3QgbGluazogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgIH1cblxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gIH0sXG5cbiAgLy8gRGVsZXRlIGJyb2FkY2FzdCBsaW5rXG4gIGRlbGV0ZUJyb2FkY2FzdExpbms6IGFzeW5jIChpZDogbnVtYmVyKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS9icm9hZGNhc3QtbGlua3MvJHtpZH1gLCB7XG4gICAgICBtZXRob2Q6ICdERUxFVEUnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICB9KTtcblxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGRlbGV0ZSBicm9hZGNhc3QgbGluazogJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgIH1cbiAgfSxcbn07XG4iXSwibmFtZXMiOlsiYnJvYWRjYXN0TGlua3NBcGkiLCJnZXRCcm9hZGNhc3RMaW5rcyIsImZpbHRlcnMiLCJwYXJhbXMiLCJVUkxTZWFyY2hQYXJhbXMiLCJPYmplY3QiLCJlbnRyaWVzIiwiZm9yRWFjaCIsImtleSIsInZhbHVlIiwidW5kZWZpbmVkIiwiYXBwZW5kIiwidG9TdHJpbmciLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsIm9rIiwiRXJyb3IiLCJzdGF0dXNUZXh0IiwianNvbiIsImdldEJyb2FkY2FzdExpbmtzQnlGaXh0dXJlIiwiZml4dHVyZUlkIiwiZ2V0QnJvYWRjYXN0TGlua0J5SWQiLCJpZCIsImNyZWF0ZUJyb2FkY2FzdExpbmsiLCJkYXRhIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJ1cGRhdGVCcm9hZGNhc3RMaW5rIiwiZGVsZXRlQnJvYWRjYXN0TGluayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/broadcast-links.ts\n"));

/***/ })

});