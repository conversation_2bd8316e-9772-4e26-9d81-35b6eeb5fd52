"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/[id]/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/fixtures/[id]/page.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixtureDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Edit,MapPin,Radio,RefreshCw,Trash2,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_fixtures_FixtureCard__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/fixtures/FixtureCard */ \"(app-pages-browser)/./src/components/fixtures/FixtureCard.tsx\");\n/* harmony import */ var _components_fixtures_FixtureStats__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/fixtures/FixtureStats */ \"(app-pages-browser)/./src/components/fixtures/FixtureStats.tsx\");\n/* harmony import */ var _components_fixtures_FixtureTimeline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/fixtures/FixtureTimeline */ \"(app-pages-browser)/./src/components/fixtures/FixtureTimeline.tsx\");\n/* harmony import */ var _components_fixtures_BroadcastLinksModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/fixtures/BroadcastLinksModal */ \"(app-pages-browser)/./src/components/fixtures/BroadcastLinksModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction FixtureDetailPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const fixtureId = parseInt(params.id);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_7__.usePermissions)();\n    // Fetch fixture details\n    const { data: fixture, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"fixture\",\n            fixtureId\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_6__.fixturesApi.getFixture(fixtureId),\n        enabled: !!fixtureId\n    });\n    // Handler functions\n    const handleEdit = ()=>{\n        // TODO: Open edit modal\n        console.log(\"Edit fixture:\", fixtureId);\n    };\n    const handleBroadcastLinks = ()=>{\n        setBroadcastLinksModalOpen(true);\n    };\n    const handleDelete = ()=>{\n        // TODO: Open delete confirmation\n        console.log(\"Delete fixture:\", fixtureId);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"1H\":\n            case \"2H\":\n            case \"HT\":\n                return \"bg-green-100 text-green-800\";\n            case \"FT\":\n                return \"bg-gray-100 text-gray-800\";\n            case \"NS\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"CANC\":\n            case \"PST\":\n                return \"bg-red-100 text-red-800\";\n            default:\n                return \"bg-yellow-100 text-yellow-800\";\n        }\n    };\n    const getStatusText = (status, elapsed)=>{\n        switch(status){\n            case \"1H\":\n            case \"2H\":\n                return \"\".concat(elapsed, \"'\");\n            case \"HT\":\n                return \"Half Time\";\n            case \"FT\":\n                return \"Full Time\";\n            case \"NS\":\n                return \"Not Started\";\n            case \"CANC\":\n                return \"Cancelled\";\n            case \"PST\":\n                return \"Postponed\";\n            default:\n                return status;\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                            className: \"h-10 w-10\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"h-8 w-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"h-4 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"h-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"h-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"h-32\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"h-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !fixture) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixture details\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: ()=>router.push(\"/dashboard/fixtures\"),\n                                    children: \"Return to Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n            lineNumber: 126,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>router.back(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Back to Fixtures\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold text-gray-900\",\n                                        children: [\n                                            fixture.homeTeamName,\n                                            \" vs \",\n                                            fixture.awayTeamName\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-1\",\n                                        children: [\n                                            fixture.leagueName,\n                                            \" • Fixture Details\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: handleBroadcastLinks,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Broadcast Links\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: handleEdit,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Edit\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleDelete,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Delete\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fixtures_FixtureCard__WEBPACK_IMPORTED_MODULE_8__.FixtureCard, {\n                                fixture: fixture\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fixtures_FixtureTimeline__WEBPACK_IMPORTED_MODULE_10__.FixtureTimeline, {\n                                fixture: fixture\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fixtures_FixtureStats__WEBPACK_IMPORTED_MODULE_9__.FixtureStats, {\n                                fixture: fixture\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Match Information\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: new Date(fixture.date).toLocaleDateString()\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Time\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: new Date(fixture.date).toLocaleTimeString([], {\n                                                                    hour: \"2-digit\",\n                                                                    minute: \"2-digit\"\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                                lineNumber: 232,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: \"League\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                                lineNumber: 244,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: fixture.leagueName\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 15\n                                            }, this),\n                                            fixture.venueName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Venue\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: fixture.venueName\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                                lineNumber: 254,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Quick Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full justify-start\",\n                                                onClick: ()=>window.open(\"/dashboard/leagues/\".concat(fixture.leagueId), \"_blank\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"View League\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full justify-start\",\n                                                onClick: ()=>window.open(\"/dashboard/teams/\".concat(fixture.homeTeamId), \"_blank\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"View Home Team\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                variant: \"outline\",\n                                                className: \"w-full justify-start\",\n                                                onClick: ()=>window.open(\"/dashboard/teams/\".concat(fixture.awayTeamId), \"_blank\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Edit_MapPin_Radio_RefreshCw_Trash2_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"View Away Team\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fixtures_BroadcastLinksModal__WEBPACK_IMPORTED_MODULE_11__.BroadcastLinksModal, {\n                isOpen: broadcastLinksModalOpen,\n                onClose: ()=>setBroadcastLinksModalOpen(false),\n                fixture: fixture\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n                lineNumber: 299,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/[id]/page.tsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\n_s(FixtureDetailPage, \"mXgA5/zQ/ptKVHl9qyEzT/ZAt0E=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_7__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery\n    ];\n});\n_c = FixtureDetailPage;\nvar _c;\n$RefreshReg$(_c, \"FixtureDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/[id]/page.tsx\n"));

/***/ })

});