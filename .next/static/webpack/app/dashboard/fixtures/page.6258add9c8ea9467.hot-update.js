"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_layout_PlaceholderPage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/layout/PlaceholderPage */ \"(app-pages-browser)/./src/components/layout/PlaceholderPage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction FixturesPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_PlaceholderPage__WEBPACK_IMPORTED_MODULE_1__.PlaceholderPage, {\n        title: \"Fixtures Management\",\n        description: \"Manage football fixtures, schedules, and match data.\",\n        iconName: \"calendar\"\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ZpeHR1cmVzL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFc0U7QUFFdkQsU0FBU0M7SUFDdEIscUJBQ0UsOERBQUNELCtFQUFlQTtRQUNkRSxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsVUFBUzs7Ozs7O0FBR2Y7S0FSd0JIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZGFzaGJvYXJkL2ZpeHR1cmVzL3BhZ2UudHN4PzA1MzMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBQbGFjZWhvbGRlclBhZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvbGF5b3V0L1BsYWNlaG9sZGVyUGFnZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEZpeHR1cmVzUGFnZSgpIHtcbiAgcmV0dXJuIChcbiAgICA8UGxhY2Vob2xkZXJQYWdlXG4gICAgICB0aXRsZT1cIkZpeHR1cmVzIE1hbmFnZW1lbnRcIlxuICAgICAgZGVzY3JpcHRpb249XCJNYW5hZ2UgZm9vdGJhbGwgZml4dHVyZXMsIHNjaGVkdWxlcywgYW5kIG1hdGNoIGRhdGEuXCJcbiAgICAgIGljb25OYW1lPVwiY2FsZW5kYXJcIlxuICAgIC8+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiUGxhY2Vob2xkZXJQYWdlIiwiRml4dHVyZXNQYWdlIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImljb25OYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ })

});