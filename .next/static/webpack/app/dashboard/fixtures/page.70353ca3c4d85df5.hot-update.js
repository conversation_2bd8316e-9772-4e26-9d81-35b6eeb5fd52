"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/lib/utils/date-time.ts":
/*!************************************!*\
  !*** ./src/lib/utils/date-time.ts ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   convertLocalDateToUTC: function() { return /* binding */ convertLocalDateToUTC; },\n/* harmony export */   formatToLocalTime: function() { return /* binding */ formatToLocalTime; },\n/* harmony export */   formatToUTC: function() { return /* binding */ formatToUTC; },\n/* harmony export */   getTimezoneDisplayName: function() { return /* binding */ getTimezoneDisplayName; },\n/* harmony export */   getTimezoneOffset: function() { return /* binding */ getTimezoneOffset; },\n/* harmony export */   getUserTimezone: function() { return /* binding */ getUserTimezone; },\n/* harmony export */   isSameDate: function() { return /* binding */ isSameDate; },\n/* harmony export */   parseUTCToLocal: function() { return /* binding */ parseUTCToLocal; }\n/* harmony export */ });\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/parseISO.js\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/isValid.js\");\n/* harmony import */ var _barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=format,isValid,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_tz__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! date-fns-tz */ \"(app-pages-browser)/./node_modules/date-fns-tz/dist/esm/index.js\");\n\n\n/**\n * Get user's timezone\n */ const getUserTimezone = ()=>{\n    return Intl.DateTimeFormat().resolvedOptions().timeZone;\n};\n/**\n * Format date to local timezone\n */ const formatToLocalTime = function(dateString) {\n    let formatStr = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"dd/MM/yyyy HH:mm\";\n    try {\n        const date = typeof dateString === \"string\" ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.parseISO)(dateString) : dateString;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.isValid)(date)) {\n            return \"Invalid Date\";\n        }\n        const userTimezone = getUserTimezone();\n        return (0,date_fns_tz__WEBPACK_IMPORTED_MODULE_0__.formatInTimeZone)(date, userTimezone, formatStr);\n    } catch (error) {\n        console.error(\"Error formatting date to local time:\", error);\n        return \"Invalid Date\";\n    }\n};\n/**\n * Format date to UTC\n */ const formatToUTC = function(dateString) {\n    let formatStr = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"dd/MM/yyyy HH:mm\";\n    try {\n        const date = typeof dateString === \"string\" ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.parseISO)(dateString) : dateString;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.isValid)(date)) {\n            return \"Invalid Date\";\n        }\n        return (0,date_fns_tz__WEBPACK_IMPORTED_MODULE_0__.formatInTimeZone)(date, \"UTC\", formatStr);\n    } catch (error) {\n        console.error(\"Error formatting date to UTC:\", error);\n        return \"Invalid Date\";\n    }\n};\n/**\n * Get timezone offset display\n */ const getTimezoneOffset = ()=>{\n    const offset = new Date().getTimezoneOffset();\n    const hours = Math.floor(Math.abs(offset) / 60);\n    const minutes = Math.abs(offset) % 60;\n    const sign = offset <= 0 ? \"+\" : \"-\";\n    return \"GMT\".concat(sign).concat(hours.toString().padStart(2, \"0\"), \":\").concat(minutes.toString().padStart(2, \"0\"));\n};\n/**\n * Get timezone display name\n */ const getTimezoneDisplayName = ()=>{\n    var _formatToParts_find;\n    const timezone = getUserTimezone();\n    const offset = getTimezoneOffset();\n    // Get short timezone name\n    const shortName = ((_formatToParts_find = new Intl.DateTimeFormat(\"en\", {\n        timeZoneName: \"short\",\n        timeZone: timezone\n    }).formatToParts(new Date()).find((part)=>part.type === \"timeZoneName\")) === null || _formatToParts_find === void 0 ? void 0 : _formatToParts_find.value) || \"\";\n    return \"\".concat(shortName, \" (\").concat(offset, \")\");\n};\n/**\n * Check if date matches selected date (ignoring time)\n */ const isSameDate = (date1, date2)=>{\n    try {\n        const d1 = typeof date1 === \"string\" ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.parseISO)(date1) : date1;\n        const d2 = typeof date2 === \"string\" ? (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.parseISO)(date2) : date2;\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.isValid)(d1) || !(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.isValid)(d2)) {\n            return false;\n        }\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)(d1, \"yyyy-MM-dd\") === (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)(d2, \"yyyy-MM-dd\");\n    } catch (error) {\n        return false;\n    }\n};\n/**\n * Convert local date to UTC for API filtering\n */ const convertLocalDateToUTC = (localDate)=>{\n    try {\n        // Convert local date to UTC by adjusting for timezone offset\n        const utcDate = new Date(localDate.getTime() - localDate.getTimezoneOffset() * 60000);\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)(utcDate, \"yyyy-MM-dd\");\n    } catch (error) {\n        console.error(\"Error converting local date to UTC:\", error);\n        return (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_3__.format)(localDate, \"yyyy-MM-dd\");\n    }\n};\n/**\n * Parse UTC date string and convert to local Date object\n */ const parseUTCToLocal = (utcDateString)=>{\n    try {\n        const utcDate = (0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__.parseISO)(utcDateString);\n        if (!(0,_barrel_optimize_names_format_isValid_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__.isValid)(utcDate)) {\n            return null;\n        }\n        // Return the date as-is since parseISO already handles UTC conversion\n        return utcDate;\n    } catch (error) {\n        console.error(\"Error parsing UTC to local:\", error);\n        return null;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/utils/date-time.ts\n"));

/***/ })

});