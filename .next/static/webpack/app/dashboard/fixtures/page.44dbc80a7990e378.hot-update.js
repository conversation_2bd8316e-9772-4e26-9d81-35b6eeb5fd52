"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_fixtures_BroadcastLinksModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/fixtures/BroadcastLinksModal */ \"(app-pages-browser)/./src/components/fixtures/BroadcastLinksModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction FixturesPage() {\n    var _data_meta_totalItems, _data_meta, _data_data, _data_data1, _data_data2, _data_meta1;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [limit, setLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leagueFilter, setLeagueFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixture, setSelectedFixture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { isAdmin, isEditor } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient)();\n    // Fetch fixtures data\n    const { data, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\",\n            page,\n            limit,\n            searchQuery,\n            statusFilter,\n            leagueFilter\n        ],\n        queryFn: ()=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixtures({\n                page,\n                limit,\n                ...searchQuery && {\n                    search: searchQuery\n                },\n                ...statusFilter && {\n                    status: statusFilter\n                },\n                ...leagueFilter && {\n                    league: leagueFilter\n                }\n            }),\n        staleTime: 30000\n    });\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation)({\n        mutationFn: (fixtureId)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.deleteFixture(fixtureId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            console.log(\"Fixture deleted successfully\");\n            setDeleteModalOpen(false);\n            setSelectedFixture(null);\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete fixture:\", error.message);\n        }\n    });\n    // Define table columns\n    const columns = [\n        {\n            key: \"date\",\n            title: \"Date & Time\",\n            sortable: true,\n            render: (value)=>{\n                const date = new Date(value);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium\",\n                            children: date.toLocaleDateString()\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-gray-500\",\n                            children: date.toLocaleTimeString([], {\n                                hour: \"2-digit\",\n                                minute: \"2-digit\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"match\",\n            title: \"Match\",\n            sortable: false,\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-3 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1 justify-end\",\n                            children: [\n                                row.homeTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: row.homeTeamLogo.replace(\"public/\", \"/api/images/\"),\n                                    alt: row.homeTeamName,\n                                    className: \"w-6 h-6 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-right\",\n                                    children: row.homeTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 font-bold\",\n                                children: \"vs\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-medium text-left\",\n                                    children: row.awayTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                row.awayTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: row.awayTeamLogo.replace(\"public/\", \"/api/images/\"),\n                                    alt: row.awayTeamName,\n                                    className: \"w-6 h-6 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            title: \"Score\",\n            align: \"center\",\n            render: (_, row)=>/*#__PURE__*/ {\n                var _row_goalsHome, _row_goalsAway;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                (_row_goalsHome = row.goalsHome) !== null && _row_goalsHome !== void 0 ? _row_goalsHome : \"-\",\n                                \" - \",\n                                (_row_goalsAway = row.goalsAway) !== null && _row_goalsAway !== void 0 ? _row_goalsAway : \"-\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this),\n                        row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"HT: \",\n                                row.scoreHalftimeHome,\n                                \" - \",\n                                row.scoreHalftimeAway\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            filterable: true,\n            render: (value, row)=>{\n                const getStatusColor = (status)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                        case \"HT\":\n                            return \"bg-green-100 text-green-800\";\n                        case \"FT\":\n                            return \"bg-gray-100 text-gray-800\";\n                        case \"NS\":\n                            return \"bg-blue-100 text-blue-800\";\n                        case \"CANC\":\n                        case \"PST\":\n                            return \"bg-red-100 text-red-800\";\n                        default:\n                            return \"bg-yellow-100 text-yellow-800\";\n                    }\n                };\n                const getStatusText = (status, elapsed)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                            return \"\".concat(elapsed, \"'\");\n                        case \"HT\":\n                            return \"Half Time\";\n                        case \"FT\":\n                            return \"Full Time\";\n                        case \"NS\":\n                            return \"Not Started\";\n                        case \"CANC\":\n                            return \"Cancelled\";\n                        case \"PST\":\n                            return \"Postponed\";\n                        default:\n                            return status;\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: getStatusColor(value),\n                    children: getStatusText(value, row.elapsed)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"leagueName\",\n            title: \"League\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Broadcast Links\",\n                            onClick: ()=>handleBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Broadcast Links\",\n                            onClick: ()=>handleViewBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit\",\n                            onClick: ()=>handleEditFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete\",\n                            onClick: ()=>handleDeleteFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Handler functions\n    const handleSearch = (query)=>{\n        setSearchQuery(query);\n        setPage(1); // Reset to first page when searching\n    };\n    const handleViewFixture = (fixture)=>{\n        // Navigate to fixture detail page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id), \"_blank\");\n    };\n    const handleEditFixture = (fixture)=>{\n        // Navigate to edit page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id, \"/edit\"), \"_blank\");\n    };\n    const handleDeleteFixture = (fixture)=>{\n        setSelectedFixture(fixture);\n        setDeleteModalOpen(true);\n    };\n    const handleBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const handleViewBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        if (selectedFixture) {\n            deleteMutation.mutate(selectedFixture.id);\n        }\n    };\n    const handleBulkSync = async ()=>{\n        try {\n            console.log(\"Starting fixtures sync...\");\n            // This would call the sync API\n            // await fixturesApi.syncFixtures();\n            console.log(\"Fixtures sync completed\");\n            refetch();\n        } catch (error) {\n            console.error(\"Sync failed:\", error.message);\n        }\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Fixtures Management\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage football fixtures and match data\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 307,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 304,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 303,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n            lineNumber: 297,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fixtures Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage football fixtures and match data\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 11\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkSync,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Export functionality\n                                    console.log(\"Export feature coming soon\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>window.open(\"/dashboard/fixtures/create\", \"_blank\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Fixture\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_meta = data.meta) === null || _data_meta === void 0 ? void 0 : (_data_meta_totalItems = _data_meta.totalItems) === null || _data_meta_totalItems === void 0 ? void 0 : _data_meta_totalItems.toLocaleString()) || \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 375,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.filter((f)=>[\n                                            \"1H\",\n                                            \"2H\",\n                                            \"HT\"\n                                        ].includes(f.status)).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Live Matches\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.filter((f)=>f.status === \"NS\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Upcoming\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.filter((f)=>f.status === \"FT\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 397,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"All Fixtures\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Complete list of football fixtures with real-time updates\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 409,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                            rows: 10,\n                            columns: 7\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 420,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n                            data: (data === null || data === void 0 ? void 0 : data.data) || [],\n                            columns: columns,\n                            loading: isLoading,\n                            searchable: true,\n                            searchPlaceholder: \"Search fixtures...\",\n                            onSearch: handleSearch,\n                            pagination: {\n                                page,\n                                limit,\n                                total: (data === null || data === void 0 ? void 0 : (_data_meta1 = data.meta) === null || _data_meta1 === void 0 ? void 0 : _data_meta1.totalItems) || 0,\n                                onPageChange: setPage,\n                                onLimitChange: setLimit\n                            },\n                            emptyMessage: \"No fixtures found\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 422,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 408,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>{\n                    setDeleteModalOpen(false);\n                    setSelectedFixture(null);\n                },\n                onConfirm: confirmDelete,\n                title: \"Delete Fixture\",\n                message: selectedFixture ? 'Are you sure you want to delete the fixture \"'.concat(selectedFixture.homeTeamName, \" vs \").concat(selectedFixture.awayTeamName, '\"? This action cannot be undone.') : \"Are you sure you want to delete this fixture?\",\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                variant: \"destructive\",\n                loading: deleteMutation.isPending\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 443,\n                columnNumber: 7\n            }, this),\n            selectedFixtureForBroadcast && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_fixtures_BroadcastLinksModal__WEBPACK_IMPORTED_MODULE_10__.BroadcastLinksModal, {\n                isOpen: broadcastLinksModalOpen,\n                onClose: ()=>{\n                    setBroadcastLinksModalOpen(false);\n                    setSelectedFixtureForBroadcast(null);\n                },\n                fixture: selectedFixtureForBroadcast\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 464,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesPage, \"iktP6OjuPcgb6yHwMYGIPzr+vdA=\", false, function() {\n    return [\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_11__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_12__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_13__.useMutation\n    ];\n});\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ })

});