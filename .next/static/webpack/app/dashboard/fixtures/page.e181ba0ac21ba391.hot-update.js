"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/fixtures/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/fixtures/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FixturesPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./src/components/ui/data-table.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/radio.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Download,Edit,Eye,List,Plus,Radio,RefreshCw,Trash2,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/date-picker */ \"(app-pages-browser)/./src/components/ui/date-picker.tsx\");\n/* harmony import */ var _components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/date-time-display */ \"(app-pages-browser)/./src/components/ui/date-time-display.tsx\");\n/* harmony import */ var _components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/date-filter-modal */ \"(app-pages-browser)/./src/components/ui/date-filter-modal.tsx\");\n/* harmony import */ var _lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils/date-time */ \"(app-pages-browser)/./src/lib/utils/date-time.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// import { BroadcastLinksModal } from '@/components/fixtures/BroadcastLinksModal';\n\n\n\n\nfunction FixturesPage() {\n    var _data_meta_totalItems, _data_meta, _data_data, _data_data1, _data_data2, _fixturesData_meta;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [limit, setLimit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(25);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Input value\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\"); // Actual search query for API\n    const [statusFilter, setStatusFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [leagueFilter, setLeagueFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixture, setSelectedFixture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedDate, setSelectedDate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [dateFilterModalOpen, setDateFilterModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { isAdmin, isEditor } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient)();\n    // Mock data for testing when API is down\n    const mockFixtures = {\n        data: [\n            {\n                id: 1,\n                homeTeamName: \"Manchester United\",\n                awayTeamName: \"Liverpool\",\n                homeTeamLogo: \"/images/teams/1.png\",\n                awayTeamLogo: \"/images/teams/2.png\",\n                date: \"2024-12-19T14:30:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Old Trafford\"\n            },\n            {\n                id: 2,\n                homeTeamName: \"Arsenal\",\n                awayTeamName: \"Chelsea\",\n                homeTeamLogo: \"/images/teams/3.png\",\n                awayTeamLogo: \"/images/teams/4.png\",\n                date: \"2024-12-20T16:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Premier League\",\n                venue: \"Emirates Stadium\"\n            },\n            {\n                id: 3,\n                homeTeamName: \"Barcelona\",\n                awayTeamName: \"Real Madrid\",\n                homeTeamLogo: \"/images/teams/5.png\",\n                awayTeamLogo: \"/images/teams/6.png\",\n                date: \"2024-12-21T20:00:00Z\",\n                status: \"LIVE\",\n                leagueName: \"La Liga\",\n                venue: \"Camp Nou\"\n            },\n            {\n                id: 4,\n                homeTeamName: \"Bayern Munich\",\n                awayTeamName: \"Borussia Dortmund\",\n                homeTeamLogo: \"/images/teams/7.png\",\n                awayTeamLogo: \"/images/teams/8.png\",\n                date: \"2024-12-18T18:30:00Z\",\n                status: \"FT\",\n                leagueName: \"Bundesliga\",\n                venue: \"Allianz Arena\"\n            },\n            {\n                id: 5,\n                homeTeamName: \"PSG\",\n                awayTeamName: \"Marseille\",\n                homeTeamLogo: \"/images/teams/9.png\",\n                awayTeamLogo: \"/images/teams/10.png\",\n                date: \"2024-12-22T21:00:00Z\",\n                status: \"NS\",\n                leagueName: \"Ligue 1\",\n                venue: \"Parc des Princes\"\n            }\n        ],\n        totalItems: 5,\n        totalPages: 1,\n        currentPage: 1,\n        limit: 25\n    };\n    // Fetch fixtures data with fallback to mock data\n    const { data, isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery)({\n        queryKey: [\n            \"fixtures\",\n            \"all\",\n            page,\n            limit,\n            searchQuery,\n            statusFilter,\n            leagueFilter,\n            selectedDate\n        ],\n        queryFn: ()=>{\n            const filters = {\n                page,\n                limit\n            };\n            // Add search query if provided\n            if (searchQuery && searchQuery.trim()) {\n                // For API, we might need to search by team names or other fields\n                // Since API doesn't have a generic search, we'll filter client-side for now\n                filters.search = searchQuery.trim();\n            }\n            if (statusFilter) filters.status = statusFilter;\n            if (leagueFilter) filters.league = leagueFilter;\n            if (selectedDate) filters.date = (0,_lib_utils_date_time__WEBPACK_IMPORTED_MODULE_13__.convertLocalDateToUTC)(selectedDate);\n            return _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getFixtures(filters);\n        },\n        staleTime: 30000,\n        retry: false,\n        onError: (error)=>{\n            console.log(\"API is down, using mock data:\", error.message);\n        }\n    });\n    // Use mock data if API fails or no data\n    const rawData = data || mockFixtures;\n    const isUsingMockData = !data;\n    // Apply client-side filtering for mock data when search is active\n    const fixturesData = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>{\n        if (!isUsingMockData || !searchQuery.trim()) {\n            return rawData;\n        }\n        // Filter mock data based on search query\n        const filteredData = rawData.data.filter((fixture)=>{\n            var _fixture_homeTeamName, _fixture_awayTeamName, _fixture_leagueName, _fixture_venue, _fixture_status;\n            const searchLower = searchQuery.toLowerCase();\n            return ((_fixture_homeTeamName = fixture.homeTeamName) === null || _fixture_homeTeamName === void 0 ? void 0 : _fixture_homeTeamName.toLowerCase().includes(searchLower)) || ((_fixture_awayTeamName = fixture.awayTeamName) === null || _fixture_awayTeamName === void 0 ? void 0 : _fixture_awayTeamName.toLowerCase().includes(searchLower)) || ((_fixture_leagueName = fixture.leagueName) === null || _fixture_leagueName === void 0 ? void 0 : _fixture_leagueName.toLowerCase().includes(searchLower)) || ((_fixture_venue = fixture.venue) === null || _fixture_venue === void 0 ? void 0 : _fixture_venue.toLowerCase().includes(searchLower)) || ((_fixture_status = fixture.status) === null || _fixture_status === void 0 ? void 0 : _fixture_status.toLowerCase().includes(searchLower));\n        });\n        return {\n            ...rawData,\n            data: filteredData,\n            meta: {\n                ...rawData.meta,\n                totalItems: filteredData.length,\n                totalPages: Math.ceil(filteredData.length / limit)\n            },\n            // For backward compatibility with mock structure\n            totalItems: filteredData.length,\n            totalPages: Math.ceil(filteredData.length / limit)\n        };\n    }, [\n        rawData,\n        searchQuery,\n        isUsingMockData,\n        limit\n    ]);\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation)({\n        mutationFn: (fixtureId)=>_lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.deleteFixture(fixtureId),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"fixtures\"\n                ]\n            });\n            console.log(\"Fixture deleted successfully\");\n            setDeleteModalOpen(false);\n            setSelectedFixture(null);\n        },\n        onError: (error)=>{\n            console.error(\"Failed to delete fixture:\", error.message);\n        }\n    });\n    // Define table columns\n    const columns = [\n        {\n            key: \"date\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Date & Time\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        className: \"h-4 w-4 text-gray-400\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this),\n            sortable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_time_display__WEBPACK_IMPORTED_MODULE_11__.DateTimeDisplay, {\n                        dateTime: value,\n                        showDate: true,\n                        showTime: true,\n                        isClickable: true,\n                        onClick: ()=>{\n                            const clickedDate = new Date(value);\n                            setSelectedDate(clickedDate);\n                            setDateFilterModalOpen(true);\n                        },\n                        className: \"min-w-[100px]\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"match\",\n            title: \"Match\",\n            sortable: false,\n            headerClassName: \"text-center\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center space-x-4 py-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-2 min-w-[80px]\",\n                            children: [\n                                row.homeTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"\".concat(\"http://172.31.213.61\", \"/\").concat(row.homeTeamLogo),\n                                    alt: row.homeTeamName,\n                                    className: \"w-8 h-8 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-center leading-tight max-w-[80px] break-words\",\n                                    children: row.homeTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 218,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"px-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-500 font-bold text-sm\",\n                                children: \"VS\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-center space-y-2 min-w-[80px]\",\n                            children: [\n                                row.awayTeamLogo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: \"\".concat(\"http://172.31.213.61\", \"/\").concat(row.awayTeamLogo),\n                                    alt: row.awayTeamName,\n                                    className: \"w-8 h-8 object-contain\",\n                                    onError: (e)=>{\n                                        e.currentTarget.style.display = \"none\";\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs font-medium text-center leading-tight max-w-[80px] break-words\",\n                                    children: row.awayTeamName\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"score\",\n            title: \"Score\",\n            align: \"center\",\n            render: (_, row)=>/*#__PURE__*/ {\n                var _row_goalsHome, _row_goalsAway;\n                return (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-bold text-lg\",\n                            children: [\n                                (_row_goalsHome = row.goalsHome) !== null && _row_goalsHome !== void 0 ? _row_goalsHome : \"-\",\n                                \" - \",\n                                (_row_goalsAway = row.goalsAway) !== null && _row_goalsAway !== void 0 ? _row_goalsAway : \"-\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: [\n                                \"HT: \",\n                                row.scoreHalftimeHome,\n                                \" - \",\n                                row.scoreHalftimeAway\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this);\n            }\n        },\n        {\n            key: \"status\",\n            title: \"Status\",\n            sortable: true,\n            filterable: true,\n            render: (value, row)=>{\n                const getStatusColor = (status)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                        case \"HT\":\n                            return \"bg-green-100 text-green-800\";\n                        case \"FT\":\n                            return \"bg-gray-100 text-gray-800\";\n                        case \"NS\":\n                            return \"bg-blue-100 text-blue-800\";\n                        case \"CANC\":\n                        case \"PST\":\n                            return \"bg-red-100 text-red-800\";\n                        default:\n                            return \"bg-yellow-100 text-yellow-800\";\n                    }\n                };\n                const getStatusText = (status, elapsed)=>{\n                    switch(status){\n                        case \"1H\":\n                        case \"2H\":\n                            return \"\".concat(elapsed, \"'\");\n                        case \"HT\":\n                            return \"Half Time\";\n                        case \"FT\":\n                            return \"Full Time\";\n                        case \"NS\":\n                            return \"Not Started\";\n                        case \"CANC\":\n                            return \"Cancelled\";\n                        case \"PST\":\n                            return \"Postponed\";\n                        default:\n                            return status;\n                    }\n                };\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                    className: getStatusColor(value),\n                    children: getStatusText(value, row.elapsed)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 320,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            key: \"leagueName\",\n            title: \"League\",\n            sortable: true,\n            filterable: true,\n            render: (value)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm text-gray-600\",\n                    children: value\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 332,\n                    columnNumber: 9\n                }, this)\n        },\n        {\n            key: \"actions\",\n            title: \"Actions\",\n            render: (_, row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex space-x-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Details\",\n                            onClick: ()=>handleViewFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 340,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Broadcast Links\",\n                            onClick: ()=>handleBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 354,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"View Broadcast Links\",\n                            onClick: ()=>handleViewBroadcastLinks(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Edit\",\n                            onClick: ()=>handleEditFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this),\n                        isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            size: \"sm\",\n                            variant: \"outline\",\n                            title: \"Delete\",\n                            onClick: ()=>handleDeleteFixture(row),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 339,\n                    columnNumber: 9\n                }, this)\n        }\n    ];\n    // Handler functions\n    const handleSearch = ()=>{\n        setSearchQuery(searchInput.trim());\n        setPage(1); // Reset to first page when searching\n    };\n    const handleSearchKeyPress = (e)=>{\n        if (e.key === \"Enter\") {\n            handleSearch();\n        }\n    };\n    const handleClearSearch = ()=>{\n        setSearchInput(\"\");\n        setSearchQuery(\"\");\n        setPage(1);\n    };\n    const handleViewFixture = (fixture)=>{\n        // Navigate to fixture detail page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id), \"_blank\");\n    };\n    const handleEditFixture = (fixture)=>{\n        // Navigate to edit page\n        window.open(\"/dashboard/fixtures/\".concat(fixture.id, \"/edit\"), \"_blank\");\n    };\n    const handleDeleteFixture = (fixture)=>{\n        setSelectedFixture(fixture);\n        setDeleteModalOpen(true);\n    };\n    const handleBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const handleViewBroadcastLinks = (fixture)=>{\n        setSelectedFixtureForBroadcast(fixture);\n        setBroadcastLinksModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        if (selectedFixture) {\n            deleteMutation.mutate(selectedFixture.id);\n        }\n    };\n    const handleBulkSync = async ()=>{\n        try {\n            console.log(\"Starting fixtures sync...\");\n            // This would call the sync API\n            // await fixturesApi.syncFixtures();\n            console.log(\"Fixtures sync completed\");\n            refetch();\n        } catch (error) {\n            console.error(\"Sync failed:\", error.message);\n        }\n    };\n    // Date filter handlers\n    const handleApplyDateFilter = (date)=>{\n        setSelectedDate(date);\n        setPage(1); // Reset to first page when filtering\n    };\n    const handleResetDateFilter = ()=>{\n        setSelectedDate(undefined);\n        setPage(1); // Reset to first page when clearing filter\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-gray-900\",\n                            children: \"Fixtures Management\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 465,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 mt-1\",\n                            children: \"Manage football fixtures and match data\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 464,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"p-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-600 mb-4\",\n                                    children: \"Failed to load fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>refetch(),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try Again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                    lineNumber: 469,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n            lineNumber: 463,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-900\",\n                                children: \"Fixtures Management\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-1\",\n                                children: \"Manage football fixtures and match data\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>refetch(),\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(isLoading ? \"animate-spin\" : \"\")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 11\n                            }, this),\n                            isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleBulkSync,\n                                disabled: isLoading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sync Data\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>{\n                                    // Export functionality\n                                    console.log(\"Export feature coming soon\");\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 521,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this),\n                            isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>window.open(\"/dashboard/fixtures/create\", \"_blank\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Add Fixture\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_meta = data.meta) === null || _data_meta === void 0 ? void 0 : (_data_meta_totalItems = _data_meta.totalItems) === null || _data_meta_totalItems === void 0 ? void 0 : _data_meta_totalItems.toLocaleString()) || \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 538,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Total Fixtures\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 541,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 537,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.filter((f)=>[\n                                            \"1H\",\n                                            \"2H\",\n                                            \"HT\"\n                                        ].includes(f.status)).length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Live Matches\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 550,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 545,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-yellow-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data1 = data.data) === null || _data_data1 === void 0 ? void 0 : _data_data1.filter((f)=>f.status === \"NS\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Upcoming\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 554,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-gray-600\",\n                                    children: (data === null || data === void 0 ? void 0 : (_data_data2 = data.data) === null || _data_data2 === void 0 ? void 0 : _data_data2.filter((f)=>f.status === \"FT\").length) || 0\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 565,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"Completed\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 568,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 563,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 535,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"All Fixtures\",\n                                                isUsingMockData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded-full font-normal\",\n                                                    children: \"Demo Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 578,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                            children: isUsingMockData ? \"Showing demo data - API backend is not available\" : \"Complete list of football fixtures with real-time updates\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_picker__WEBPACK_IMPORTED_MODULE_10__.DatePicker, {\n                                            date: selectedDate,\n                                            onDateChange: setSelectedDate,\n                                            placeholder: \"Filter by date\",\n                                            className: \"w-[200px]\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 595,\n                                            columnNumber: 15\n                                        }, this),\n                                        selectedDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            size: \"sm\",\n                                            onClick: ()=>setSelectedDate(undefined),\n                                            className: \"px-2\",\n                                            title: \"Clear date filter\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Download_Edit_Eye_List_Plus_Radio_RefreshCw_Trash2_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.TableSkeleton, {\n                            rows: 10,\n                            columns: 7\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 617,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_5__.DataTable, {\n                            data: (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.data) || [],\n                            columns: columns,\n                            loading: isLoading && !isUsingMockData,\n                            searchable: true,\n                            searchPlaceholder: \"Search fixtures...\",\n                            onSearch: handleSearch,\n                            pagination: {\n                                page,\n                                limit,\n                                total: (fixturesData === null || fixturesData === void 0 ? void 0 : (_fixturesData_meta = fixturesData.meta) === null || _fixturesData_meta === void 0 ? void 0 : _fixturesData_meta.totalItems) || (fixturesData === null || fixturesData === void 0 ? void 0 : fixturesData.totalItems) || 0,\n                                onPageChange: setPage,\n                                onLimitChange: (newLimit)=>{\n                                    setLimit(newLimit);\n                                    setPage(1); // Reset to first page when changing limit\n                                }\n                            },\n                            emptyMessage: \"No fixtures found\",\n                            error: error && !isUsingMockData ? error : null\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 574,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_6__.ConfirmModal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>{\n                    setDeleteModalOpen(false);\n                    setSelectedFixture(null);\n                },\n                onConfirm: confirmDelete,\n                title: \"Delete Fixture\",\n                message: selectedFixture ? 'Are you sure you want to delete the fixture \"'.concat(selectedFixture.homeTeamName, \" vs \").concat(selectedFixture.awayTeamName, '\"? This action cannot be undone.') : \"Are you sure you want to delete this fixture?\",\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                variant: \"destructive\",\n                loading: deleteMutation.isPending\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 644,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_date_filter_modal__WEBPACK_IMPORTED_MODULE_12__.DateFilterModal, {\n                isOpen: dateFilterModalOpen,\n                onClose: ()=>setDateFilterModalOpen(false),\n                selectedDate: selectedDate,\n                onDateSelect: setSelectedDate,\n                onApplyFilter: handleApplyDateFilter,\n                onResetFilter: handleResetDateFilter\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n                lineNumber: 676,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/fixtures/page.tsx\",\n        lineNumber: 485,\n        columnNumber: 5\n    }, this);\n}\n_s(FixturesPage, \"I3nI8t96W3QSaTr1m7GNDLV7lbA=\", false, function() {\n    return [\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_14__.useQueryClient,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_15__.useQuery,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_16__.useMutation\n    ];\n});\n_c = FixturesPage;\nvar _c;\n$RefreshReg$(_c, \"FixturesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/fixtures/page.tsx\n"));

/***/ })

});