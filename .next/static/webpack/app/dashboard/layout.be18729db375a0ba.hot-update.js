"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/lib/api/auth.ts":
/*!*****************************!*\
  !*** ./src/lib/api/auth.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: function() { return /* binding */ authApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst authApi = {\n    // System Authentication\n    login: async (credentials)=>{\n        try {\n            console.log(\"\\uD83D\\uDD10 Attempting API login...\");\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/login\", credentials);\n            console.log(\"✅ API login successful\");\n            return response;\n        } catch (error) {\n            console.warn(\"⚠️ API login failed, using mock data for development:\", error.message);\n            // Mock response for development\n            if (credentials.username === \"admin\" && credentials.password === \"admin123456\") {\n                const mockResponse = {\n                    user: {\n                        id: 1,\n                        username: \"admin\",\n                        email: \"<EMAIL>\",\n                        fullName: \"System Administrator\",\n                        role: \"admin\",\n                        isActive: true,\n                        lastLoginAt: new Date().toISOString(),\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    },\n                    accessToken: \"mock-access-token-\" + Date.now(),\n                    refreshToken: \"mock-refresh-token-\" + Date.now()\n                };\n                // Simulate API delay\n                await new Promise((resolve)=>setTimeout(resolve, 500));\n                return mockResponse;\n            } else {\n                throw new Error(\"Invalid credentials. Try admin/admin123456 for demo.\");\n            }\n        }\n    },\n    logout: async (refreshToken)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/logout\", {\n            refreshToken\n        });\n        return response;\n    },\n    logoutFromAllDevices: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/logout-all\");\n        return response;\n    },\n    refreshToken: async (refreshToken)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/refresh\", {\n            refreshToken\n        });\n        return response;\n    },\n    getProfile: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/system-auth/profile\");\n        return response;\n    },\n    updateProfile: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/system-auth/profile\", data);\n        return response;\n    },\n    changePassword: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/change-password\", data);\n        return response;\n    },\n    // System User Management (Admin only)\n    createUser: async (userData)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/users\", userData);\n        return response;\n    },\n    updateUser: async (id, data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/system-auth/users/\".concat(id), data);\n        return response;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/auth.ts\n"));

/***/ })

});