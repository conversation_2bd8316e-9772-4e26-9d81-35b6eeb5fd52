"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/api-test/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/api-test/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/api-test/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ApiTestPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _lib_api_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/auth */ \"(app-pages-browser)/./src/lib/api/auth.ts\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ApiTestPage() {\n    _s();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateResult = (endpoint, result)=>{\n        setTestResults((prev)=>{\n            const existing = prev.find((r)=>r.endpoint === endpoint);\n            if (existing) {\n                return prev.map((r)=>r.endpoint === endpoint ? {\n                        ...r,\n                        ...result\n                    } : r);\n            } else {\n                return [\n                    ...prev,\n                    {\n                        endpoint,\n                        status: \"pending\",\n                        ...result\n                    }\n                ];\n            }\n        });\n    };\n    const testEndpoint = async (name, testFn)=>{\n        const startTime = Date.now();\n        updateResult(name, {\n            status: \"pending\"\n        });\n        try {\n            const data = await testFn();\n            const duration = Date.now() - startTime;\n            updateResult(name, {\n                status: \"success\",\n                data: data,\n                duration\n            });\n        } catch (error) {\n            const duration = Date.now() - startTime;\n            updateResult(name, {\n                status: \"error\",\n                error: error.message || \"Unknown error\",\n                duration\n            });\n        }\n    };\n    const runAllTests = async ()=>{\n        setIsRunning(true);\n        setTestResults([]);\n        // Test 1: API Documentation\n        await testEndpoint(\"API Documentation\", async ()=>{\n            var _response_info, _response_info1;\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_5__.apiClient.get(\"/api-docs-json\");\n            return {\n                title: ((_response_info = response.info) === null || _response_info === void 0 ? void 0 : _response_info.title) || \"APISportsGame API\",\n                version: ((_response_info1 = response.info) === null || _response_info1 === void 0 ? void 0 : _response_info1.version) || \"1.0.0\",\n                endpoints: Object.keys(response.paths || {}).length\n            };\n        });\n        // Test 2: Public Fixtures\n        await testEndpoint(\"Public Fixtures\", async ()=>{\n            var _response_data, _response_data1, _response_data2, _response_data_, _response_data3, _response_data_1, _response_data4;\n            const response = await _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesApi.getUpcomingAndLive({\n                limit: 3\n            });\n            return {\n                totalFixtures: ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.length) || 0,\n                liveMatches: ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.filter((f)=>[\n                        \"1H\",\n                        \"2H\",\n                        \"HT\"\n                    ].includes(f.status)).length) || 0,\n                upcomingMatches: ((_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.filter((f)=>f.status === \"NS\").length) || 0,\n                sampleFixture: ((_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : (_response_data_ = _response_data3[0]) === null || _response_data_ === void 0 ? void 0 : _response_data_.homeTeamName) + \" vs \" + ((_response_data4 = response.data) === null || _response_data4 === void 0 ? void 0 : (_response_data_1 = _response_data4[0]) === null || _response_data_1 === void 0 ? void 0 : _response_data_1.awayTeamName) || \"No fixtures\"\n            };\n        });\n        // Test 3: Public Leagues\n        await testEndpoint(\"Public Leagues\", async ()=>{\n            var _response_meta, _response_meta1, _response_data_, _response_data;\n            const response = await _lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__.leaguesApi.getLeagues({\n                limit: 3\n            });\n            return {\n                totalLeagues: ((_response_meta = response.meta) === null || _response_meta === void 0 ? void 0 : _response_meta.totalItems) || 0,\n                currentPage: ((_response_meta1 = response.meta) === null || _response_meta1 === void 0 ? void 0 : _response_meta1.currentPage) || 1,\n                sampleLeague: ((_response_data = response.data) === null || _response_data === void 0 ? void 0 : (_response_data_ = _response_data[0]) === null || _response_data_ === void 0 ? void 0 : _response_data_.name) || \"No leagues\"\n            };\n        });\n        // Test 4: System Auth Login (Test credentials)\n        await testEndpoint(\"System Auth Login\", async ()=>{\n            const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_6__.authApi.login({\n                username: \"admin\",\n                password: \"admin123456\"\n            });\n            return {\n                username: response.user.username,\n                role: response.user.role,\n                email: response.user.email,\n                tokenLength: response.accessToken.length\n            };\n        });\n        setIsRunning(false);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"success\":\n                return \"bg-green-100 text-green-800\";\n            case \"error\":\n                return \"bg-red-100 text-red-800\";\n            case \"pending\":\n                return \"bg-yellow-100 text-yellow-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"success\":\n                return \"✅\";\n            case \"error\":\n                return \"❌\";\n            case \"pending\":\n                return \"⏳\";\n            default:\n                return \"⚪\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"API Connection Test\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Test connection to APISportsGame API endpoints\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"API Configuration\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Current API settings and connection details\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Base URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"http://localhost:3000\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Timeout:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"30 seconds\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Auth Token:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children:  true && localStorage.getItem(\"accessToken\") ? \"✅ Present\" : \"❌ Not found\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Test Results\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"API endpoint connectivity tests\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: runAllTests,\n                                    disabled: isRunning,\n                                    className: \"w-full\",\n                                    children: isRunning ? \"Running Tests...\" : \"Run All Tests\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: getStatusIcon(result.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: result.endpoint\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                                    lineNumber: 190,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    className: getStatusColor(result.status),\n                                                                    children: result.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                                    lineNumber: 193,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                result.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        result.duration,\n                                                                        \"ms\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                                    lineNumber: 197,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 21\n                                                }, this),\n                                                result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-50 border border-red-200 rounded p-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-800 text-sm font-medium\",\n                                                            children: \"Error:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-700 text-sm\",\n                                                            children: result.error\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 23\n                                                }, this),\n                                                result.data && result.status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 border border-green-200 rounded p-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-green-800 text-sm font-medium\",\n                                                            children: \"Response:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-green-700 text-xs mt-1 overflow-x-auto\",\n                                                            children: [\n                                                                JSON.stringify(result.data, null, 2).substring(0, 200),\n                                                                JSON.stringify(result.data, null, 2).length > 200 ? \"...\" : \"\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiTestPage, \"MISEoYXl+xEb/JgGuAnLoONnNLA=\");\n_c = ApiTestPage;\nvar _c;\n$RefreshReg$(_c, \"ApiTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/api-test/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/auth.ts":
/*!*****************************!*\
  !*** ./src/lib/api/auth.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authApi: function() { return /* binding */ authApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst authApi = {\n    // System Authentication\n    login: async (credentials)=>{\n        console.log(\"\\uD83D\\uDD10 Attempting API login...\");\n        try {\n            const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/login\", credentials);\n            console.log(\"✅ API login successful\");\n            // Get user profile with the token\n            const userProfile = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/system-auth/profile\", {\n                headers: {\n                    Authorization: \"Bearer \".concat(response.accessToken)\n                }\n            });\n            return {\n                user: userProfile,\n                accessToken: response.accessToken,\n                refreshToken: response.refreshToken\n            };\n        } catch (error) {\n            console.error(\"❌ API login failed:\", error.message);\n            // Only use mock as absolute fallback\n            if (error.code === \"ECONNREFUSED\" || error.code === \"NETWORK_ERROR\") {\n                console.warn(\"⚠️ API server not available, using mock data\");\n                if (credentials.username === \"admin\" && credentials.password === \"admin123456\") {\n                    const mockResponse = {\n                        user: {\n                            id: 1,\n                            username: \"admin\",\n                            email: \"<EMAIL>\",\n                            fullName: \"System Administrator\",\n                            role: \"admin\",\n                            isActive: true,\n                            lastLoginAt: new Date().toISOString(),\n                            createdAt: new Date().toISOString(),\n                            updatedAt: new Date().toISOString()\n                        },\n                        accessToken: \"mock-access-token-\" + Date.now(),\n                        refreshToken: \"mock-refresh-token-\" + Date.now()\n                    };\n                    await new Promise((resolve)=>setTimeout(resolve, 500));\n                    return mockResponse;\n                }\n            }\n            // Re-throw API errors (invalid credentials, etc.)\n            throw error;\n        }\n    },\n    logout: async (refreshToken)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/logout\", {\n            refreshToken\n        });\n        return response;\n    },\n    logoutFromAllDevices: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/logout-all\");\n        return response;\n    },\n    refreshToken: async (refreshToken)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/refresh\", {\n            refreshToken\n        });\n        return response;\n    },\n    getProfile: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/system-auth/profile\");\n        return response;\n    },\n    updateProfile: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/system-auth/profile\", data);\n        return response;\n    },\n    changePassword: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/change-password\", data);\n        return response;\n    },\n    // System User Management (Admin only)\n    createUser: async (userData)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/system-auth/users\", userData);\n        return response;\n    },\n    updateUser: async (id, data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.put(\"/system-auth/users/\".concat(id), data);\n        return response;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/auth.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/fixtures.ts":
/*!*********************************!*\
  !*** ./src/lib/api/fixtures.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fixturesApi: function() { return /* binding */ fixturesApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst fixturesApi = {\n    // Public endpoints\n    getFixtures: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures?\".concat(params.toString()));\n        return response;\n    },\n    getFixtureById: async (externalId)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/\".concat(externalId));\n        return response;\n    },\n    // Upcoming and Live fixtures (Public)\n    getUpcomingAndLive: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/upcoming-and-live?\".concat(params.toString()));\n        return response;\n    },\n    // Team schedule (Requires auth)\n    getTeamSchedule: async function(teamId) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/schedules/\".concat(teamId, \"?\").concat(params.toString()));\n        return response;\n    },\n    // Fixture statistics (Requires auth)\n    getFixtureStatistics: async (externalId)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/statistics/\".concat(externalId));\n        return response;\n    },\n    // Admin only - Sync operations\n    triggerSeasonSync: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/sync/fixtures\");\n        return response;\n    },\n    triggerDailySync: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/sync/daily\");\n        return response;\n    },\n    // Editor+ - Sync status\n    getSyncStatus: async ()=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/fixtures/sync/status\");\n        return response;\n    },\n    // CRUD operations (if needed)\n    createFixture: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/football/fixtures\", data);\n        return response;\n    },\n    updateFixture: async (externalId, data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(\"/football/fixtures/\".concat(externalId), data);\n        return response;\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/fixtures.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api/leagues.ts":
/*!********************************!*\
  !*** ./src/lib/api/leagues.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   leaguesApi: function() { return /* binding */ leaguesApi; }\n/* harmony export */ });\n/* harmony import */ var _client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n\nconst leaguesApi = {\n    // Public endpoint\n    getLeagues: async function() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        const params = new URLSearchParams();\n        Object.entries(filters).forEach((param)=>{\n            let [key, value] = param;\n            if (value !== undefined) {\n                params.append(key, value.toString());\n            }\n        });\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/leagues?\".concat(params.toString()));\n        return response;\n    },\n    // Requires authentication\n    getLeagueById: async (externalId, season)=>{\n        const params = season ? \"?season=\".concat(season) : \"\";\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.get(\"/football/leagues/\".concat(externalId).concat(params));\n        return response;\n    },\n    // Editor+ access required\n    createLeague: async (data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.post(\"/football/leagues\", data);\n        return response;\n    },\n    // Editor+ access required\n    updateLeague: async (id, data)=>{\n        const response = await _client__WEBPACK_IMPORTED_MODULE_0__.apiClient.patch(\"/football/leagues/\".concat(id), data);\n        return response;\n    },\n    // Helper methods for common operations\n    getActiveLeagues: async ()=>{\n        return leaguesApi.getLeagues({\n            active: true\n        });\n    },\n    getLeaguesByCountry: async (country)=>{\n        return leaguesApi.getLeagues({\n            country\n        });\n    },\n    toggleLeagueStatus: async (id, active)=>{\n        return leaguesApi.updateLeague(id, {\n            active\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/leagues.ts\n"));

/***/ })

});