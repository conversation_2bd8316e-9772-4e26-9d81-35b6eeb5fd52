"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/api-test/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/api-test/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/dashboard/api-test/page.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ApiTestPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/client */ \"(app-pages-browser)/./src/lib/api/client.ts\");\n/* harmony import */ var _lib_api_auth__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api/auth */ \"(app-pages-browser)/./src/lib/api/auth.ts\");\n/* harmony import */ var _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api/fixtures */ \"(app-pages-browser)/./src/lib/api/fixtures.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ApiTestPage() {\n    _s();\n    const [testResults, setTestResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isRunning, setIsRunning] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const updateResult = (endpoint, result)=>{\n        setTestResults((prev)=>{\n            const existing = prev.find((r)=>r.endpoint === endpoint);\n            if (existing) {\n                return prev.map((r)=>r.endpoint === endpoint ? {\n                        ...r,\n                        ...result\n                    } : r);\n            } else {\n                return [\n                    ...prev,\n                    {\n                        endpoint,\n                        status: \"pending\",\n                        ...result\n                    }\n                ];\n            }\n        });\n    };\n    const testEndpoint = async (name, testFn)=>{\n        const startTime = Date.now();\n        updateResult(name, {\n            status: \"pending\"\n        });\n        try {\n            const data = await testFn();\n            const duration = Date.now() - startTime;\n            updateResult(name, {\n                status: \"success\",\n                data: data,\n                duration\n            });\n        } catch (error) {\n            const duration = Date.now() - startTime;\n            updateResult(name, {\n                status: \"error\",\n                error: error.message || \"Unknown error\",\n                duration\n            });\n        }\n    };\n    const runAllTests = async ()=>{\n        setIsRunning(true);\n        setTestResults([]);\n        // Test 1: API Documentation\n        await testEndpoint(\"API Documentation\", async ()=>{\n            var _response_info, _response_info1;\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_5__.apiClient.get(\"/api-docs-json\");\n            return {\n                title: ((_response_info = response.info) === null || _response_info === void 0 ? void 0 : _response_info.title) || \"APISportsGame API\",\n                version: ((_response_info1 = response.info) === null || _response_info1 === void 0 ? void 0 : _response_info1.version) || \"1.0.0\",\n                endpoints: Object.keys(response.paths || {}).length\n            };\n        });\n        // Test 2: Public Fixtures\n        await testEndpoint(\"Public Fixtures\", async ()=>{\n            const response = await _lib_api_fixtures__WEBPACK_IMPORTED_MODULE_7__.fixturesService.getUpcomingAndLive({\n                limit: 5\n            });\n            return response;\n        });\n        // Test 3: Public Leagues\n        await testEndpoint(\"Public Leagues\", async ()=>{\n            const response = await _lib_api_leagues__WEBPACK_IMPORTED_MODULE_8__.leaguesService.getLeagues({\n                limit: 5\n            });\n            return response;\n        });\n        // Test 4: System Auth Login\n        await testEndpoint(\"System Auth Login\", async ()=>{\n            const response = await _lib_api_auth__WEBPACK_IMPORTED_MODULE_6__.authService.login({\n                username: \"admin\",\n                password: \"admin123456\"\n            });\n            return response;\n        });\n        // Test 5: API Documentation\n        await testEndpoint(\"API Documentation\", async ()=>{\n            var _response_info, _response_info1;\n            const response = await _lib_api_client__WEBPACK_IMPORTED_MODULE_5__.apiClient.get(\"/api-docs-json\");\n            return {\n                title: (_response_info = response.info) === null || _response_info === void 0 ? void 0 : _response_info.title,\n                version: (_response_info1 = response.info) === null || _response_info1 === void 0 ? void 0 : _response_info1.version\n            };\n        });\n        setIsRunning(false);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"success\":\n                return \"bg-green-100 text-green-800\";\n            case \"error\":\n                return \"bg-red-100 text-red-800\";\n            case \"pending\":\n                return \"bg-yellow-100 text-yellow-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"success\":\n                return \"✅\";\n            case \"error\":\n                return \"❌\";\n            case \"pending\":\n                return \"⏳\";\n            default:\n                return \"⚪\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"API Connection Test\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mt-1\",\n                        children: \"Test connection to APISportsGame API endpoints\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"API Configuration\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Current API settings and connection details\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Base URL:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"http://localhost:3000\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Timeout:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children: \"30 seconds\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"Auth Token:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-600\",\n                                            children:  true && localStorage.getItem(\"accessToken\") ? \"✅ Present\" : \"❌ Not found\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"Test Results\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"API endpoint connectivity tests\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: runAllTests,\n                                    disabled: isRunning,\n                                    className: \"w-full\",\n                                    children: isRunning ? \"Running Tests...\" : \"Run All Tests\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                testResults.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg\",\n                                                                    children: getStatusIcon(result.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                                    lineNumber: 181,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: result.endpoint\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                                    lineNumber: 182,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                    className: getStatusColor(result.status),\n                                                                    children: result.status\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                                    lineNumber: 185,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                result.duration && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        result.duration,\n                                                                        \"ms\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 21\n                                                }, this),\n                                                result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-red-50 border border-red-200 rounded p-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-800 text-sm font-medium\",\n                                                            children: \"Error:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-red-700 text-sm\",\n                                                            children: result.error\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 23\n                                                }, this),\n                                                result.data && result.status === \"success\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-50 border border-green-200 rounded p-2 mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-green-800 text-sm font-medium\",\n                                                            children: \"Response:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"text-green-700 text-xs mt-1 overflow-x-auto\",\n                                                            children: [\n                                                                JSON.stringify(result.data, null, 2).substring(0, 200),\n                                                                JSON.stringify(result.data, null, 2).length > 200 ? \"...\" : \"\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n                lineNumber: 158,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/api-test/page.tsx\",\n        lineNumber: 120,\n        columnNumber: 5\n    }, this);\n}\n_s(ApiTestPage, \"MISEoYXl+xEb/JgGuAnLoONnNLA=\");\n_c = ApiTestPage;\nvar _c;\n$RefreshReg$(_c, \"ApiTestPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/api-test/page.tsx\n"));

/***/ })

});