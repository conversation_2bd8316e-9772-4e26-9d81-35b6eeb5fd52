[{"name": "generate-buildid", "duration": 149, "timestamp": 111756063385, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748168492713, "traceId": "5050abd5c1619112"}, {"name": "load-custom-routes", "duration": 109, "timestamp": 111756063617, "id": 5, "parentId": 1, "tags": {}, "startTime": 1748168492713, "traceId": "5050abd5c1619112"}, {"name": "create-pages-mapping", "duration": 97, "timestamp": 111756123215, "id": 6, "parentId": 1, "tags": {}, "startTime": 1748168492773, "traceId": "5050abd5c1619112"}, {"name": "collect-app-paths", "duration": 1421, "timestamp": 111756123333, "id": 7, "parentId": 1, "tags": {}, "startTime": 1748168492773, "traceId": "5050abd5c1619112"}, {"name": "create-app-mapping", "duration": 601, "timestamp": 111756124802, "id": 8, "parentId": 1, "tags": {}, "startTime": 1748168492774, "traceId": "5050abd5c1619112"}, {"name": "public-dir-conflict-check", "duration": 452, "timestamp": 111756125563, "id": 9, "parentId": 1, "tags": {}, "startTime": 1748168492775, "traceId": "5050abd5c1619112"}, {"name": "generate-routes-manifest", "duration": 875, "timestamp": 111756126129, "id": 10, "parentId": 1, "tags": {}, "startTime": 1748168492776, "traceId": "5050abd5c1619112"}, {"name": "create-dist-dir", "duration": 163, "timestamp": 111756127283, "id": 11, "parentId": 1, "tags": {}, "startTime": 1748168492777, "traceId": "5050abd5c1619112"}, {"name": "write-routes-manifest", "duration": 334, "timestamp": 111756129427, "id": 12, "parentId": 1, "tags": {}, "startTime": 1748168492779, "traceId": "5050abd5c1619112"}, {"name": "generate-required-server-files", "duration": 177, "timestamp": 111756130109, "id": 13, "parentId": 1, "tags": {}, "startTime": 1748168492780, "traceId": "5050abd5c1619112"}, {"name": "next-build", "duration": 1121943, "timestamp": 111756016837, "id": 1, "tags": {"buildMode": "default", "isTurboBuild": "false", "version": "14.1.4", "isTurbopack": false, "has-custom-webpack-config": "false", "use-build-worker": "true"}, "startTime": 1748168492666, "traceId": "5050abd5c1619112"}]