'use client';

import { useAuth } from '@/lib/hooks/useAuth';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Trophy, Users, Radio, Shield, Clock } from 'lucide-react';

export default function DashboardPage() {
  const { user } = useAuth();
  const { isAdmin, isEditor, isModerator } = usePermissions();

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'editor':
        return 'bg-blue-100 text-blue-800';
      case 'moderator':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const quickStats = [
    {
      title: 'Total Fixtures',
      value: '1,234',
      icon: Calendar,
      description: 'Active fixtures in database',
    },
    {
      title: 'Active Leagues',
      value: '45',
      icon: Trophy,
      description: 'Currently active leagues',
    },
    {
      title: 'Teams',
      value: '890',
      icon: Users,
      description: 'Teams in database',
    },
    {
      title: 'Broadcast Links',
      value: '156',
      icon: Radio,
      description: 'Active broadcast links',
    },
  ];

  const recentActivities = [
    {
      action: 'Fixture sync completed',
      time: '2 minutes ago',
      type: 'sync',
    },
    {
      action: 'New broadcast link added',
      time: '15 minutes ago',
      type: 'broadcast',
    },
    {
      action: 'League updated: Premier League',
      time: '1 hour ago',
      type: 'league',
    },
    {
      action: 'User tier upgraded',
      time: '2 hours ago',
      type: 'user',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {getWelcomeMessage()}, {user?.fullName || user?.username}!
            </h1>
            <p className="text-gray-600 mt-1">
              Welcome to the APISportsGame Content Management System
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Badge className={`${getRoleColor(user?.role || '')} flex items-center space-x-1`}>
              <Shield className="h-3 w-3" />
              <span className="capitalize">{user?.role}</span>
            </Badge>
            <div className="text-right text-sm text-gray-500">
              <p>Last login</p>
              <p className="font-medium">
                {user?.lastLoginAt 
                  ? new Date(user.lastLoginAt).toLocaleDateString()
                  : 'First time'
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
                  </div>
                  <div className="bg-blue-100 p-3 rounded-full">
                    <Icon className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="mr-2 h-5 w-5" />
              Recent Activities
            </CardTitle>
            <CardDescription>
              Latest system activities and updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.action}
                    </p>
                    <p className="text-xs text-gray-500">{activity.time}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common tasks and shortcuts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <button className="w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium">View Live Fixtures</p>
                    <p className="text-sm text-gray-500">Check ongoing matches</p>
                  </div>
                </div>
              </button>
              
              {isEditor() && (
                <button className="w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3">
                    <Radio className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="font-medium">Add Broadcast Link</p>
                      <p className="text-sm text-gray-500">Add new streaming link</p>
                    </div>
                  </div>
                </button>
              )}
              
              {isAdmin() && (
                <button className="w-full text-left p-3 rounded-lg border hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-3">
                    <Users className="h-5 w-5 text-purple-600" />
                    <div>
                      <p className="font-medium">Manage Users</p>
                      <p className="text-sm text-gray-500">User administration</p>
                    </div>
                  </div>
                </button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
