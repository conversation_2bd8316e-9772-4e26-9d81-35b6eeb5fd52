'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { apiClient } from '@/lib/api/client';
import { authApi } from '@/lib/api/auth';
import { fixturesApi } from '@/lib/api/fixtures';
import { leaguesApi } from '@/lib/api/leagues';

interface TestResult {
  endpoint: string;
  status: 'pending' | 'success' | 'error';
  data?: any;
  error?: string;
  duration?: number;
}

export default function ApiTestPage() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const updateResult = (endpoint: string, result: Partial<TestResult>) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.endpoint === endpoint);
      if (existing) {
        return prev.map(r => r.endpoint === endpoint ? { ...r, ...result } : r);
      } else {
        return [...prev, { endpoint, status: 'pending', ...result }];
      }
    });
  };

  const testEndpoint = async (name: string, testFn: () => Promise<any>) => {
    const startTime = Date.now();
    updateResult(name, { status: 'pending' });

    try {
      const data = await testFn();
      const duration = Date.now() - startTime;
      updateResult(name, {
        status: 'success',
        data: data,
        duration
      });
    } catch (error: any) {
      const duration = Date.now() - startTime;
      updateResult(name, {
        status: 'error',
        error: error.message || 'Unknown error',
        duration
      });
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    // Test 1: API Documentation
    await testEndpoint('API Documentation', async () => {
      const response = await apiClient.get('/api-docs-json');
      return {
        title: response.info?.title || 'APISportsGame API',
        version: response.info?.version || '1.0.0',
        endpoints: Object.keys(response.paths || {}).length
      };
    });

    // Test 2: Public Fixtures
    await testEndpoint('Public Fixtures', async () => {
      const response = await fixturesApi.getUpcomingAndLive({ limit: 3 });
      return {
        totalFixtures: response.data?.length || 0,
        liveMatches: response.data?.filter(f => ['1H', '2H', 'HT'].includes(f.status)).length || 0,
        upcomingMatches: response.data?.filter(f => f.status === 'NS').length || 0,
        sampleFixture: response.data?.[0]?.homeTeamName + ' vs ' + response.data?.[0]?.awayTeamName || 'No fixtures'
      };
    });

    // Test 3: Public Leagues
    await testEndpoint('Public Leagues', async () => {
      const response = await leaguesApi.getLeagues({ limit: 3 });
      return {
        totalLeagues: response.meta?.totalItems || 0,
        currentPage: response.meta?.currentPage || 1,
        sampleLeague: response.data?.[0]?.name || 'No leagues'
      };
    });

    // Test 4: System Auth Login (Test credentials)
    await testEndpoint('System Auth Login', async () => {
      const response = await authApi.login({
        username: 'admin',
        password: 'admin123456'
      });
      return {
        username: response.user.username,
        role: response.user.role,
        email: response.user.email,
        tokenLength: response.accessToken.length
      };
    });

    setIsRunning(false);
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      case 'pending': return '⏳';
      default: return '⚪';
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">API Connection Test</h1>
        <p className="text-gray-600 mt-1">
          Test connection to APISportsGame API endpoints
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>API Configuration</CardTitle>
          <CardDescription>
            Current API settings and connection details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="font-medium">Base URL:</span>
              <span className="text-gray-600">http://localhost:3000</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Timeout:</span>
              <span className="text-gray-600">30 seconds</span>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Auth Token:</span>
              <span className="text-gray-600">
                {typeof window !== 'undefined' && localStorage.getItem('accessToken')
                  ? '✅ Present'
                  : '❌ Not found'
                }
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
          <CardDescription>
            API endpoint connectivity tests
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button
              onClick={runAllTests}
              disabled={isRunning}
              className="w-full"
            >
              {isRunning ? 'Running Tests...' : 'Run All Tests'}
            </Button>

            {testResults.length > 0 && (
              <div className="space-y-3">
                {testResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getStatusIcon(result.status)}</span>
                        <span className="font-medium">{result.endpoint}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor(result.status)}>
                          {result.status}
                        </Badge>
                        {result.duration && (
                          <span className="text-sm text-gray-500">
                            {result.duration}ms
                          </span>
                        )}
                      </div>
                    </div>

                    {result.error && (
                      <div className="bg-red-50 border border-red-200 rounded p-2 mt-2">
                        <p className="text-red-800 text-sm font-medium">Error:</p>
                        <p className="text-red-700 text-sm">{result.error}</p>
                      </div>
                    )}

                    {result.data && result.status === 'success' && (
                      <div className="bg-green-50 border border-green-200 rounded p-2 mt-2">
                        <p className="text-green-800 text-sm font-medium">Response:</p>
                        <pre className="text-green-700 text-xs mt-1 overflow-x-auto">
                          {JSON.stringify(result.data, null, 2).substring(0, 200)}
                          {JSON.stringify(result.data, null, 2).length > 200 ? '...' : ''}
                        </pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
