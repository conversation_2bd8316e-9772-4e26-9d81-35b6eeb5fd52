'use client';

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DataTable, Column } from '@/components/ui/data-table';
import { ConfirmModal } from '@/components/ui/modal';
import { fixturesApi } from '@/lib/api/fixtures';
import { Fixture } from '@/lib/types/api';
import { Eye, Edit, Trash2, Plus, RefreshCw, Calendar, Filter, Download, Upload, Radio, List, X } from 'lucide-react';
import { TableSkeleton } from '@/components/ui/skeleton';
import { usePermissions } from '@/lib/middleware/auth-guard';
// import { BroadcastLinksModal } from '@/components/fixtures/BroadcastLinksModal';
import { DatePicker } from '@/components/ui/date-picker';
import { DateTimeDisplay } from '@/components/ui/date-time-display';
import { isSameDate, convertLocalDateToUTC } from '@/lib/utils/date-time';

export default function FixturesPage() {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(25);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [leagueFilter, setLeagueFilter] = useState('');
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [selectedFixture, setSelectedFixture] = useState<Fixture | null>(null);
  const [broadcastLinksModalOpen, setBroadcastLinksModalOpen] = useState(false);
  const [selectedFixtureForBroadcast, setSelectedFixtureForBroadcast] = useState<Fixture | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  const { isAdmin, isEditor } = usePermissions();
  const queryClient = useQueryClient();

  // Mock data for testing when API is down
  const mockFixtures = {
    data: [
      {
        id: 1,
        homeTeamName: 'Manchester United',
        awayTeamName: 'Liverpool',
        homeTeamLogo: '/images/teams/1.png',
        awayTeamLogo: '/images/teams/2.png',
        date: '2024-12-19T14:30:00Z',
        status: 'scheduled',
        leagueName: 'Premier League',
        venue: 'Old Trafford'
      },
      {
        id: 2,
        homeTeamName: 'Arsenal',
        awayTeamName: 'Chelsea',
        homeTeamLogo: '/images/teams/3.png',
        awayTeamLogo: '/images/teams/4.png',
        date: '2024-12-20T16:00:00Z',
        status: 'scheduled',
        leagueName: 'Premier League',
        venue: 'Emirates Stadium'
      },
      {
        id: 3,
        homeTeamName: 'Barcelona',
        awayTeamName: 'Real Madrid',
        homeTeamLogo: '/images/teams/5.png',
        awayTeamLogo: '/images/teams/6.png',
        date: '2024-12-21T20:00:00Z',
        status: 'scheduled',
        leagueName: 'La Liga',
        venue: 'Camp Nou'
      }
    ],
    totalItems: 3,
    totalPages: 1,
    currentPage: 1,
    limit: 25
  };

  // Fetch fixtures data
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['fixtures', 'all', page, limit, searchQuery, statusFilter, leagueFilter, selectedDate],
    queryFn: () => fixturesApi.getFixtures({
      page,
      limit,
      ...(searchQuery && { search: searchQuery }),
      ...(statusFilter && { status: statusFilter }),
      ...(leagueFilter && { league: leagueFilter }),
      ...(selectedDate && { date: convertLocalDateToUTC(selectedDate) })
    }),
    staleTime: 30000, // 30 seconds
    retry: false, // Don't retry when API is down
    onError: () => {
      console.log('API is down, using mock data');
    }
  });

  // Use mock data if API fails
  const fixturesData = data || mockFixtures;

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: (fixtureId: number) => fixturesApi.deleteFixture(fixtureId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fixtures'] });
      console.log('Fixture deleted successfully');
      setDeleteModalOpen(false);
      setSelectedFixture(null);
    },
    onError: (error: any) => {
      console.error('Failed to delete fixture:', error.message);
    },
  });

  // Define table columns
  const columns: Column<Fixture>[] = [
    {
      key: 'date',
      title: (
        <div className="flex items-center space-x-2">
          <span>Date & Time</span>
          <Calendar className="h-4 w-4 text-gray-400" />
        </div>
      ),
      sortable: true,
      render: (value) => (
        <div className="text-sm">
          <DateTimeDisplay
            dateTime={value}
            showDate={true}
            showTime={true}
            className=""
          />
        </div>
      ),
    },
    {
      key: 'match',
      title: 'Match',
      sortable: false,
      render: (_, row) => (
        <div className="flex items-center justify-center space-x-3 py-2">
          <div className="flex items-center space-x-2 flex-1 justify-end">
            {row.homeTeamLogo && (
              <img
                src={row.homeTeamLogo.replace('public/', '/api/images/')}
                alt={row.homeTeamName}
                className="w-6 h-6 object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
            <span className="font-medium text-right">{row.homeTeamName}</span>
          </div>
          <div className="px-2">
            <span className="text-gray-500 font-bold">vs</span>
          </div>
          <div className="flex items-center space-x-2 flex-1">
            <span className="font-medium text-left">{row.awayTeamName}</span>
            {row.awayTeamLogo && (
              <img
                src={row.awayTeamLogo.replace('public/', '/api/images/')}
                alt={row.awayTeamName}
                className="w-6 h-6 object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
          </div>
        </div>
      ),
    },
    {
      key: 'score',
      title: 'Score',
      align: 'center',
      render: (_, row) => (
        <div className="text-center">
          <div className="font-bold text-lg">
            {row.goalsHome ?? '-'} - {row.goalsAway ?? '-'}
          </div>
          {(row.scoreHalftimeHome !== null && row.scoreHalftimeAway !== null) && (
            <div className="text-xs text-gray-500">
              HT: {row.scoreHalftimeHome} - {row.scoreHalftimeAway}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      render: (value, row) => {
        const getStatusColor = (status: string) => {
          switch (status) {
            case '1H':
            case '2H':
            case 'HT':
              return 'bg-green-100 text-green-800';
            case 'FT':
              return 'bg-gray-100 text-gray-800';
            case 'NS':
              return 'bg-blue-100 text-blue-800';
            case 'CANC':
            case 'PST':
              return 'bg-red-100 text-red-800';
            default:
              return 'bg-yellow-100 text-yellow-800';
          }
        };

        const getStatusText = (status: string, elapsed?: number) => {
          switch (status) {
            case '1H':
            case '2H':
              return `${elapsed}'`;
            case 'HT':
              return 'Half Time';
            case 'FT':
              return 'Full Time';
            case 'NS':
              return 'Not Started';
            case 'CANC':
              return 'Cancelled';
            case 'PST':
              return 'Postponed';
            default:
              return status;
          }
        };

        return (
          <Badge className={getStatusColor(value)}>
            {getStatusText(value, row.elapsed)}
          </Badge>
        );
      },
    },
    {
      key: 'leagueName',
      title: 'League',
      sortable: true,
      filterable: true,
      render: (value) => (
        <span className="text-sm text-gray-600">{value}</span>
      ),
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, row) => (
        <div className="flex space-x-1">
          <Button
            size="sm"
            variant="outline"
            title="View Details"
            onClick={() => handleViewFixture(row)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            title="Broadcast Links"
            onClick={() => handleBroadcastLinks(row)}
          >
            <Radio className="h-4 w-4" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            title="View Broadcast Links"
            onClick={() => handleViewBroadcastLinks(row)}
          >
            <List className="h-4 w-4" />
          </Button>
          {isEditor() && (
            <Button
              size="sm"
              variant="outline"
              title="Edit"
              onClick={() => handleEditFixture(row)}
            >
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {isAdmin() && (
            <Button
              size="sm"
              variant="outline"
              title="Delete"
              onClick={() => handleDeleteFixture(row)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ),
    },
  ];

  // Handler functions
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPage(1); // Reset to first page when searching
  };

  const handleViewFixture = (fixture: Fixture) => {
    // Navigate to fixture detail page
    window.open(`/dashboard/fixtures/${fixture.id}`, '_blank');
  };

  const handleEditFixture = (fixture: Fixture) => {
    // Navigate to edit page
    window.open(`/dashboard/fixtures/${fixture.id}/edit`, '_blank');
  };

  const handleDeleteFixture = (fixture: Fixture) => {
    setSelectedFixture(fixture);
    setDeleteModalOpen(true);
  };

  const handleBroadcastLinks = (fixture: Fixture) => {
    setSelectedFixtureForBroadcast(fixture);
    setBroadcastLinksModalOpen(true);
  };

  const handleViewBroadcastLinks = (fixture: Fixture) => {
    setSelectedFixtureForBroadcast(fixture);
    setBroadcastLinksModalOpen(true);
  };

  const confirmDelete = () => {
    if (selectedFixture) {
      deleteMutation.mutate(selectedFixture.id);
    }
  };

  const handleBulkSync = async () => {
    try {
      console.log('Starting fixtures sync...');
      // This would call the sync API
      // await fixturesApi.syncFixtures();
      console.log('Fixtures sync completed');
      refetch();
    } catch (error: any) {
      console.error('Sync failed:', error.message);
    }
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Fixtures Management</h1>
          <p className="text-gray-600 mt-1">Manage football fixtures and match data</p>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">Failed to load fixtures</p>
              <Button onClick={() => refetch()}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Fixtures Management</h1>
          <p className="text-gray-600 mt-1">Manage football fixtures and match data</p>
        </div>

        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          {isAdmin() && (
            <Button
              variant="outline"
              onClick={handleBulkSync}
              disabled={isLoading}
            >
              <Upload className="mr-2 h-4 w-4" />
              Sync Data
            </Button>
          )}

          <Button
            variant="outline"
            onClick={() => {
              // Export functionality
              console.log('Export feature coming soon');
            }}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>

          {isEditor() && (
            <Button onClick={() => window.open('/dashboard/fixtures/create', '_blank')}>
              <Plus className="mr-2 h-4 w-4" />
              Add Fixture
            </Button>
          )}
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {data?.meta?.totalItems?.toLocaleString() || 'Loading...'}
            </div>
            <p className="text-sm text-gray-600">Total Fixtures</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {data?.data?.filter(f => ['1H', '2H', 'HT'].includes(f.status)).length || 0}
            </div>
            <p className="text-sm text-gray-600">Live Matches</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {data?.data?.filter(f => f.status === 'NS').length || 0}
            </div>
            <p className="text-sm text-gray-600">Upcoming</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">
              {data?.data?.filter(f => f.status === 'FT').length || 0}
            </div>
            <p className="text-sm text-gray-600">Completed</p>
          </CardContent>
        </Card>
      </div>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                All Fixtures
              </CardTitle>
              <CardDescription>
                Complete list of football fixtures with real-time updates
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <DatePicker
                date={selectedDate}
                onDateChange={setSelectedDate}
                placeholder="Filter by date"
                className="w-[200px]"
              />
              {selectedDate && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedDate(undefined)}
                  className="px-2"
                  title="Clear date filter"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <TableSkeleton rows={10} columns={7} />
          ) : (
            <DataTable
              data={fixturesData?.data || []}
              columns={columns}
              loading={isLoading && !error}
              searchable={true}
              searchPlaceholder="Search fixtures..."
              onSearch={handleSearch}
              pagination={{
                page,
                limit,
                total: fixturesData?.totalItems || 0,
                onPageChange: setPage,
                onLimitChange: setLimit,
              }}
              emptyMessage="No fixtures found"
            />
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={deleteModalOpen}
        onClose={() => {
          setDeleteModalOpen(false);
          setSelectedFixture(null);
        }}
        onConfirm={confirmDelete}
        title="Delete Fixture"
        message={
          selectedFixture
            ? `Are you sure you want to delete the fixture "${selectedFixture.homeTeamName} vs ${selectedFixture.awayTeamName}"? This action cannot be undone.`
            : 'Are you sure you want to delete this fixture?'
        }
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
        loading={deleteMutation.isPending}
      />

      {/* Broadcast Links Modal */}
      {/* {selectedFixtureForBroadcast && (
        <BroadcastLinksModal
          isOpen={broadcastLinksModalOpen}
          onClose={() => {
            setBroadcastLinksModalOpen(false);
            setSelectedFixtureForBroadcast(null);
          }}
          fixture={selectedFixtureForBroadcast}
        />
      )} */}
    </div>
  );
}
