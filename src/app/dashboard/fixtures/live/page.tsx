'use client';

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, Clock, MapPin, Users } from 'lucide-react';
import { fixturesApi } from '@/lib/api/fixtures';
import { Fixture } from '@/lib/types/api';

export default function LiveFixturesPage() {
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch live and upcoming fixtures
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['fixtures', 'live-upcoming'],
    queryFn: () => fixturesApi.getUpcomingAndLive({ limit: 20 }),
    refetchInterval: autoRefresh ? 30000 : false, // Refresh every 30 seconds
    staleTime: 10000, // Consider data stale after 10 seconds
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case '1H':
      case '2H':
      case 'HT':
        return 'bg-green-100 text-green-800 animate-pulse';
      case 'FT':
        return 'bg-gray-100 text-gray-800';
      case 'NS':
        return 'bg-blue-100 text-blue-800';
      case 'CANC':
      case 'PST':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusText = (status: string, elapsed?: number) => {
    switch (status) {
      case '1H':
        return `${elapsed}'`;
      case '2H':
        return `${elapsed}'`;
      case 'HT':
        return 'Half Time';
      case 'FT':
        return 'Full Time';
      case 'NS':
        return 'Not Started';
      case 'CANC':
        return 'Cancelled';
      case 'PST':
        return 'Postponed';
      default:
        return status;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
    };
  };

  const FixtureCard = ({ fixture }: { fixture: Fixture }) => {
    const { date, time } = formatDate(fixture.date);
    const isLive = ['1H', '2H', 'HT'].includes(fixture.status);

    return (
      <Card className={`transition-all duration-200 ${isLive ? 'ring-2 ring-green-500' : ''}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Badge className={getStatusColor(fixture.status)}>
                {getStatusText(fixture.status, fixture.elapsed)}
              </Badge>
              {isLive && (
                <Badge variant="outline" className="text-green-600 border-green-600">
                  LIVE
                </Badge>
              )}
            </div>
            <div className="text-sm text-gray-500">
              {date} • {time}
            </div>
          </div>
          <div className="text-sm text-gray-600">
            {fixture.leagueName} • {fixture.round}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Teams and Score */}
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <Users className="h-4 w-4 text-gray-600" />
                  </div>
                  <span className="font-medium">{fixture.homeTeamName}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                    <Users className="h-4 w-4 text-gray-600" />
                  </div>
                  <span className="font-medium">{fixture.awayTeamName}</span>
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold">
                  {fixture.goalsHome ?? '-'}
                </div>
                <div className="text-2xl font-bold">
                  {fixture.goalsAway ?? '-'}
                </div>
              </div>
            </div>

            {/* Venue */}
            {fixture.venue && (
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <MapPin className="h-4 w-4" />
                <span>{fixture.venue.name}, {fixture.venue.city}</span>
              </div>
            )}

            {/* Half-time Score */}
            {(fixture.scoreHalftimeHome !== null && fixture.scoreHalftimeAway !== null) && (
              <div className="text-sm text-gray-600">
                Half-time: {fixture.scoreHalftimeHome} - {fixture.scoreHalftimeAway}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Live & Upcoming Fixtures</h1>
          <p className="text-gray-600 mt-1">Real-time football match updates</p>
        </div>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">Failed to load fixtures</p>
              <Button onClick={() => refetch()}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Live & Upcoming Fixtures</h1>
          <p className="text-gray-600 mt-1">Real-time football match updates</p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant={autoRefresh ? "default" : "outline"}
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Clock className="mr-2 h-4 w-4" />
            Auto Refresh {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={isLoading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {data?.data?.filter(f => ['1H', '2H', 'HT'].includes(f.status)).length || 0}
            </div>
            <p className="text-sm text-gray-600">Live Matches</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {data?.data?.filter(f => f.status === 'NS').length || 0}
            </div>
            <p className="text-sm text-gray-600">Upcoming</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">
              {data?.data?.length || 0}
            </div>
            <p className="text-sm text-gray-600">Total Fixtures</p>
          </CardContent>
        </Card>
      </div>

      {/* Fixtures Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-6 bg-gray-200 rounded"></div>
                  <div className="h-6 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : data?.data && data.data.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {data.data.map((fixture) => (
            <FixtureCard key={fixture.id} fixture={fixture} />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-gray-600">No live or upcoming fixtures found</p>
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {data?.meta && data.meta.totalPages > 1 && (
        <div className="flex justify-center">
          <p className="text-sm text-gray-600">
            Page {data.meta.currentPage} of {data.meta.totalPages} 
            ({data.meta.totalItems} total fixtures)
          </p>
        </div>
      )}
    </div>
  );
}
