'use client';

import { useQuery } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { fixturesApi } from '@/lib/api/fixtures';
import { 
  ArrowLeft, 
  Edit, 
  Calendar, 
  MapPin, 
  Clock, 
  Trophy,
  Users,
  Target,
  Activity
} from 'lucide-react';
import { usePermissions } from '@/lib/middleware/auth-guard';

export default function FixtureDetailPage() {
  const params = useParams();
  const router = useRouter();
  const fixtureId = parseInt(params.id as string);
  const { isEditor } = usePermissions();

  // Fetch fixture details
  const { data: fixture, isLoading, error } = useQuery({
    queryKey: ['fixture', fixtureId],
    queryFn: () => fixturesApi.getFixture(fixtureId),
    enabled: !!fixtureId,
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case '1H':
      case '2H':
      case 'HT':
        return 'bg-green-100 text-green-800';
      case 'FT':
        return 'bg-gray-100 text-gray-800';
      case 'NS':
        return 'bg-blue-100 text-blue-800';
      case 'CANC':
      case 'PST':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusText = (status: string, elapsed?: number) => {
    switch (status) {
      case '1H':
      case '2H':
        return `${elapsed}'`;
      case 'HT':
        return 'Half Time';
      case 'FT':
        return 'Full Time';
      case 'NS':
        return 'Not Started';
      case 'CANC':
        return 'Cancelled';
      case 'PST':
        return 'Postponed';
      default:
        return status;
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-10 w-10" />
          <div className="space-y-2">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-48" />
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-64" />
            <Skeleton className="h-48" />
          </div>
          <div className="space-y-6">
            <Skeleton className="h-32" />
            <Skeleton className="h-48" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !fixture) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
        
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p className="text-red-600 mb-4">Failed to load fixture details</p>
              <Button onClick={() => router.push('/dashboard/fixtures')}>
                Return to Fixtures
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => router.back()}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {fixture.homeTeamName} vs {fixture.awayTeamName}
            </h1>
            <p className="text-gray-600 mt-1">Fixture Details</p>
          </div>
        </div>
        
        {isEditor() && (
          <Button onClick={() => router.push(`/dashboard/fixtures/${fixtureId}/edit`)}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Fixture
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Match Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Trophy className="mr-2 h-5 w-5" />
                Match Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center space-y-4">
                {/* Teams and Score */}
                <div className="flex items-center justify-center space-x-8">
                  <div className="text-center">
                    <div className="text-2xl font-bold">{fixture.homeTeamName}</div>
                    <div className="text-sm text-gray-500">Home</div>
                  </div>
                  
                  <div className="text-center">
                    <div className="text-4xl font-bold text-blue-600">
                      {fixture.goalsHome ?? '-'} - {fixture.goalsAway ?? '-'}
                    </div>
                    {(fixture.scoreHalftimeHome !== null && fixture.scoreHalftimeAway !== null) && (
                      <div className="text-sm text-gray-500">
                        HT: {fixture.scoreHalftimeHome} - {fixture.scoreHalftimeAway}
                      </div>
                    )}
                  </div>
                  
                  <div className="text-center">
                    <div className="text-2xl font-bold">{fixture.awayTeamName}</div>
                    <div className="text-sm text-gray-500">Away</div>
                  </div>
                </div>

                {/* Status */}
                <div className="flex justify-center">
                  <Badge className={getStatusColor(fixture.status)}>
                    {getStatusText(fixture.status, fixture.elapsed)}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Match Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="mr-2 h-5 w-5" />
                Match Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {fixture.goalsHome ?? 0}
                  </div>
                  <div className="text-sm text-gray-600">Goals</div>
                </div>
                
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {fixture.goalsAway ?? 0}
                  </div>
                  <div className="text-sm text-gray-600">Goals</div>
                </div>
              </div>
              
              {fixture.elapsed && (
                <div className="mt-4 text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {fixture.elapsed}'
                  </div>
                  <div className="text-sm text-gray-600">Minutes Played</div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Match Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                Match Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="font-medium">Date</div>
                  <div className="text-sm text-gray-600">
                    {new Date(fixture.date).toLocaleDateString()}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Clock className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="font-medium">Time</div>
                  <div className="text-sm text-gray-600">
                    {new Date(fixture.date).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Trophy className="h-4 w-4 text-gray-400" />
                <div>
                  <div className="font-medium">League</div>
                  <div className="text-sm text-gray-600">{fixture.leagueName}</div>
                </div>
              </div>
              
              {fixture.venueName && (
                <div className="flex items-center space-x-3">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <div>
                    <div className="font-medium">Venue</div>
                    <div className="text-sm text-gray-600">{fixture.venueName}</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => window.open(`/dashboard/leagues/${fixture.leagueId}`, '_blank')}
              >
                <Trophy className="mr-2 h-4 w-4" />
                View League
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => window.open(`/dashboard/teams/${fixture.homeTeamId}`, '_blank')}
              >
                <Users className="mr-2 h-4 w-4" />
                View Home Team
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => window.open(`/dashboard/teams/${fixture.awayTeamId}`, '_blank')}
              >
                <Users className="mr-2 h-4 w-4" />
                View Away Team
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
