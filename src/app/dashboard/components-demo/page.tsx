'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DataTable, Column } from '@/components/ui/data-table';
import { 
  InputField, 
  SelectField, 
  CheckboxField, 
  TextareaField,
  FormSection,
  FormActions 
} from '@/components/ui/form-field';
import { Modal, ConfirmModal, FormModal } from '@/components/ui/modal';
import { Skeleton, CardSkeleton, TableSkeleton } from '@/components/ui/skeleton';
import { Eye, Edit, Trash2, Plus } from 'lucide-react';

// Sample data for DataTable demo
interface SampleData {
  id: number;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive';
  lastLogin: string;
}

const sampleData: SampleData[] = [
  { id: 1, name: '<PERSON>', email: '<EMAIL>', role: 'Admin', status: 'active', lastLogin: '2025-05-24' },
  { id: 2, name: '<PERSON>', email: '<EMAIL>', role: 'Editor', status: 'active', lastLogin: '2025-05-23' },
  { id: 3, name: 'Bob <PERSON>', email: '<EMAIL>', role: 'Moderator', status: 'inactive', lastLogin: '2025-05-20' },
  { id: 4, name: 'Alice Brown', email: '<EMAIL>', role: 'Editor', status: 'active', lastLogin: '2025-05-24' },
  { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', role: 'Moderator', status: 'active', lastLogin: '2025-05-22' },
];

export default function ComponentsDemoPage() {
  const [showModal, setShowModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [showFormModal, setShowFormModal] = useState(false);
  const [showSkeletons, setShowSkeletons] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    role: '',
    bio: '',
    notifications: false,
  });

  // DataTable columns
  const columns: Column<SampleData>[] = [
    {
      key: 'name',
      title: 'Name',
      sortable: true,
      render: (value) => <span className="font-medium">{value}</span>,
    },
    {
      key: 'email',
      title: 'Email',
      sortable: true,
    },
    {
      key: 'role',
      title: 'Role',
      sortable: true,
      filterable: true,
      render: (value) => (
        <Badge variant={value === 'Admin' ? 'default' : 'secondary'}>
          {value}
        </Badge>
      ),
    },
    {
      key: 'status',
      title: 'Status',
      sortable: true,
      filterable: true,
      render: (value) => (
        <Badge variant={value === 'active' ? 'default' : 'secondary'}>
          {value}
        </Badge>
      ),
    },
    {
      key: 'lastLogin',
      title: 'Last Login',
      sortable: true,
    },
    {
      key: 'actions',
      title: 'Actions',
      render: (_, row) => (
        <div className="flex space-x-2">
          <Button size="sm" variant="outline">
            <Eye className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="outline">
            <Edit className="h-4 w-4" />
          </Button>
          <Button size="sm" variant="outline">
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ];

  const handleFormSubmit = () => {
    console.log('Form submitted:', formData);
    setShowFormModal(false);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Reusable Components Demo</h1>
        <p className="text-gray-600 mt-1">
          Showcase of all reusable components built for the CMS
        </p>
      </div>

      {/* DataTable Demo */}
      <Card>
        <CardHeader>
          <CardTitle>DataTable Component</CardTitle>
          <CardDescription>
            Advanced data table with sorting, filtering, and pagination
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DataTable
            data={sampleData}
            columns={columns}
            searchable={true}
            searchPlaceholder="Search users..."
            emptyMessage="No users found"
          />
        </CardContent>
      </Card>

      {/* Form Components Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Form Components</CardTitle>
          <CardDescription>
            Various form fields with validation and styling
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FormSection title="User Information" description="Basic user details">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <InputField
                label="Full Name"
                placeholder="Enter full name"
                required
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              />
              <InputField
                label="Email Address"
                type="email"
                placeholder="Enter email"
                required
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            
            <SelectField
              label="Role"
              placeholder="Select a role"
              required
              value={formData.role}
              onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}
              options={[
                { value: 'admin', label: 'Administrator' },
                { value: 'editor', label: 'Editor' },
                { value: 'moderator', label: 'Moderator' },
              ]}
            />
            
            <TextareaField
              label="Bio"
              placeholder="Tell us about yourself..."
              description="Optional bio information"
              value={formData.bio}
              onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
            />
            
            <CheckboxField
              label="Enable email notifications"
              checked={formData.notifications}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, notifications: checked }))}
            />
          </FormSection>
          
          <FormActions>
            <Button variant="outline">Cancel</Button>
            <Button>Save Changes</Button>
          </FormActions>
        </CardContent>
      </Card>

      {/* Modal Components Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Modal Components</CardTitle>
          <CardDescription>
            Different types of modal dialogs
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-4">
            <Button onClick={() => setShowModal(true)}>
              Basic Modal
            </Button>
            <Button onClick={() => setShowConfirmModal(true)} variant="outline">
              Confirm Modal
            </Button>
            <Button onClick={() => setShowFormModal(true)} variant="outline">
              Form Modal
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Loading Skeletons Demo */}
      <Card>
        <CardHeader>
          <CardTitle>Loading Skeletons</CardTitle>
          <CardDescription>
            Loading states for different components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button 
              onClick={() => setShowSkeletons(!showSkeletons)}
              variant="outline"
            >
              {showSkeletons ? 'Hide' : 'Show'} Skeletons
            </Button>
            
            {showSkeletons && (
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-2">Card Skeleton</h4>
                  <CardSkeleton />
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Table Skeleton</h4>
                  <TableSkeleton rows={3} columns={4} />
                </div>
                
                <div>
                  <h4 className="font-medium mb-2">Basic Skeletons</h4>
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-8 w-1/4" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Basic Modal"
        description="This is a basic modal dialog example"
      >
        <div className="space-y-4">
          <p>This is the content of the modal. You can put any content here.</p>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowModal(false)}>
              Close
            </Button>
          </div>
        </div>
      </Modal>

      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={() => setShowConfirmModal(false)}
        onConfirm={() => {
          console.log('Confirmed!');
          setShowConfirmModal(false);
        }}
        title="Confirm Action"
        message="Are you sure you want to proceed with this action?"
        confirmText="Yes, proceed"
        variant="destructive"
      />

      <FormModal
        isOpen={showFormModal}
        onClose={() => setShowFormModal(false)}
        onSubmit={handleFormSubmit}
        title="Create New User"
        description="Fill in the details to create a new user"
        submitText="Create User"
      >
        <div className="space-y-4">
          <InputField
            label="Username"
            placeholder="Enter username"
            required
          />
          <InputField
            label="Email"
            type="email"
            placeholder="Enter email"
            required
          />
          <SelectField
            label="Role"
            placeholder="Select role"
            required
            options={[
              { value: 'admin', label: 'Administrator' },
              { value: 'editor', label: 'Editor' },
              { value: 'moderator', label: 'Moderator' },
            ]}
          />
        </div>
      </FormModal>
    </div>
  );
}
