'use client';

import { useState } from 'react';
import { AuthGuard } from '@/lib/middleware/auth-guard';
import { Header } from '@/components/layout/Header';
import { Sidebar } from '@/components/layout/Sidebar';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleMenuClick = () => {
    setSidebarOpen(true);
  };

  const handleSidebarClose = () => {
    setSidebarOpen(false);
  };

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50">
        <Header onMenuClick={handleMenuClick} />
        <div className="flex">
          <Sidebar
            isOpen={sidebarOpen}
            onClose={handleSidebarClose}
          />
          <main className="flex-1 p-4 sm:p-6 md:ml-0">
            {children}
          </main>
        </div>
      </div>
    </AuthGuard>
  );
}
