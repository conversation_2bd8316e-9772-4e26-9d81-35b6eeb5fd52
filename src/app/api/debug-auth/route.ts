import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    const allHeaders = Object.fromEntries(request.headers.entries());
    
    console.log('🔍 Debug Auth Headers:');
    console.log('Authorization:', authHeader);
    console.log('All headers:', allHeaders);
    
    return NextResponse.json({
      authHeader,
      hasAuth: !!authHeader,
      allHeaders,
      timestamp: new Date().toISOString()
    });
  } catch (error: any) {
    console.error('❌ Debug Auth Error:', error);
    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    );
  }
}
