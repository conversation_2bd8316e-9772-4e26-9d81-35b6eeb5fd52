import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Forward all query parameters
    const params = new URLSearchParams();
    searchParams.forEach((value, key) => {
      params.append(key, value);
    });

    console.log('🔄 Proxying broadcast links request:', `${API_BASE_URL}/broadcast-links?${params.toString()}`);

    const response = await fetch(`${API_BASE_URL}/broadcast-links?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ API Error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          error: 'Failed to fetch broadcast links',
          status: response.status,
          message: response.statusText 
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Broadcast links fetched successfully:', data.meta);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🔄 Proxying broadcast link create request:', `${API_BASE_URL}/broadcast-links`);

    const response = await fetch(`${API_BASE_URL}/broadcast-links`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error('❌ API Error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          error: 'Failed to create broadcast link',
          status: response.status,
          message: response.statusText 
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Broadcast link created successfully:', data.data?.id);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
