import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Forward all query parameters
    const params = new URLSearchParams();
    searchParams.forEach((value, key) => {
      params.append(key, value);
    });

    console.log('🔄 Proxying broadcast links request:', `${API_BASE_URL}/broadcast-links?${params.toString()}`);

    const response = await fetch(`${API_BASE_URL}/broadcast-links?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ API Error:', response.status, response.statusText);
      return NextResponse.json(
        {
          error: 'Failed to fetch broadcast links',
          status: response.status,
          message: response.statusText
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Broadcast links fetched successfully:', data.meta);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    console.log('📝 Mock: Creating broadcast link:', body);

    // Mock successful creation
    const mockData = {
      data: {
        id: Math.floor(Math.random() * 1000) + 1,
        fixtureId: body.fixtureId,
        linkName: body.linkName,
        linkUrl: body.linkUrl,
        linkComment: body.linkComment,
        language: body.language || 'English',
        quality: body.quality || 'HD',
        addedBy: 1, // Mock user ID
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      status: 201
    };

    console.log('✅ Mock: Broadcast link created successfully:', mockData.data.id);

    return NextResponse.json(mockData, { status: 201 });
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}
