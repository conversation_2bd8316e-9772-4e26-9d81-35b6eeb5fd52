import { NextRequest, NextResponse } from 'next/server';
import { mockBroadcastLinksStorage } from '@/lib/mock/broadcast-links-storage';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { fixtureId: string } }
) {
  try {
    const fixtureId = params.fixtureId;

    console.log('🔄 Proxying broadcast links for fixture request:', `${API_BASE_URL}/football/broadcast-links/fixture/${fixtureId}`);

    // Get data from mock storage
    const fixtureIdNum = parseInt(fixtureId);
    const links = mockBroadcastLinksStorage.getByFixture(fixtureIdNum);

    console.log('📝 Mock: Returning broadcast links for fixture:', fixtureId, '- Found:', links.length, 'links');

    const mockData = {
      data: links,
      status: 200
    };

    return NextResponse.json(mockData);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}
