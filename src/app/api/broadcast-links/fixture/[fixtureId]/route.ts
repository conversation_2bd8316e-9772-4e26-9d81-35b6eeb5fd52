import { NextRequest, NextResponse } from 'next/server';
import { mockBroadcastLinksStorage } from '@/lib/mock/broadcast-links-storage';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { fixtureId: string } }
) {
  try {
    const fixtureId = params.fixtureId;

    // Try real API first, fallback to mock if not available
    console.log('🔄 Trying real API for broadcast links:', `${API_BASE_URL}/football/broadcast-links/fixture/${fixtureId}`);

    try {
      const response = await fetch(`${API_BASE_URL}/football/broadcast-links/fixture/${fixtureId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          ...(request.headers.get('authorization') && {
            'Authorization': request.headers.get('authorization')!
          })
        },
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Real API: Broadcast links fetched successfully:', data.data?.length || 0, 'links');
        return NextResponse.json(data);
      } else if (response.status === 404) {
        console.log('⚠️ Real API not implemented, falling back to mock data');
        // Fallback to mock data
        const fixtureIdNum = parseInt(fixtureId);
        const links = mockBroadcastLinksStorage.getByFixture(fixtureIdNum);

        console.log('📝 Mock: Returning broadcast links for fixture:', fixtureId, '- Found:', links.length, 'links');

        const mockData = {
          data: links,
          status: 200
        };

        return NextResponse.json(mockData);
      } else {
        console.error('❌ Real API Error:', response.status, response.statusText);
        return NextResponse.json(
          {
            error: 'Failed to fetch broadcast links for fixture',
            status: response.status,
            message: response.statusText
          },
          { status: response.status }
        );
      }
    } catch (networkError) {
      console.log('⚠️ Network error, falling back to mock data:', networkError);
      // Fallback to mock data on network error
      const fixtureIdNum = parseInt(fixtureId);
      const links = mockBroadcastLinksStorage.getByFixture(fixtureIdNum);

      console.log('📝 Mock: Returning broadcast links for fixture:', fixtureId, '- Found:', links.length, 'links');

      const mockData = {
        data: links,
        status: 200
      };

      return NextResponse.json(mockData);
    }
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}
