import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { fixtureId: string } }
) {
  try {
    const fixtureId = params.fixtureId;

    console.log('🔄 Proxying broadcast links for fixture request:', `${API_BASE_URL}/football/broadcast-links/fixture/${fixtureId}`);

    // Mock response since API endpoint doesn't exist yet
    console.log('📝 Mock: Returning empty broadcast links for fixture:', fixtureId);

    const mockData = {
      data: [],
      status: 200
    };

    return NextResponse.json(mockData);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}
