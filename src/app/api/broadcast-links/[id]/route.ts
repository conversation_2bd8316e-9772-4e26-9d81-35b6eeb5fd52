import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const broadcastLinkId = params.id;

    console.log('🔄 Proxying broadcast link detail request:', `${API_BASE_URL}/broadcast-links/${broadcastLinkId}`);

    const response = await fetch(`${API_BASE_URL}/broadcast-links/${broadcastLinkId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ API Error:', response.status, response.statusText);
      return NextResponse.json(
        {
          error: 'Failed to fetch broadcast link',
          status: response.status,
          message: response.statusText
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Broadcast link detail fetched successfully:', data.data?.id);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const body = await request.json();

    console.log('🔄 Proxying broadcast link update request:', `${API_BASE_URL}/football/broadcast-links/${id}`);

    const response = await fetch(`${API_BASE_URL}/football/broadcast-links/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error('❌ API Error:', response.status, response.statusText);
      return NextResponse.json(
        {
          error: 'Failed to update broadcast link',
          status: response.status,
          message: response.statusText
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Broadcast link updated successfully:', data.data?.id);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    console.log('🔄 Proxying broadcast link delete request:', `${API_BASE_URL}/football/broadcast-links/${id}`);

    const response = await fetch(`${API_BASE_URL}/football/broadcast-links/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ API Error:', response.status, response.statusText);
      return NextResponse.json(
        {
          error: 'Failed to delete broadcast link',
          status: response.status,
          message: response.statusText
        },
        { status: response.status }
      );
    }

    console.log('✅ Broadcast link deleted successfully:', id);

    return NextResponse.json({ success: true, message: 'Broadcast link deleted successfully' });
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}
