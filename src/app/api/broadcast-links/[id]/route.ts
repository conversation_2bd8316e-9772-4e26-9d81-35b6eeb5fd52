import { NextRequest, NextResponse } from 'next/server';
import { mockBroadcastLinksStorage } from '@/lib/mock/broadcast-links-storage';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const broadcastLinkId = params.id;

    console.log('🔄 Proxying broadcast link detail request:', `${API_BASE_URL}/broadcast-links/${broadcastLinkId}`);

    const response = await fetch(`${API_BASE_URL}/broadcast-links/${broadcastLinkId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ API Error:', response.status, response.statusText);
      return NextResponse.json(
        {
          error: 'Failed to fetch broadcast link',
          status: response.status,
          message: response.statusText
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Broadcast link detail fetched successfully:', data.data?.id);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);
    const body = await request.json();

    console.log('📝 Mock: Updating broadcast link:', id, body);

    // Update in mock storage
    const updatedLink = mockBroadcastLinksStorage.update(id, {
      linkName: body.linkName,
      linkUrl: body.linkUrl,
      linkComment: body.linkComment,
      language: body.language,
      quality: body.quality,
    });

    if (!updatedLink) {
      return NextResponse.json(
        {
          error: 'Broadcast link not found',
          status: 404
        },
        { status: 404 }
      );
    }

    const mockData = {
      data: updatedLink,
      status: 200
    };

    console.log('✅ Mock: Broadcast link updated successfully:', updatedLink.id);

    return NextResponse.json(mockData);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const id = parseInt(params.id);

    console.log('📝 Mock: Deleting broadcast link:', id);

    // Delete from mock storage
    const deleted = mockBroadcastLinksStorage.delete(id);

    if (!deleted) {
      return NextResponse.json(
        {
          error: 'Broadcast link not found',
          status: 404
        },
        { status: 404 }
      );
    }

    console.log('✅ Mock: Broadcast link deleted successfully:', id);
    console.log('📊 Mock: Total links in storage:', mockBroadcastLinksStorage.getAll().length);

    return NextResponse.json({ status: 200 });
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}
