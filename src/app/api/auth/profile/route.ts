import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    console.log('🔄 Proxying profile request, auth:', authHeader ? 'Present' : 'Missing');

    const response = await fetch(`${API_BASE_URL}/system-auth/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(authHeader && {
          'Authorization': authHeader
        })
      },
    });

    if (!response.ok) {
      console.error('❌ Profile API Error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          error: 'Failed to fetch profile',
          status: response.status,
          message: response.statusText 
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Profile fetched successfully');

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Profile Proxy Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
