import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const authHeader = request.headers.get('authorization');
    
    console.log('🔄 Proxying logout request');

    const response = await fetch(`${API_BASE_URL}/system-auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(authHeader && {
          'Authorization': authHeader
        })
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error('❌ Logout API Error:', response.status, response.statusText);
      return NextResponse.json(
        { 
          error: 'Logout failed',
          status: response.status,
          message: response.statusText 
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Logout successful');

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Logout Proxy Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
