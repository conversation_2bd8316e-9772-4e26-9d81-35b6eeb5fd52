import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    console.log('🔄 Proxying login request:', body.username);

    const response = await fetch(`${API_BASE_URL}/system-auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      console.error('❌ Login API Error:', response.status, response.statusText);
      const errorData = await response.text();
      return NextResponse.json(
        { 
          error: 'Login failed',
          status: response.status,
          message: response.statusText,
          details: errorData
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Login successful for user:', body.username);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Login Proxy Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
