import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Testing token validity...');
    
    // Step 1: Get fresh token
    const loginResponse = await fetch(`${API_BASE_URL}/system-auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123456'
      }),
    });

    if (!loginResponse.ok) {
      console.error('❌ Login failed:', loginResponse.status);
      return NextResponse.json({ error: 'Login failed' }, { status: 500 });
    }

    const loginData = await loginResponse.json();
    console.log('✅ Fresh token obtained');

    // Step 2: Test profile with fresh token
    const profileResponse = await fetch(`${API_BASE_URL}/system-auth/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${loginData.accessToken}`
      },
    });

    console.log('🔍 Profile response status:', profileResponse.status);

    if (!profileResponse.ok) {
      const errorText = await profileResponse.text();
      console.error('❌ Profile failed with fresh token:', errorText);
      return NextResponse.json({ 
        error: 'Profile failed with fresh token',
        status: profileResponse.status,
        details: errorText
      }, { status: 500 });
    }

    const profileData = await profileResponse.json();
    console.log('✅ Profile success with fresh token');

    return NextResponse.json({
      success: true,
      tokenLength: loginData.accessToken.length,
      tokenPreview: loginData.accessToken.substring(0, 30) + '...',
      profile: profileData
    });

  } catch (error: any) {
    console.error('❌ Test Token Error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error.message 
      },
      { status: 500 }
    );
  }
}
