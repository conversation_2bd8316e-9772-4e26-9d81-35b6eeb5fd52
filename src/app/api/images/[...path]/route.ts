import { NextRequest, NextResponse } from 'next/server';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const imagePath = params.path.join('/');
    const imageUrl = `${API_BASE_URL}/${imagePath}`;
    
    console.log('🔄 Proxying image request:', imageUrl);

    const response = await fetch(imageUrl, {
      method: 'GET',
      headers: {
        // Forward relevant headers
        ...(request.headers.get('accept') && {
          'Accept': request.headers.get('accept')!
        }),
        ...(request.headers.get('user-agent') && {
          'User-Agent': request.headers.get('user-agent')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ Image Error:', response.status, response.statusText);
      // Return a placeholder or 404
      return new NextResponse(null, { status: 404 });
    }

    const imageBuffer = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/png';

    console.log('✅ Image served successfully:', imagePath);

    return new NextResponse(imageBuffer, {
      status: 200,
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
      },
    });
  } catch (error: any) {
    console.error('❌ Image Proxy Error:', error);
    return new NextResponse(null, { status: 500 });
  }
}
