'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { LoginForm } from '@/components/forms/LoginForm';
import { useAuth } from '@/lib/hooks/useAuth';
import { PageLoading } from '@/components/ui/loading-states';

export default function LoginPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // Redirect if already authenticated
    if (isAuthenticated && !isLoading) {
      router.push('/dashboard');
    }
  }, [isAuthenticated, isLoading, router]);

  // Show loading while checking auth status
  if (isLoading) {
    return <PageLoading message="Checking authentication..." />;
  }

  // Don't render login form if already authenticated
  if (isAuthenticated) {
    return <PageLoading message="Redirecting to dashboard..." />;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            APISportsGame CMS
          </h1>
          <p className="text-gray-600">
            Content Management System
          </p>
        </div>
        
        <LoginForm 
          onSuccess={() => router.push('/dashboard')}
        />
        
        <div className="text-center text-sm text-gray-500">
          <p>© 2025 APISportsGame. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}
