'use client';

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Fixture } from '@/lib/types/api';
import { Calendar, MapPin, User, Clock } from 'lucide-react';
import { DateTimeDisplay } from '@/components/ui/date-time-display';

interface FixtureCardProps {
  fixture: Fixture;
  className?: string;
}

export function FixtureCard({ fixture, className }: FixtureCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case '1H':
      case '2H':
      case 'HT':
        return 'bg-green-100 text-green-800';
      case 'FT':
        return 'bg-gray-100 text-gray-800';
      case 'NS':
        return 'bg-blue-100 text-blue-800';
      case 'CANC':
      case 'PST':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getStatusText = (status: string, elapsed?: number) => {
    switch (status) {
      case '1H':
      case '2H':
        return `${elapsed}'`;
      case 'HT':
        return 'Half Time';
      case 'FT':
        return 'Full Time';
      case 'NS':
        return 'Not Started';
      case 'CANC':
        return 'Cancelled';
      case 'PST':
        return 'Postponed';
      default:
        return status;
    }
  };

  return (
    <Card className={className}>
      <CardContent className="p-6">
        {/* Header with League and Status */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-600">{fixture.leagueName}</span>
            {fixture.round && (
              <span className="text-xs text-gray-500">• {fixture.round}</span>
            )}
          </div>
          <Badge className={getStatusColor(fixture.status)}>
            {getStatusText(fixture.status, fixture.elapsed)}
          </Badge>
        </div>

        {/* Teams and Score */}
        <div className="flex items-center justify-center space-x-6 mb-6">
          {/* Home Team */}
          <div className="flex flex-col items-center space-y-3 flex-1">
            {fixture.homeTeamLogo && (
              <img
                src={`${process.env.NEXT_PUBLIC_DOMAIN_CDN_PICTURE}/${fixture.homeTeamLogo}`}
                alt={fixture.homeTeamName}
                className="w-16 h-16 object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
            <div className="text-center">
              <h3 className="font-semibold text-lg">{fixture.homeTeamName}</h3>
              <span className="text-sm text-gray-500">Home</span>
            </div>
          </div>

          {/* Score */}
          <div className="flex flex-col items-center space-y-2">
            <div className="text-4xl font-bold text-gray-900">
              {fixture.goalsHome ?? '-'} - {fixture.goalsAway ?? '-'}
            </div>
            {(fixture.scoreHalftimeHome !== null && fixture.scoreHalftimeAway !== null) && (
              <div className="text-sm text-gray-500">
                HT: {fixture.scoreHalftimeHome} - {fixture.scoreHalftimeAway}
              </div>
            )}
            <span className="text-xs text-gray-400 uppercase tracking-wide">VS</span>
          </div>

          {/* Away Team */}
          <div className="flex flex-col items-center space-y-3 flex-1">
            {fixture.awayTeamLogo && (
              <img
                src={`${process.env.NEXT_PUBLIC_DOMAIN_CDN_PICTURE}/${fixture.awayTeamLogo}`}
                alt={fixture.awayTeamName}
                className="w-16 h-16 object-contain"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
            <div className="text-center">
              <h3 className="font-semibold text-lg">{fixture.awayTeamName}</h3>
              <span className="text-sm text-gray-500">Away</span>
            </div>
          </div>
        </div>

        {/* Match Info */}
        <div className="space-y-3 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <DateTimeDisplay
              dateTime={fixture.date}
              showDate={true}
              showTime={true}
              className="font-medium"
            />
          </div>
          
          {fixture.venue && (
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4" />
              <span>
                {typeof fixture.venue === 'string' ? fixture.venue : fixture.venue.name}
                {typeof fixture.venue === 'object' && fixture.venue.city && (
                  <span className="text-gray-500">, {fixture.venue.city}</span>
                )}
              </span>
            </div>
          )}
          
          {fixture.referee && (
            <div className="flex items-center space-x-2">
              <User className="h-4 w-4" />
              <span>Referee: {fixture.referee}</span>
            </div>
          )}

          {fixture.elapsed && fixture.status !== 'NS' && (
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>{fixture.elapsed}' minutes played</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
