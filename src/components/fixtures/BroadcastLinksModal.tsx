'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Fixture } from '@/lib/types/api';
import { Plus, ExternalLink, Radio, Globe, Zap, X } from 'lucide-react';

interface BroadcastLinksModalProps {
      isOpen: boolean;
      onClose: () => void;
      fixture: Fixture;
}

export const BroadcastLinksModal: React.FC<BroadcastLinksModalProps> = ({
      isOpen,
      onClose,
      fixture,
}) => {
      const [showAddForm, setShowAddForm] = useState(false);
      const [formData, setFormData] = useState({
            title: '',
            url: '',
            language: 'en',
            quality: 'HD',
      });

      // Mock data for demonstration
      const mockBroadcastLinks = [
            {
                  id: 1,
                  title: 'ESPN HD Stream',
                  url: 'https://espn.com/live',
                  language: 'en',
                  quality: 'HD',
                  isActive: true,
            },
            {
                  id: 2,
                  title: 'Sky Sports 4K',
                  url: 'https://skysports.com/live',
                  language: 'en',
                  quality: '4K',
                  isActive: true,
            },
      ];

      const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();
            console.log('Adding broadcast link:', formData);
            setShowAddForm(false);
            setFormData({ title: '', url: '', language: 'en', quality: 'HD' });
      };

      const getQualityColor = (quality: string) => {
            switch (quality.toLowerCase()) {
                  case '4k':
                  case 'uhd':
                        return 'bg-purple-100 text-purple-800';
                  case 'hd':
                  case '1080p':
                        return 'bg-blue-100 text-blue-800';
                  case 'sd':
                  case '720p':
                        return 'bg-green-100 text-green-800';
                  default:
                        return 'bg-gray-100 text-gray-800';
            }
      };

      const getLanguageFlag = (language: string) => {
            const flags: Record<string, string> = {
                  en: '🇺🇸',
                  es: '🇪🇸',
                  fr: '🇫🇷',
                  de: '🇩🇪',
                  it: '🇮🇹',
                  pt: '🇵🇹',
                  ar: '🇸🇦',
            };
            return flags[language] || '🌐';
      };

      if (!isOpen) return null;

      return (
            <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
                  <div className="bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto">
                        {/* Header */}
                        <div className="p-6 border-b">
                              <div className="flex items-center justify-between">
                                    <div>
                                          <h2 className="text-xl font-semibold flex items-center">
                                                <Radio className="mr-2 h-5 w-5" />
                                                Broadcast Links - {fixture.homeTeamName} vs {fixture.awayTeamName}
                                          </h2>
                                          <p className="text-gray-600 mt-1">Manage streaming links for this fixture</p>
                                    </div>
                                    <Button variant="outline" size="sm" onClick={onClose}>
                                          <X className="h-4 w-4" />
                                    </Button>
                              </div>
                        </div>

                        {/* Content */}
                        <div className="p-6 space-y-6">
                              {/* Add New Link Button */}
                              <div className="flex justify-between items-center">
                                    <div className="text-sm text-gray-600">
                                          {mockBroadcastLinks.length} broadcast link(s) available
                                    </div>
                                    <Button
                                          onClick={() => setShowAddForm(!showAddForm)}
                                          size="sm"
                                    >
                                          <Plus className="mr-2 h-4 w-4" />
                                          Add Link
                                    </Button>
                              </div>

                              {/* Add Form */}
                              {showAddForm && (
                                    <Card>
                                          <CardHeader>
                                                <CardTitle className="text-lg">Add New Broadcast Link</CardTitle>
                                          </CardHeader>
                                          <CardContent>
                                                <form onSubmit={handleSubmit} className="space-y-4">
                                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                  <Label htmlFor="title">Title</Label>
                                                                  <Input
                                                                        id="title"
                                                                        value={formData.title}
                                                                        onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                                                                        placeholder="e.g., ESPN HD Stream"
                                                                        required
                                                                  />
                                                            </div>
                                                            <div>
                                                                  <Label htmlFor="url">URL</Label>
                                                                  <Input
                                                                        id="url"
                                                                        type="url"
                                                                        value={formData.url}
                                                                        onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                                                                        placeholder="https://..."
                                                                        required
                                                                  />
                                                            </div>
                                                            <div>
                                                                  <Label htmlFor="language">Language</Label>
                                                                  <select
                                                                        id="language"
                                                                        value={formData.language}
                                                                        onChange={(e) => setFormData({ ...formData, language: e.target.value })}
                                                                        className="w-full p-2 border rounded"
                                                                  >
                                                                        <option value="en">🇺🇸 English</option>
                                                                        <option value="es">🇪🇸 Spanish</option>
                                                                        <option value="fr">🇫🇷 French</option>
                                                                        <option value="de">🇩🇪 German</option>
                                                                        <option value="it">🇮🇹 Italian</option>
                                                                        <option value="pt">🇵🇹 Portuguese</option>
                                                                        <option value="ar">🇸🇦 Arabic</option>
                                                                  </select>
                                                            </div>
                                                            <div>
                                                                  <Label htmlFor="quality">Quality</Label>
                                                                  <select
                                                                        id="quality"
                                                                        value={formData.quality}
                                                                        onChange={(e) => setFormData({ ...formData, quality: e.target.value })}
                                                                        className="w-full p-2 border rounded"
                                                                  >
                                                                        <option value="4K">4K Ultra HD</option>
                                                                        <option value="HD">HD (1080p)</option>
                                                                        <option value="720p">HD (720p)</option>
                                                                        <option value="SD">SD (480p)</option>
                                                                  </select>
                                                            </div>
                                                      </div>

                                                      <div className="flex space-x-2">
                                                            <Button type="submit">Add Link</Button>
                                                            <Button
                                                                  type="button"
                                                                  variant="outline"
                                                                  onClick={() => setShowAddForm(false)}
                                                            >
                                                                  Cancel
                                                            </Button>
                                                      </div>
                                                </form>
                                          </CardContent>
                                    </Card>
                              )}

                              {/* Broadcast Links List */}
                              <div className="space-y-4">
                                    {mockBroadcastLinks.length === 0 ? (
                                          <div className="text-center py-8">
                                                <Radio className="mx-auto h-12 w-12 text-gray-400" />
                                                <p className="mt-2 text-gray-600">No broadcast links available</p>
                                                <p className="text-sm text-gray-500">Add a link to get started</p>
                                          </div>
                                    ) : (
                                          mockBroadcastLinks.map((link) => (
                                                <Card key={link.id}>
                                                      <CardContent className="p-4">
                                                            <div className="flex items-center justify-between">
                                                                  <div className="flex-1">
                                                                        <div className="flex items-center space-x-3">
                                                                              <div className="flex items-center space-x-2">
                                                                                    <Globe className="h-4 w-4 text-blue-600" />
                                                                                    <span className="font-medium">{link.title}</span>
                                                                              </div>
                                                                              <Badge className={getQualityColor(link.quality)}>
                                                                                    <Zap className="mr-1 h-3 w-3" />
                                                                                    {link.quality}
                                                                              </Badge>
                                                                              <Badge variant="outline">
                                                                                    {getLanguageFlag(link.language)} {link.language.toUpperCase()}
                                                                              </Badge>
                                                                        </div>
                                                                        <p className="text-sm text-gray-600 mt-1 truncate">
                                                                              {link.url}
                                                                        </p>
                                                                  </div>
                                                                  <div className="flex items-center space-x-2">
                                                                        <Button
                                                                              size="sm"
                                                                              variant="outline"
                                                                              onClick={() => window.open(link.url, '_blank')}
                                                                        >
                                                                              <ExternalLink className="h-4 w-4" />
                                                                        </Button>
                                                                        <Button
                                                                              size="sm"
                                                                              variant="outline"
                                                                              onClick={() => console.log('Delete link:', link.id)}
                                                                        >
                                                                              <X className="h-4 w-4" />
                                                                        </Button>
                                                                  </div>
                                                            </div>
                                                      </CardContent>
                                                </Card>
                                          ))
                                    )}
                              </div>
                        </div>
                  </div>
            </div>
      );
};