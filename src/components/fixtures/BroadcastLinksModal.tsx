'use client';

import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Fixture } from '@/lib/types/api';
import { Plus, ExternalLink, Radio, Globe, Zap, X, Edit, Trash2, Save, MessageCircle } from 'lucide-react';
import { broadcastLinksApi, BroadcastLink, CreateBroadcastLinkData, UpdateBroadcastLinkData } from '@/lib/api/broadcast-links';
import { TableSkeleton } from '@/components/ui/skeleton';

interface BroadcastLinksModalProps {
      isOpen: boolean;
      onClose: () => void;
      fixture: Fixture;
}

export const BroadcastLinksModal: React.FC<BroadcastLinksModalProps> = ({
      isOpen,
      onClose,
      fixture,
}) => {
      const [showAddForm, setShowAddForm] = useState(false);
      const [editingLink, setEditingLink] = useState<BroadcastLink | null>(null);
      const [formData, setFormData] = useState({
            title: '',
            url: '',
            comment: '',
            language: 'English',
            quality: 'HD',
      });

      const queryClient = useQueryClient();

      // Fetch broadcast links for this fixture
      const { data: linksData, isLoading, error } = useQuery({
            queryKey: ['broadcast-links', fixture.externalId || fixture.id],
            queryFn: () => broadcastLinksApi.getBroadcastLinksByFixture(fixture.externalId || fixture.id),
            enabled: isOpen,
      });

      const links = linksData?.data || [];

      // Create mutation
      const createMutation = useMutation({
            mutationFn: (data: CreateBroadcastLinkData) => broadcastLinksApi.createBroadcastLink(data),
            onSuccess: () => {
                  queryClient.invalidateQueries({ queryKey: ['broadcast-links'] });
                  setShowAddForm(false);
                  resetForm();
            },
            onError: (error: any) => {
                  console.error('Failed to create broadcast link:', error.message);
            },
      });

      // Update mutation
      const updateMutation = useMutation({
            mutationFn: ({ id, data }: { id: number; data: UpdateBroadcastLinkData }) =>
                  broadcastLinksApi.updateBroadcastLink(id, data),
            onSuccess: () => {
                  queryClient.invalidateQueries({ queryKey: ['broadcast-links'] });
                  setEditingLink(null);
                  resetForm();
            },
            onError: (error: any) => {
                  console.error('Failed to update broadcast link:', error.message);
            },
      });

      // Delete mutation
      const deleteMutation = useMutation({
            mutationFn: (id: number) => broadcastLinksApi.deleteBroadcastLink(id),
            onSuccess: () => {
                  queryClient.invalidateQueries({ queryKey: ['broadcast-links'] });
            },
            onError: (error: any) => {
                  console.error('Failed to delete broadcast link:', error.message);
            },
      });

      const resetForm = () => {
            setFormData({
                  title: '',
                  url: '',
                  comment: '',
                  language: 'English',
                  quality: 'HD'
            });
      };

      const handleSubmit = (e: React.FormEvent) => {
            e.preventDefault();

            if (!formData.title.trim() || !formData.url.trim() || !formData.comment.trim()) {
                  return;
            }

            const submitData = {
                  fixtureId: fixture.externalId || fixture.id,
                  linkName: formData.title.trim(),
                  linkUrl: formData.url.trim(),
                  linkComment: formData.comment.trim(),
                  language: formData.language,
                  quality: formData.quality,
            };

            if (editingLink) {
                  updateMutation.mutate({ id: editingLink.id, data: submitData });
            } else {
                  createMutation.mutate(submitData);
            }
      };

      const handleEdit = (link: BroadcastLink) => {
            setEditingLink(link);
            setFormData({
                  title: link.linkName,
                  url: link.linkUrl,
                  comment: link.linkComment || '',
                  language: link.language || 'English',
                  quality: link.quality || 'HD',
            });
            setShowAddForm(true);
      };

      const handleDelete = (link: BroadcastLink) => {
            if (confirm(`Are you sure you want to delete "${link.linkName}"?`)) {
                  deleteMutation.mutate(link.id);
            }
      };

      const handleCancel = () => {
            setShowAddForm(false);
            setEditingLink(null);
            resetForm();
      };

      const getQualityColor = (quality: string) => {
            switch (quality.toLowerCase()) {
                  case '4k':
                  case 'uhd':
                        return 'bg-purple-100 text-purple-800';
                  case 'hd':
                  case '1080p':
                        return 'bg-blue-100 text-blue-800';
                  case 'sd':
                  case '720p':
                        return 'bg-green-100 text-green-800';
                  default:
                        return 'bg-gray-100 text-gray-800';
            }
      };

      const getLanguageFlag = (language: string) => {
            const flags: Record<string, string> = {
                  en: '🇺🇸',
                  es: '🇪🇸',
                  fr: '🇫🇷',
                  de: '🇩🇪',
                  it: '🇮🇹',
                  pt: '🇵🇹',
                  ar: '🇸🇦',
            };
            return flags[language] || '🌐';
      };

      if (!isOpen) return null;

      return (
            <div className="fixed inset-0 z-50 bg-black/50 flex items-center justify-center p-4">
                  <div className="bg-white rounded-lg max-w-4xl w-full max-h-[80vh] overflow-y-auto">
                        {/* Header */}
                        <div className="p-6 border-b">
                              <div className="flex items-center justify-between">
                                    <div>
                                          <h2 className="text-xl font-semibold flex items-center">
                                                <Radio className="mr-2 h-5 w-5" />
                                                Broadcast Links - {fixture.homeTeamName} vs {fixture.awayTeamName}
                                          </h2>
                                          <p className="text-gray-600 mt-1">Manage streaming links for this fixture</p>
                                    </div>
                                    <Button variant="outline" size="sm" onClick={onClose}>
                                          <X className="h-4 w-4" />
                                    </Button>
                              </div>
                        </div>

                        {/* Content */}
                        <div className="p-6 space-y-6">
                              {/* Add New Link Button */}
                              {!showAddForm && (
                                    <div className="flex justify-between items-center">
                                          <div className="text-sm text-gray-600">
                                                {links.length} broadcast link{links.length !== 1 ? 's' : ''} available
                                          </div>
                                          <Button onClick={() => setShowAddForm(true)}>
                                                <Plus className="h-4 w-4 mr-2" />
                                                Add Link
                                          </Button>
                                    </div>
                              )}

                              {/* Add Form */}
                              {showAddForm && (
                                    <Card>
                                          <CardHeader>
                                                <div className="flex items-center justify-between">
                                                      <CardTitle className="text-lg">
                                                            {editingLink ? 'Edit Broadcast Link' : 'Add New Broadcast Link'}
                                                      </CardTitle>
                                                      <Button type="button" variant="outline" size="sm" onClick={handleCancel}>
                                                            <X className="h-4 w-4" />
                                                      </Button>
                                                </div>
                                          </CardHeader>
                                          <CardContent>
                                                <form onSubmit={handleSubmit} className="space-y-4">
                                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                            <div>
                                                                  <Label htmlFor="title">Title</Label>
                                                                  <Input
                                                                        id="title"
                                                                        value={formData.title}
                                                                        onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                                                                        placeholder="e.g., ESPN HD Stream"
                                                                        required
                                                                  />
                                                            </div>
                                                            <div>
                                                                  <Label htmlFor="url">URL</Label>
                                                                  <Input
                                                                        id="url"
                                                                        type="url"
                                                                        value={formData.url}
                                                                        onChange={(e) => setFormData({ ...formData, url: e.target.value })}
                                                                        placeholder="https://..."
                                                                        required
                                                                  />
                                                            </div>
                                                            <div>
                                                                  <Label htmlFor="language">Language</Label>
                                                                  <select
                                                                        id="language"
                                                                        value={formData.language}
                                                                        onChange={(e) => setFormData({ ...formData, language: e.target.value })}
                                                                        className="w-full p-2 border rounded"
                                                                  >
                                                                        <option value="en">🇺🇸 English</option>
                                                                        <option value="es">🇪🇸 Spanish</option>
                                                                        <option value="fr">🇫🇷 French</option>
                                                                        <option value="de">🇩🇪 German</option>
                                                                        <option value="it">🇮🇹 Italian</option>
                                                                        <option value="pt">🇵🇹 Portuguese</option>
                                                                        <option value="ar">🇸🇦 Arabic</option>
                                                                  </select>
                                                            </div>
                                                            <div>
                                                                  <Label htmlFor="quality">Quality</Label>
                                                                  <select
                                                                        id="quality"
                                                                        value={formData.quality}
                                                                        onChange={(e) => setFormData({ ...formData, quality: e.target.value })}
                                                                        className="w-full p-2 border rounded"
                                                                  >
                                                                        <option value="4K">4K Ultra HD</option>
                                                                        <option value="HD">HD (1080p)</option>
                                                                        <option value="720p">HD (720p)</option>
                                                                        <option value="SD">SD (480p)</option>
                                                                  </select>
                                                            </div>
                                                      </div>

                                                      <div>
                                                            <Label htmlFor="comment">Comment</Label>
                                                            <Input
                                                                  id="comment"
                                                                  value={formData.comment}
                                                                  onChange={(e) => setFormData({ ...formData, comment: e.target.value })}
                                                                  placeholder="e.g., Official HD stream with English commentary"
                                                                  required
                                                            />
                                                      </div>

                                                      <div className="flex justify-end space-x-2 pt-4">
                                                            <Button type="button" variant="outline" onClick={handleCancel}>
                                                                  Cancel
                                                            </Button>
                                                            <Button
                                                                  type="submit"
                                                                  disabled={createMutation.isLoading || updateMutation.isLoading}
                                                            >
                                                                  <Save className="h-4 w-4 mr-2" />
                                                                  {editingLink ? 'Update' : 'Add'} Link
                                                            </Button>
                                                      </div>
                                                </form>
                                          </CardContent>
                                    </Card>
                              )}

                              {/* Broadcast Links List */}
                              <div className="space-y-4">
                                    {isLoading ? (
                                          <TableSkeleton rows={3} columns={1} />
                                    ) : error ? (
                                          <div className="text-center py-8">
                                                <p className="text-red-600 mb-4">Failed to load broadcast links</p>
                                                <Button onClick={() => console.log('Mock: Retry loading')}>
                                                      Try Again
                                                </Button>
                                          </div>
                                    ) : links.length === 0 ? (
                                          <div className="text-center py-8">
                                                <Radio className="mx-auto h-12 w-12 text-gray-400" />
                                                <p className="mt-2 text-gray-600">No broadcast links added yet</p>
                                                <p className="text-sm text-gray-500">Add a link to get started</p>
                                                {!showAddForm && (
                                                      <Button onClick={() => setShowAddForm(true)} className="mt-4">
                                                            <Plus className="h-4 w-4 mr-2" />
                                                            Add First Link
                                                      </Button>
                                                )}
                                          </div>
                                    ) : (
                                          links.map((link: BroadcastLink) => (
                                                <Card key={link.id}>
                                                      <CardContent className="p-4">
                                                            <div className="flex items-center justify-between">
                                                                  <div className="flex-1">
                                                                        <div className="flex items-center space-x-3 mb-2">
                                                                              <div className="flex items-center space-x-2">
                                                                                    {link.linkName.toLowerCase().includes('comment') || link.linkName.toLowerCase().includes('chat') ? (
                                                                                          <MessageCircle className="h-4 w-4 text-blue-600" />
                                                                                    ) : (
                                                                                          <Radio className="h-4 w-4 text-green-600" />
                                                                                    )}
                                                                                    <span className="font-medium">{link.linkName}</span>
                                                                              </div>
                                                                              <Badge className={getQualityColor(link.quality || 'HD')}>
                                                                                    <Zap className="mr-1 h-3 w-3" />
                                                                                    {link.quality || 'HD'}
                                                                              </Badge>
                                                                              <Badge variant="outline">
                                                                                    {getLanguageFlag(link.language || 'English')} {link.language || 'English'}
                                                                              </Badge>
                                                                              <Badge variant="outline" className="bg-green-50 text-green-700">
                                                                                    Active
                                                                              </Badge>
                                                                        </div>
                                                                        <div className="text-sm text-gray-600 flex items-center space-x-2">
                                                                              <a
                                                                                    href={link.linkUrl}
                                                                                    target="_blank"
                                                                                    rel="noopener noreferrer"
                                                                                    className="text-blue-600 hover:text-blue-800 flex items-center space-x-1 truncate max-w-[300px]"
                                                                              >
                                                                                    <span className="truncate">{link.linkUrl}</span>
                                                                                    <ExternalLink className="h-3 w-3 flex-shrink-0" />
                                                                              </a>
                                                                        </div>
                                                                  </div>
                                                                  <div className="flex items-center space-x-2">
                                                                        <Button
                                                                              size="sm"
                                                                              variant="outline"
                                                                              onClick={() => handleEdit(link)}
                                                                              disabled={editingLink?.id === link.id}
                                                                        >
                                                                              <Edit className="h-4 w-4" />
                                                                        </Button>
                                                                        <Button
                                                                              size="sm"
                                                                              variant="outline"
                                                                              onClick={() => handleDelete(link)}
                                                                              disabled={deleteMutation.isLoading}
                                                                        >
                                                                              <Trash2 className="h-4 w-4" />
                                                                        </Button>
                                                                  </div>
                                                            </div>
                                                      </CardContent>
                                                </Card>
                                          ))
                                    )}
                              </div>
                        </div>
                  </div>
            </div>
      );
};