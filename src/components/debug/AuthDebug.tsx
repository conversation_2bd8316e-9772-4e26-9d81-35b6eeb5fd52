'use client';

import { useAuthStore } from '@/lib/stores/auth';
import { useAuth } from '@/lib/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export const AuthDebug = () => {
  const authStore = useAuthStore();
  const auth = useAuth();

  const testBroadcastAPI = async () => {
    try {
      console.log('🔍 Testing broadcast API...');

      const response = await fetch('/api/broadcast-links/fixture/1274453', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.accessToken}`
        }
      });

      const data = await response.json();
      console.log('📡 API Response:', { status: response.status, data });

    } catch (error) {
      console.error('❌ API Error:', error);
    }
  };

  const testDebugAPI = async () => {
    try {
      console.log('🔍 Testing debug API...');

      const response = await fetch('/api/debug-auth', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authStore.accessToken}`
        }
      });

      const data = await response.json();
      console.log('📡 Debug Response:', { status: response.status, data });

    } catch (error) {
      console.error('❌ Debug Error:', error);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto mt-4">
      <CardHeader>
        <CardTitle>Auth Debug Info</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-semibold">Auth Store:</h3>
          <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
            {JSON.stringify({
              isAuthenticated: authStore.isAuthenticated,
              hasToken: !!authStore.accessToken,
              tokenLength: authStore.accessToken?.length || 0,
              user: authStore.user?.username || 'No user',
              tokenPreview: authStore.accessToken?.substring(0, 20) + '...' || 'No token'
            }, null, 2)}
          </pre>
        </div>

        <div>
          <h3 className="font-semibold">Auth Hook:</h3>
          <pre className="bg-gray-100 p-2 rounded text-sm overflow-auto">
            {JSON.stringify({
              isAuthenticated: auth.isAuthenticated,
              isLoading: auth.isLoading,
              user: auth.user?.username || 'No user'
            }, null, 2)}
          </pre>
        </div>

        <div className="flex gap-2 flex-wrap">
          <Button onClick={testDebugAPI} variant="outline">
            Test Debug API
          </Button>
          <Button onClick={testBroadcastAPI} variant="outline">
            Test Broadcast API
          </Button>
          <Button
            onClick={() => {
              console.log('🔄 Force logout clicked');
              authStore.clearAuth();
              window.location.href = '/auth/login';
            }}
            variant="destructive"
          >
            Force Logout
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
