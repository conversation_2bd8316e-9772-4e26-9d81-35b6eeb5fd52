"use client"

import * as React from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon, X, Check, RotateCcw } from "lucide-react"

import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

interface DateFilterModalProps {
  isOpen: boolean
  onClose: () => void
  selectedDate?: Date
  onDateSelect: (date: Date | undefined) => void
  onApplyFilter: (date: Date | undefined) => void
  onResetFilter: () => void
}

export function DateFilterModal({
  isOpen,
  onClose,
  selectedDate,
  onDateSelect,
  onApplyFilter,
  onResetFilter,
}: DateFilterModalProps) {
  const [tempDate, setTempDate] = React.useState<Date | undefined>(selectedDate)

  React.useEffect(() => {
    setTempDate(selectedDate)
  }, [selectedDate, isOpen])

  const handleApply = () => {
    onApplyFilter(tempDate)
    onDateSelect(tempDate)
    onClose()
  }

  const handleReset = () => {
    setTempDate(undefined)
    onResetFilter()
    onDateSelect(undefined)
    onClose()
  }

  const handleCancel = () => {
    setTempDate(selectedDate) // Reset to original value
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px] p-0 overflow-hidden">
        <DialogHeader className="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
          <DialogTitle className="flex items-center gap-2 text-lg font-semibold text-gray-900">
            <CalendarIcon className="h-5 w-5 text-blue-600" />
            Filter by Date
          </DialogTitle>
          <DialogDescription className="text-sm text-gray-600 mt-1">
            Select a date to filter fixtures. Choose a specific date or reset to show all fixtures.
          </DialogDescription>
        </DialogHeader>

        <div className="px-6 py-6">
          {/* Selected Date Display */}
          {tempDate && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-blue-900">Selected Date:</div>
                  <div className="text-lg font-semibold text-blue-700">
                    {format(tempDate, "EEEE, MMMM d, yyyy")}
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setTempDate(undefined)}
                  className="text-blue-600 hover:text-blue-800 hover:bg-blue-100"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {/* Calendar */}
          <div className="flex justify-center">
            <Calendar
              mode="single"
              selected={tempDate}
              onSelect={setTempDate}
              initialFocus
              className="rounded-md border shadow-sm"
            />
          </div>

          {/* Quick Actions */}
          <div className="mt-4 p-3 bg-gray-50 rounded-lg">
            <div className="text-xs font-medium text-gray-700 mb-2">Quick Actions:</div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setTempDate(new Date())}
                className="text-xs"
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const tomorrow = new Date()
                  tomorrow.setDate(tomorrow.getDate() + 1)
                  setTempDate(tomorrow)
                }}
                className="text-xs"
              >
                Tomorrow
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const nextWeek = new Date()
                  nextWeek.setDate(nextWeek.getDate() + 7)
                  setTempDate(nextWeek)
                }}
                className="text-xs"
              >
                Next Week
              </Button>
            </div>
          </div>
        </div>

        <DialogFooter className="px-6 py-4 bg-gray-50 border-t flex-col sm:flex-row gap-2">
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={handleReset}
              className="flex-1 sm:flex-none"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset Filter
            </Button>
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1 sm:flex-none"
            >
              Cancel
            </Button>
          </div>
          <Button
            onClick={handleApply}
            disabled={!tempDate}
            className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700"
          >
            <Check className="h-4 w-4 mr-2" />
            Apply Filter
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
