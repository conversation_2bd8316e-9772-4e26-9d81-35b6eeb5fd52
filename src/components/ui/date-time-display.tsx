'use client';

import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  formatToLocalTime,
  formatToUTC,
  getTimezoneDisplayName,
} from '@/lib/utils/date-time';

interface DateTimeDisplayProps {
  dateTime: string;
  className?: string;
  showDate?: boolean;
  showTime?: boolean;
  format?: string;
}

export const DateTimeDisplay: React.FC<DateTimeDisplayProps> = ({
  dateTime,
  className = '',
  showDate = true,
  showTime = true,
  format,
}) => {
  const localDate = formatToLocalTime(dateTime, 'dd/MM/yyyy');
  const localTime = formatToLocalTime(dateTime, 'HH:mm');
  const utcDate = formatToUTC(dateTime, 'dd/MM/yyyy');
  const utcTime = formatToUTC(dateTime, 'HH:mm');
  const timezoneInfo = getTimezoneDisplayName();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`cursor-help ${className}`}>
            {showDate && (
              <div className="font-medium text-sm">
                {localDate}
              </div>
            )}
            {showTime && (
              <div className="text-gray-600 text-sm">
                {localTime}
              </div>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <div className="space-y-1">
            <div className="font-medium">UTC Time:</div>
            <div className="text-sm">{utcDate} {utcTime} (GMT+0)</div>
            <div className="text-xs text-gray-400 mt-2">
              Local: {timezoneInfo}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
