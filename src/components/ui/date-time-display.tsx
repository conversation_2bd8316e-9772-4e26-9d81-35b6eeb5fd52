'use client';

import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  formatToLocalTime,
  formatToUTC,
  getTimezoneDisplayName,
} from '@/lib/utils/date-time';

interface DateTimeDisplayProps {
  dateTime: string;
  className?: string;
  showDate?: boolean;
  showTime?: boolean;
  format?: string;
}

interface ClickableDateTimeDisplayProps extends DateTimeDisplayProps {
  onClick?: () => void;
  isClickable?: boolean;
}

export const DateTimeDisplay: React.FC<ClickableDateTimeDisplayProps> = ({
  dateTime,
  className = '',
  showDate = true,
  showTime = true,
  format,
  onClick,
  isClickable = false,
}) => {
  const localDate = formatToLocalTime(dateTime, 'dd/MM/yyyy');
  const localTime = formatToLocalTime(dateTime, 'HH:mm');
  const utcDate = formatToUTC(dateTime, 'dd/MM/yyyy');
  const utcTime = formatToUTC(dateTime, 'HH:mm');
  const timezoneInfo = getTimezoneDisplayName();

  const containerClasses = `
    text-center cursor-help transition-all duration-200
    ${isClickable ? 'hover:bg-blue-50 hover:shadow-sm rounded-md p-2 cursor-pointer' : ''}
    ${className}
  `.trim();

  const content = (
    <div className={containerClasses} onClick={isClickable ? onClick : undefined}>
      {showDate && (
        <div className="font-semibold text-sm text-gray-900 leading-tight">
          {localDate}
        </div>
      )}
      {showTime && (
        <div className="text-gray-600 text-xs font-medium mt-1 leading-tight">
          {localTime}
        </div>
      )}
      {isClickable && (
        <div className="text-xs text-blue-500 opacity-0 group-hover:opacity-100 transition-opacity mt-1">
          Click to filter
        </div>
      )}
    </div>
  );

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={isClickable ? 'group' : ''}>
            {content}
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs bg-gray-900 text-white">
          <div className="space-y-2">
            <div className="font-medium text-sm">UTC Time:</div>
            <div className="text-sm">{utcDate} {utcTime} (GMT+0)</div>
            <div className="text-xs text-gray-300 mt-2 border-t border-gray-700 pt-2">
              Local: {timezoneInfo}
            </div>
            {isClickable && (
              <div className="text-xs text-blue-300 border-t border-gray-700 pt-2">
                💡 Click to filter by this date
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
