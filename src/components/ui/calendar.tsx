"use client"

import * as React from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { DayPicker } from "react-day-picker"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"

export type CalendarProps = React.ComponentProps<typeof DayPicker>

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  const today = new Date()
  today.setHours(0, 0, 0, 0) // Reset time to start of day for accurate comparison

  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn("p-4", className)}
      classNames={{
        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
        month: "space-y-4",
        caption: "flex justify-center pt-1 relative items-center mb-4",
        caption_label: "text-lg font-semibold text-gray-900",
        nav: "space-x-1 flex items-center",
        nav_button: cn(
          "h-8 w-8 bg-white border border-gray-200 rounded-lg p-0 hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 shadow-sm"
        ),
        nav_button_previous: "absolute left-1",
        nav_button_next: "absolute right-1",
        table: "w-full border-collapse space-y-1",
        head_row: "flex mb-2",
        head_cell: "text-gray-500 rounded-md w-10 h-10 font-medium text-sm flex items-center justify-center",
        row: "flex w-full mt-1",
        cell: "relative p-0",
        day: cn(
          "h-10 w-10 p-0 font-medium text-sm rounded-lg transition-all duration-200 relative",
          "hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        ),
        day_range_end: "day-range-end",
        day_selected: "bg-blue-600 text-white hover:bg-blue-700 shadow-lg scale-105",
        day_today: "bg-blue-100 text-blue-900 font-bold border-2 border-blue-500",
        day_outside: "text-gray-300 opacity-50",
        day_disabled: "text-gray-400 opacity-40 cursor-not-allowed hover:scale-100",
        day_range_middle: "aria-selected:bg-blue-100 aria-selected:text-blue-900",
        day_hidden: "invisible",
        ...classNames,
      }}
      components={{
        IconLeft: ({ ...props }) => <ChevronLeft className="h-4 w-4 text-gray-600" />,
        IconRight: ({ ...props }) => <ChevronRight className="h-4 w-4 text-gray-600" />,
        Day: ({ date, displayMonth, ...props }) => {
          // Check if date exists
          if (!date) {
            return null;
          }

          const dayDate = new Date(date)
          dayDate.setHours(0, 0, 0, 0)

          const isPast = dayDate < today
          const isToday = dayDate.getTime() === today.getTime()
          const isFuture = dayDate > today

          let dayClasses = cn(
            "h-10 w-10 p-0 font-medium text-sm rounded-lg transition-all duration-200 relative",
            "hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2",
            "flex items-center justify-center"
          )

          if (isPast) {
            dayClasses = cn(dayClasses,
              "text-gray-800 bg-gray-100 hover:bg-gray-200",
              "cursor-pointer"
            )
          } else if (isToday) {
            dayClasses = cn(dayClasses,
              "bg-blue-100 text-blue-900 font-bold border-2 border-blue-500",
              "hover:bg-blue-200 cursor-pointer"
            )
          } else if (isFuture) {
            dayClasses = cn(dayClasses,
              "text-blue-600 bg-blue-50 hover:bg-blue-100 hover:text-blue-700",
              "cursor-pointer font-semibold"
            )
          }

          // Handle selected state
          if (props.selected) {
            dayClasses = cn(
              "bg-blue-600 text-white hover:bg-blue-700 shadow-lg scale-105",
              "border-2 border-blue-600"
            )
          }

          return (
            <button
              {...props}
              className={dayClasses}
              type="button"
            >
              {date.getDate()}
              {isToday && (
                <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-500 rounded-full"></div>
              )}
            </button>
          )
        },
      }}
      {...props}
    />
  )
}
Calendar.displayName = "Calendar"

export { Calendar }
