'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  LayoutDashboard,
  Calendar,
  Trophy,
  Users,
  Settings,
  Radio,
  UserCheck,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  X,
  Menu,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { usePermissions } from '@/lib/middleware/auth-guard';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface NavItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  children?: NavItem[];
  requiredRole?: 'admin' | 'editor' | 'moderator';
}

const navigation: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
  },
  {
    title: 'Fixtures',
    icon: Calendar,
    children: [
      { title: 'All Fixtures', href: '/dashboard/fixtures', icon: Calendar },
      { title: 'Live & Upcoming', href: '/dashboard/fixtures/live', icon: Radio },
      { title: 'Sync Data', href: '/dashboard/fixtures/sync', icon: RefreshCw, requiredRole: 'admin' },
    ],
  },
  {
    title: 'Leagues',
    href: '/dashboard/leagues',
    icon: Trophy,
  },
  {
    title: 'Teams',
    href: '/dashboard/teams',
    icon: Users,
  },
  {
    title: 'Broadcast Links',
    href: '/dashboard/broadcast',
    icon: Radio,
    requiredRole: 'editor',
  },
  {
    title: 'User Management',
    icon: UserCheck,
    requiredRole: 'admin',
    children: [
      { title: 'System Users', href: '/dashboard/users/system', icon: UserCheck },
      { title: 'Registered Users', href: '/dashboard/users/registered', icon: Users },
      { title: 'Tier Statistics', href: '/dashboard/users/tiers', icon: Trophy },
    ],
  },
  {
    title: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
  },
  {
    title: 'API Test',
    href: '/dashboard/api-test',
    icon: RefreshCw,
    requiredRole: 'admin',
  },
];

interface SidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  className?: string;
}

export const Sidebar: React.FC<SidebarProps> = ({
  isOpen = true,
  onClose,
  className
}) => {
  const pathname = usePathname();
  const { hasRole } = usePermissions();
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [isMobile, setIsMobile] = useState(false);

  // Check if mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Close sidebar on mobile when route changes
  useEffect(() => {
    if (isMobile && onClose) {
      onClose();
    }
  }, [pathname, isMobile, onClose]);

  const toggleExpanded = (title: string) => {
    setExpandedItems(prev =>
      prev.includes(title)
        ? prev.filter(item => item !== title)
        : [...prev, title]
    );
  };

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(href + '/');
  };

  const canAccessItem = (item: NavItem): boolean => {
    if (!item.requiredRole) return true;
    return hasRole(item.requiredRole);
  };

  const renderNavItem = (item: NavItem, level = 0) => {
    if (!canAccessItem(item)) return null;

    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.title);
    const Icon = item.icon;

    if (hasChildren) {
      return (
        <div key={item.title}>
          <button
            onClick={() => toggleExpanded(item.title)}
            className={cn(
              'w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-colors',
              'text-gray-700 hover:text-gray-900 hover:bg-gray-100',
              level > 0 && 'ml-4'
            )}
          >
            <div className="flex items-center">
              <Icon className="mr-3 h-4 w-4" />
              <span>{item.title}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-2 text-xs">
                  {item.badge}
                </Badge>
              )}
            </div>
            {isExpanded ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </button>
          {isExpanded && (
            <div className="mt-1 space-y-1">
              {item.children?.map(child => renderNavItem(child, level + 1))}
            </div>
          )}
        </div>
      );
    }

    return (
      <Link
        key={item.title}
        href={item.href!}
        className={cn(
          'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
          level > 0 && 'ml-4',
          isActive(item.href!)
            ? 'bg-blue-100 text-blue-700'
            : 'text-gray-700 hover:text-gray-900 hover:bg-gray-100'
        )}
      >
        <Icon className="mr-3 h-4 w-4" />
        <span>{item.title}</span>
        {item.badge && (
          <Badge variant="secondary" className="ml-2 text-xs">
            {item.badge}
          </Badge>
        )}
      </Link>
    );
  };

  // Mobile overlay
  if (isMobile) {
    return (
      <>
        {/* Backdrop */}
        {isOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
            onClick={onClose}
          />
        )}

        {/* Mobile Sidebar */}
        <aside
          className={cn(
            'fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out md:hidden',
            isOpen ? 'translate-x-0' : '-translate-x-full',
            className
          )}
        >
          {/* Mobile Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="p-4 space-y-2 overflow-y-auto h-full pb-20">
            {navigation.map(item => renderNavItem(item))}
          </nav>
        </aside>
      </>
    );
  }

  // Desktop Sidebar
  return (
    <aside className={cn(
      'w-64 bg-white border-r border-gray-200 min-h-screen',
      className
    )}>
      <nav className="p-4 space-y-2">
        {navigation.map(item => renderNavItem(item))}
      </nav>
    </aside>
  );
};
