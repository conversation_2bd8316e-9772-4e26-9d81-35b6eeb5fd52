'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Construction } from 'lucide-react';
import Link from 'next/link';

interface PlaceholderPageProps {
  title: string;
  description?: string;
  iconName?: string;
  backUrl?: string;
  backLabel?: string;
}

export const PlaceholderPage: React.FC<PlaceholderPageProps> = ({
  title,
  description = 'This page is under development and will be available soon.',
  icon: Icon = Construction,
  backUrl = '/dashboard',
  backLabel = 'Back to Dashboard',
}) => {
  return (
    <div className="flex items-center justify-center min-h-[60vh]">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100 dark:bg-orange-900/20">
            <Icon className="h-8 w-8 text-orange-600 dark:text-orange-400" />
          </div>
          <CardTitle className="text-xl">{title}</CardTitle>
          <CardDescription className="text-base">
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-sm text-muted-foreground">
              <p>Features coming soon:</p>
              <ul className="mt-2 space-y-1 text-left">
                <li>• Data management interface</li>
                <li>• CRUD operations</li>
                <li>• Advanced filtering</li>
                <li>• Export functionality</li>
              </ul>
            </div>

            <Button asChild className="w-full">
              <Link href={backUrl}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                {backLabel}
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
