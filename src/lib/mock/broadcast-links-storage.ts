// In-memory storage for mock broadcast links
// This will be replaced with real database when API is implemented

interface MockBroadcastLink {
  id: number;
  fixtureId: number;
  linkName: string;
  linkUrl: string;
  linkComment: string;
  language: string;
  quality: string;
  addedBy: number;
  createdAt: string;
  updatedAt: string;
}

// In-memory storage with some initial data for testing
let broadcastLinksStorage: MockBroadcastLink[] = [
  {
    id: 1,
    fixtureId: 1274453,
    linkName: 'ESPN HD Stream',
    linkUrl: 'https://espn.com/live',
    linkComment: 'Official HD stream with English commentary',
    language: 'English',
    quality: 'HD',
    addedBy: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  {
    id: 2,
    fixtureId: 1274453,
    linkName: 'Sky Sports 4K',
    linkUrl: 'https://skysports.com/live',
    linkComment: '4K Ultra HD stream with multiple language options',
    language: 'English',
    quality: '4K',
    addedBy: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  }
];
let nextId = 3;

export const mockBroadcastLinksStorage = {
  // Get all links for a fixture
  getByFixture: (fixtureId: number): MockBroadcastLink[] => {
    return broadcastLinksStorage.filter(link => link.fixtureId === fixtureId);
  },

  // Create new link
  create: (data: Omit<MockBroadcastLink, 'id' | 'addedBy' | 'createdAt' | 'updatedAt'>): MockBroadcastLink => {
    const newLink: MockBroadcastLink = {
      ...data,
      id: nextId++,
      addedBy: 1, // Mock user ID
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    broadcastLinksStorage.push(newLink);
    return newLink;
  },

  // Update link
  update: (id: number, data: Partial<Omit<MockBroadcastLink, 'id' | 'addedBy' | 'createdAt'>>): MockBroadcastLink | null => {
    const index = broadcastLinksStorage.findIndex(link => link.id === id);
    if (index === -1) return null;

    broadcastLinksStorage[index] = {
      ...broadcastLinksStorage[index],
      ...data,
      updatedAt: new Date().toISOString(),
    };

    return broadcastLinksStorage[index];
  },

  // Delete link
  delete: (id: number): boolean => {
    const index = broadcastLinksStorage.findIndex(link => link.id === id);
    if (index === -1) return false;

    broadcastLinksStorage.splice(index, 1);
    return true;
  },

  // Get by ID
  getById: (id: number): MockBroadcastLink | null => {
    return broadcastLinksStorage.find(link => link.id === id) || null;
  },

  // Clear all (for testing)
  clear: (): void => {
    broadcastLinksStorage = [];
    nextId = 1;
  },

  // Get all (for debugging)
  getAll: (): MockBroadcastLink[] => {
    return [...broadcastLinksStorage];
  }
};
