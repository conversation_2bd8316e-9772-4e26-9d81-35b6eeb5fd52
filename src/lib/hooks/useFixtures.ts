import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fixturesApi, FixtureFilters, SyncResponse } from '@/lib/api/fixtures';
import { Fixture, PaginatedResponse } from '@/lib/types/api';

export const useFixtures = (filters: FixtureFilters = {}) => {
  const queryClient = useQueryClient();

  // Get fixtures query
  const fixturesQuery = useQuery({
    queryKey: ['fixtures', filters],
    queryFn: () => fixturesApi.getFixtures(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes for fixtures data
  });

  // Get upcoming and live fixtures
  const upcomingLiveQuery = useQuery({
    queryKey: ['fixtures', 'upcoming-live', filters],
    queryFn: () => fixturesApi.getUpcomingAndLive(filters),
    staleTime: 30 * 1000, // 30 seconds for live data
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
  });

  return {
    // Fixtures data
    fixtures: fixturesQuery.data?.data || [],
    fixturesMeta: fixturesQuery.data?.meta,
    isFixturesLoading: fixturesQuery.isLoading,
    fixturesError: fixturesQuery.error,
    
    // Upcoming and live data
    upcomingLive: upcomingLiveQuery.data?.data || [],
    upcomingLiveMeta: upcomingLiveQuery.data?.meta,
    isUpcomingLiveLoading: upcomingLiveQuery.isLoading,
    upcomingLiveError: upcomingLiveQuery.error,
    
    // Refetch functions
    refetchFixtures: fixturesQuery.refetch,
    refetchUpcomingLive: upcomingLiveQuery.refetch,
  };
};

export const useFixture = (externalId: number) => {
  const fixtureQuery = useQuery({
    queryKey: ['fixtures', externalId],
    queryFn: () => fixturesApi.getFixtureById(externalId),
    enabled: !!externalId,
  });

  return {
    fixture: fixtureQuery.data?.data,
    isLoading: fixtureQuery.isLoading,
    error: fixtureQuery.error,
    refetch: fixtureQuery.refetch,
  };
};

export const useFixtureStatistics = (externalId: number) => {
  const statisticsQuery = useQuery({
    queryKey: ['fixtures', externalId, 'statistics'],
    queryFn: () => fixturesApi.getFixtureStatistics(externalId),
    enabled: !!externalId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    statistics: statisticsQuery.data?.data,
    isLoading: statisticsQuery.isLoading,
    error: statisticsQuery.error,
    refetch: statisticsQuery.refetch,
  };
};

export const useTeamSchedule = (teamId: number, filters: any = {}) => {
  const scheduleQuery = useQuery({
    queryKey: ['fixtures', 'team-schedule', teamId, filters],
    queryFn: () => fixturesApi.getTeamSchedule(teamId, filters),
    enabled: !!teamId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return {
    schedule: scheduleQuery.data?.data || [],
    scheduleMeta: scheduleQuery.data?.meta,
    isLoading: scheduleQuery.isLoading,
    error: scheduleQuery.error,
    refetch: scheduleQuery.refetch,
  };
};

// Sync operations hooks (Admin only)
export const useFixtureSync = () => {
  const queryClient = useQueryClient();

  // Trigger season sync
  const seasonSyncMutation = useMutation({
    mutationFn: fixturesApi.triggerSeasonSync,
    onSuccess: () => {
      // Invalidate all fixture queries after sync
      queryClient.invalidateQueries({ queryKey: ['fixtures'] });
    },
  });

  // Trigger daily sync
  const dailySyncMutation = useMutation({
    mutationFn: fixturesApi.triggerDailySync,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['fixtures'] });
    },
  });

  // Get sync status
  const syncStatusQuery = useQuery({
    queryKey: ['fixtures', 'sync-status'],
    queryFn: fixturesApi.getSyncStatus,
    staleTime: 1 * 60 * 1000, // 1 minute
  });

  return {
    // Sync status
    syncStatus: syncStatusQuery.data,
    isSyncStatusLoading: syncStatusQuery.isLoading,
    syncStatusError: syncStatusQuery.error,
    
    // Sync mutations
    triggerSeasonSync: seasonSyncMutation.mutate,
    triggerDailySync: dailySyncMutation.mutate,
    
    // Sync mutation states
    isSeasonSyncLoading: seasonSyncMutation.isPending,
    seasonSyncError: seasonSyncMutation.error,
    seasonSyncData: seasonSyncMutation.data,
    
    isDailySyncLoading: dailySyncMutation.isPending,
    dailySyncError: dailySyncMutation.error,
    dailySyncData: dailySyncMutation.data,
    
    // Refetch
    refetchSyncStatus: syncStatusQuery.refetch,
  };
};
