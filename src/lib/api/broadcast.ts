import { apiClient } from './client';
import { BroadcastLink, PaginatedResponse } from '@/lib/types/api';

export interface BroadcastFilters {
  page?: number;
  limit?: number;
  fixtureId?: number;
  addedBy?: number;
}

export interface CreateBroadcastLinkData {
  fixtureId: number;
  linkName: string;
  linkUrl: string;
  linkComment: string;
}

export interface UpdateBroadcastLinkData {
  linkName?: string;
  linkUrl?: string;
  linkComment?: string;
}

export const broadcastApi = {
  // Get broadcast links with filters
  getBroadcastLinks: async (filters: BroadcastFilters = {}): Promise<PaginatedResponse<BroadcastLink>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<BroadcastLink>>(
      `/broadcast-links?${params.toString()}`
    );
    return response;
  },

  // Get broadcast link by ID
  getBroadcastLinkById: async (id: number): Promise<BroadcastLink> => {
    const response = await apiClient.get<BroadcastLink>(`/broadcast-links/${id}`);
    return response;
  },

  // Get broadcast links for a specific fixture
  getBroadcastLinksByFixture: async (fixtureId: number): Promise<PaginatedResponse<BroadcastLink>> => {
    return broadcastApi.getBroadcastLinks({ fixtureId });
  },

  // Create broadcast link (Editor+ access)
  createBroadcastLink: async (data: CreateBroadcastLinkData): Promise<BroadcastLink> => {
    const response = await apiClient.post<BroadcastLink>('/broadcast-links', data);
    return response;
  },

  // Update broadcast link (Admin/Moderator: any link, Editor: own only)
  updateBroadcastLink: async (id: number, data: UpdateBroadcastLinkData): Promise<BroadcastLink> => {
    const response = await apiClient.patch<BroadcastLink>(`/broadcast-links/${id}`, data);
    return response;
  },

  // Delete broadcast link (Admin/Moderator: any link, Editor: own only)
  deleteBroadcastLink: async (id: number): Promise<{ message: string }> => {
    const response = await apiClient.delete<{ message: string }>(`/broadcast-links/${id}`);
    return response;
  },

  // Bulk operations (Admin/Moderator only)
  bulkDeleteBroadcastLinks: async (ids: number[]): Promise<{ message: string; deletedCount: number }> => {
    const response = await apiClient.post<{ message: string; deletedCount: number }>(
      '/broadcast-links/bulk-delete',
      { ids }
    );
    return response;
  },

  // Get user's own broadcast links (Editor+)
  getMyBroadcastLinks: async (filters: Omit<BroadcastFilters, 'addedBy'> = {}): Promise<PaginatedResponse<BroadcastLink>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<BroadcastLink>>(
      `/broadcast-links/my-links?${params.toString()}`
    );
    return response;
  },

  // Validate broadcast link URL
  validateBroadcastUrl: async (url: string): Promise<{ isValid: boolean; message?: string }> => {
    const response = await apiClient.post<{ isValid: boolean; message?: string }>(
      '/broadcast-links/validate-url',
      { url }
    );
    return response;
  },
};
