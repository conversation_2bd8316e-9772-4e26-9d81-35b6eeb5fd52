import { PaginatedResponse } from '@/lib/types/api';
import { useAuthStore } from '@/lib/stores/auth';

// Helper function to get auth headers
const getAuthHeaders = () => {
  const authState = useAuthStore.getState();
  const token = authState.accessToken;

  console.log('🔑 Auth Debug:', {
    isAuthenticated: authState.isAuthenticated,
    hasToken: !!token,
    tokenLength: token?.length || 0,
    tokenPreview: token?.substring(0, 20) + '...' || 'No token'
  });

  if (!token) {
    console.error('❌ No access token found in auth store!');

    // Try to get from localStorage as fallback
    const fallbackToken = localStorage.getItem('accessToken');
    if (fallbackToken) {
      console.log('🔄 Using fallback token from localStorage');
      return {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${fallbackToken}`
      };
    }
  }

  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

export interface BroadcastLink {
  id: number;
  fixtureId: number;
  linkName: string;        // API uses linkName
  linkUrl: string;         // API uses linkUrl
  linkComment: string;     // API uses linkComment
  language?: string;
  quality?: string;
  addedBy: number;
  createdAt: string;
  updatedAt: string;
}

export interface BroadcastLinkFilters {
  page?: number;
  limit?: number;
  fixtureId?: number;
  language?: string;
  quality?: string;
  isActive?: boolean;
  search?: string;
}

export interface CreateBroadcastLinkData {
  fixtureId: number;
  linkName: string;        // API uses linkName
  linkUrl: string;         // API uses linkUrl
  linkComment: string;     // API uses linkComment (required)
  language?: string;
  quality?: string;
}

export interface UpdateBroadcastLinkData {
  linkName?: string;       // API uses linkName
  linkUrl?: string;        // API uses linkUrl
  linkComment?: string;    // API uses linkComment
  language?: string;
  quality?: string;
}

export const broadcastLinksApi = {
  // Get all broadcast links with filters
  getBroadcastLinks: async (filters: BroadcastLinkFilters = {}): Promise<PaginatedResponse<BroadcastLink>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await fetch(`/api/broadcast-links?${params.toString()}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);
    }

    return await response.json();
  },

  // Get broadcast links for a specific fixture
  getBroadcastLinksByFixture: async (fixtureId: number): Promise<{ data: BroadcastLink[] }> => {
    const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch broadcast links for fixture: ${response.statusText}`);
    }

    return await response.json();
  },

  // Get single broadcast link
  getBroadcastLinkById: async (id: number): Promise<{ data: BroadcastLink }> => {
    const response = await fetch(`/api/broadcast-links/${id}`, {
      method: 'GET',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);
    }

    return await response.json();
  },

  // Create new broadcast link
  createBroadcastLink: async (data: CreateBroadcastLinkData): Promise<{ data: BroadcastLink }> => {
    const response = await fetch('/api/broadcast-links', {
      method: 'POST',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to create broadcast link: ${response.statusText}`);
    }

    return await response.json();
  },

  // Update broadcast link
  updateBroadcastLink: async (id: number, data: UpdateBroadcastLinkData): Promise<{ data: BroadcastLink }> => {
    const response = await fetch(`/api/broadcast-links/${id}`, {
      method: 'PUT',
      headers: getAuthHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to update broadcast link: ${response.statusText}`);
    }

    return await response.json();
  },

  // Delete broadcast link
  deleteBroadcastLink: async (id: number): Promise<void> => {
    const response = await fetch(`/api/broadcast-links/${id}`, {
      method: 'DELETE',
      headers: getAuthHeaders(),
    });

    if (!response.ok) {
      throw new Error(`Failed to delete broadcast link: ${response.statusText}`);
    }
  },
};
