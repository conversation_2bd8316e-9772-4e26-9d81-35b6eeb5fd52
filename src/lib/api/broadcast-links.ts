import { PaginatedResponse } from '@/lib/types/api';

export interface BroadcastLink {
  id: number;
  fixtureId: number;
  title: string;
  url: string;
  language: string;
  quality: string;
  isActive: boolean;
  priority: number;
  createdAt: string;
  updatedAt: string;
}

export interface BroadcastLinkFilters {
  page?: number;
  limit?: number;
  fixtureId?: number;
  language?: string;
  quality?: string;
  isActive?: boolean;
  search?: string;
}

export interface CreateBroadcastLinkData {
  fixtureId: number;
  title: string;
  url: string;
  language: string;
  quality: string;
  isActive?: boolean;
  priority?: number;
}

export interface UpdateBroadcastLinkData {
  title?: string;
  url?: string;
  language?: string;
  quality?: string;
  isActive?: boolean;
  priority?: number;
}

export const broadcastLinksApi = {
  // Get all broadcast links with filters
  getBroadcastLinks: async (filters: BroadcastLinkFilters = {}): Promise<PaginatedResponse<BroadcastLink>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await fetch(`/api/broadcast-links?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch broadcast links: ${response.statusText}`);
    }

    return await response.json();
  },

  // Get broadcast links for a specific fixture
  getBroadcastLinksByFixture: async (fixtureId: number): Promise<{ data: BroadcastLink[] }> => {
    const response = await fetch(`/api/broadcast-links/fixture/${fixtureId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch broadcast links for fixture: ${response.statusText}`);
    }

    return await response.json();
  },

  // Get single broadcast link
  getBroadcastLinkById: async (id: number): Promise<{ data: BroadcastLink }> => {
    const response = await fetch(`/api/broadcast-links/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch broadcast link: ${response.statusText}`);
    }

    return await response.json();
  },

  // Create new broadcast link
  createBroadcastLink: async (data: CreateBroadcastLinkData): Promise<{ data: BroadcastLink }> => {
    const response = await fetch('/api/broadcast-links', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to create broadcast link: ${response.statusText}`);
    }

    return await response.json();
  },

  // Update broadcast link
  updateBroadcastLink: async (id: number, data: UpdateBroadcastLinkData): Promise<{ data: BroadcastLink }> => {
    const response = await fetch(`/api/broadcast-links/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to update broadcast link: ${response.statusText}`);
    }

    return await response.json();
  },

  // Delete broadcast link
  deleteBroadcastLink: async (id: number): Promise<void> => {
    const response = await fetch(`/api/broadcast-links/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete broadcast link: ${response.statusText}`);
    }
  },
};
