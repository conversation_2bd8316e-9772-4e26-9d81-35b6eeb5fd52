import { apiClient } from './client';
import { League, PaginatedResponse } from '@/lib/types/api';

export interface LeagueFilters {
  page?: number;
  limit?: number;
  country?: string;
  active?: boolean;
}

export interface CreateLeagueData {
  externalId: number;
  name: string;
  country: string;
  logo: string;
  flag: string;
  season: number;
  active: boolean;
  type: string;
}

export interface UpdateLeagueData {
  name?: string;
  country?: string;
  logo?: string;
  flag?: string;
  season?: number;
  active?: boolean;
  type?: string;
}

export const leaguesApi = {
  // Public endpoint
  getLeagues: async (filters: LeagueFilters = {}): Promise<PaginatedResponse<League>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<League>>(
      `/football/leagues?${params.toString()}`
    );
    return response;
  },

  // Requires authentication
  getLeagueById: async (externalId: number, season?: number): Promise<League> => {
    const params = season ? `?season=${season}` : '';
    const response = await apiClient.get<League>(`/football/leagues/${externalId}${params}`);
    return response;
  },

  // Editor+ access required
  createLeague: async (data: CreateLeagueData): Promise<League> => {
    const response = await apiClient.post<League>('/football/leagues', data);
    return response;
  },

  // Editor+ access required
  updateLeague: async (id: number, data: UpdateLeagueData): Promise<League> => {
    const response = await apiClient.patch<League>(`/football/leagues/${id}`, data);
    return response;
  },

  // Helper methods for common operations
  getActiveLeagues: async (): Promise<PaginatedResponse<League>> => {
    return leaguesApi.getLeagues({ active: true });
  },

  getLeaguesByCountry: async (country: string): Promise<PaginatedResponse<League>> => {
    return leaguesApi.getLeagues({ country });
  },

  toggleLeagueStatus: async (id: number, active: boolean): Promise<League> => {
    return leaguesApi.updateLeague(id, { active });
  },
};
