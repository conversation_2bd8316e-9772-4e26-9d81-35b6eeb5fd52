import { apiClient } from './client';
import { AuthResponse, LoginCredentials, SystemUser } from '@/lib/types/api';

export const authApi = {
  // System Authentication
  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      console.log('🔐 Attempting API login...');
      const response = await apiClient.post<AuthResponse>('/system-auth/login', credentials);
      console.log('✅ API login successful');
      return response;
    } catch (error: any) {
      console.warn('⚠️ API login failed, using mock data for development:', error.message);

      // Mock response for development
      if (credentials.username === 'admin' && credentials.password === 'admin123456') {
        const mockResponse: AuthResponse = {
          user: {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            fullName: 'System Administrator',
            role: 'admin',
            isActive: true,
            lastLoginAt: new Date().toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          },
          accessToken: 'mock-access-token-' + Date.now(),
          refreshToken: 'mock-refresh-token-' + Date.now(),
        };

        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 500));
        return mockResponse;
      } else {
        throw new Error('Invalid credentials. Try admin/admin123456 for demo.');
      }
    }
  },

  logout: async (refreshToken: string): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/system-auth/logout', {
      refreshToken,
    });
    return response;
  },

  logoutFromAllDevices: async (): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/system-auth/logout-all');
    return response;
  },

  refreshToken: async (refreshToken: string): Promise<{ accessToken: string }> => {
    const response = await apiClient.post<{ accessToken: string }>('/system-auth/refresh', {
      refreshToken,
    });
    return response;
  },

  getProfile: async (): Promise<SystemUser> => {
    const response = await apiClient.get<SystemUser>('/system-auth/profile');
    return response;
  },

  updateProfile: async (data: Partial<SystemUser>): Promise<SystemUser> => {
    const response = await apiClient.put<SystemUser>('/system-auth/profile', data);
    return response;
  },

  changePassword: async (data: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<{ message: string }> => {
    const response = await apiClient.post<{ message: string }>('/system-auth/change-password', data);
    return response;
  },

  // System User Management (Admin only)
  createUser: async (userData: {
    username: string;
    email: string;
    password: string;
    role: 'admin' | 'editor' | 'moderator';
    fullName?: string;
  }): Promise<SystemUser> => {
    const response = await apiClient.post<SystemUser>('/system-auth/users', userData);
    return response;
  },

  updateUser: async (id: number, data: Partial<SystemUser>): Promise<SystemUser> => {
    const response = await apiClient.put<SystemUser>(`/system-auth/users/${id}`, data);
    return response;
  },
};
