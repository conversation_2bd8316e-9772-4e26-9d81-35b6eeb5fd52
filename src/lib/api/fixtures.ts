import { apiClient } from './client';
import { Fixture, PaginatedResponse } from '@/lib/types/api';

export interface FixtureFilters {
  page?: number;
  limit?: number;
  league?: number;
  season?: number;
  team?: number;
  venue?: number;
  date?: string;
  status?: string;
  timezone?: string;
  from?: string;
  to?: string;
}

export interface SyncResponse {
  status: string;
  message: string;
  fixturesUpserted?: number;
  details?: {
    seasonsProcessed: number[];
    leaguesProcessed: number;
    totalFixtures: number;
    duration: string;
    timestamp: string;
  };
}

export interface SyncStatus {
  lastSync: string;
  fixtures: number;
  errors: any[];
}

export const fixturesApi = {
  // Public endpoints - Using Next.js API proxy
  getFixtures: async (filters: FixtureFilters = {}): Promise<PaginatedResponse<Fixture>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    // Use Next.js API proxy instead of direct API call
    const response = await fetch(`/api/fixtures?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch fixtures: ${response.statusText}`);
    }

    return await response.json();
  },

  getFixtureById: async (externalId: number): Promise<{ data: Fixture; status: number }> => {
    // Use Next.js API proxy instead of direct API call
    const response = await fetch(`/api/fixtures/${externalId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch fixture: ${response.statusText}`);
    }

    return await response.json();
  },

  // Upcoming and Live fixtures (Public) - Using Next.js API proxy
  getUpcomingAndLive: async (filters: {
    leagueId?: number;
    limit?: number;
    page?: number;
  } = {}): Promise<PaginatedResponse<Fixture>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    // Use Next.js API proxy instead of direct API call
    const response = await fetch(`/api/fixtures/live?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch live fixtures: ${response.statusText}`);
    }

    return await response.json();
  },

  // Team schedule (Requires auth)
  getTeamSchedule: async (
    teamId: number,
    filters: {
      from?: string;
      to?: string;
      limit?: number;
      page?: number;
    } = {}
  ): Promise<PaginatedResponse<Fixture>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<Fixture>>(
      `/football/fixtures/schedules/${teamId}?${params.toString()}`
    );
    return response;
  },

  // Fixture statistics (Requires auth)
  getFixtureStatistics: async (externalId: number): Promise<{
    data: Array<{
      team: { id: number; name: string };
      statistics: Array<{ type: string; value: string | number }>;
    }>;
    status: number;
  }> => {
    const response = await apiClient.get<{
      data: Array<{
        team: { id: number; name: string };
        statistics: Array<{ type: string; value: string | number }>;
      }>;
      status: number;
    }>(`/football/fixtures/statistics/${externalId}`);
    return response;
  },

  // Admin only - Sync operations
  triggerSeasonSync: async (): Promise<SyncResponse> => {
    const response = await apiClient.get<SyncResponse>('/football/fixtures/sync/fixtures');
    return response;
  },

  triggerDailySync: async (): Promise<SyncResponse> => {
    const response = await apiClient.get<SyncResponse>('/football/fixtures/sync/daily');
    return response;
  },

  // Editor+ - Sync status
  getSyncStatus: async (): Promise<SyncStatus> => {
    const response = await apiClient.get<SyncStatus>('/football/fixtures/sync/status');
    return response;
  },

  // CRUD operations - Using Next.js API proxy
  createFixture: async (data: any): Promise<Fixture> => {
    const response = await fetch('/api/fixtures', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to create fixture: ${response.statusText}`);
    }

    return await response.json();
  },

  updateFixture: async (externalId: number, data: any): Promise<Fixture> => {
    const response = await fetch(`/api/fixtures/${externalId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`Failed to update fixture: ${response.statusText}`);
    }

    return await response.json();
  },

  deleteFixture: async (externalId: number): Promise<void> => {
    const response = await fetch(`/api/fixtures/${externalId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to delete fixture: ${response.statusText}`);
    }
  },

  // Aliases for consistency
  getFixture: async (externalId: number): Promise<Fixture> => {
    const response = await fixturesApi.getFixtureById(externalId);
    return response.data;
  },
};
