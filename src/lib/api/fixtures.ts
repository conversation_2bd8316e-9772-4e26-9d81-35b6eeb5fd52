import { apiClient } from './client';
import { Fixture, PaginatedResponse } from '@/lib/types/api';

export interface FixtureFilters {
  page?: number;
  limit?: number;
  league?: number;
  season?: number;
  team?: number;
  venue?: number;
  date?: string;
  status?: string;
  timezone?: string;
  from?: string;
  to?: string;
}

export interface SyncResponse {
  status: string;
  message: string;
  fixturesUpserted?: number;
  details?: {
    seasonsProcessed: number[];
    leaguesProcessed: number;
    totalFixtures: number;
    duration: string;
    timestamp: string;
  };
}

export interface SyncStatus {
  lastSync: string;
  fixtures: number;
  errors: any[];
}

export const fixturesApi = {
  // Public endpoints
  getFixtures: async (filters: FixtureFilters = {}): Promise<PaginatedResponse<Fixture>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<Fixture>>(
      `/football/fixtures?${params.toString()}`
    );
    return response;
  },

  getFixtureById: async (externalId: number): Promise<{ data: Fixture; status: number }> => {
    const response = await apiClient.get<{ data: Fixture; status: number }>(
      `/football/fixtures/${externalId}`
    );
    return response;
  },

  // Upcoming and Live fixtures (Public)
  getUpcomingAndLive: async (filters: {
    leagueId?: number;
    limit?: number;
    page?: number;
  } = {}): Promise<PaginatedResponse<Fixture>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<Fixture>>(
      `/football/fixtures/upcoming-and-live?${params.toString()}`
    );
    return response;
  },

  // Team schedule (Requires auth)
  getTeamSchedule: async (
    teamId: number,
    filters: {
      from?: string;
      to?: string;
      limit?: number;
      page?: number;
    } = {}
  ): Promise<PaginatedResponse<Fixture>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await apiClient.get<PaginatedResponse<Fixture>>(
      `/football/fixtures/schedules/${teamId}?${params.toString()}`
    );
    return response;
  },

  // Fixture statistics (Requires auth)
  getFixtureStatistics: async (externalId: number): Promise<{
    data: Array<{
      team: { id: number; name: string };
      statistics: Array<{ type: string; value: string | number }>;
    }>;
    status: number;
  }> => {
    const response = await apiClient.get<{
      data: Array<{
        team: { id: number; name: string };
        statistics: Array<{ type: string; value: string | number }>;
      }>;
      status: number;
    }>(`/football/fixtures/statistics/${externalId}`);
    return response;
  },

  // Admin only - Sync operations
  triggerSeasonSync: async (): Promise<SyncResponse> => {
    const response = await apiClient.get<SyncResponse>('/football/fixtures/sync/fixtures');
    return response;
  },

  triggerDailySync: async (): Promise<SyncResponse> => {
    const response = await apiClient.get<SyncResponse>('/football/fixtures/sync/daily');
    return response;
  },

  // Editor+ - Sync status
  getSyncStatus: async (): Promise<SyncStatus> => {
    const response = await apiClient.get<SyncStatus>('/football/fixtures/sync/status');
    return response;
  },

  // CRUD operations (if needed)
  createFixture: async (data: any): Promise<Fixture> => {
    const response = await apiClient.post<Fixture>('/football/fixtures', data);
    return response;
  },

  updateFixture: async (externalId: number, data: any): Promise<Fixture> => {
    const response = await apiClient.patch<Fixture>(`/football/fixtures/${externalId}`, data);
    return response;
  },
};
