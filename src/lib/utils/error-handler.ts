import { AxiosError } from 'axios';

export interface ApiError {
  message: string;
  statusCode?: number;
  error?: string;
  details?: any;
}

export const handleApiError = (error: unknown): ApiError => {
  // Handle Axios errors
  if (error instanceof AxiosError) {
    const response = error.response;
    
    if (response?.data) {
      return {
        message: response.data.message || 'An error occurred',
        statusCode: response.status,
        error: response.data.error,
        details: response.data.details,
      };
    }
    
    // Network errors
    if (error.code === 'NETWORK_ERROR') {
      return {
        message: 'Network error. Please check your connection.',
        statusCode: 0,
        error: 'NETWORK_ERROR',
      };
    }
    
    // Timeout errors
    if (error.code === 'ECONNABORTED') {
      return {
        message: 'Request timeout. Please try again.',
        statusCode: 0,
        error: 'TIMEOUT',
      };
    }
    
    return {
      message: error.message || 'An unexpected error occurred',
      statusCode: response?.status,
      error: 'UNKNOWN_ERROR',
    };
  }
  
  // Handle other types of errors
  if (error instanceof Error) {
    return {
      message: error.message,
      error: 'CLIENT_ERROR',
    };
  }
  
  // Fallback for unknown errors
  return {
    message: 'An unexpected error occurred',
    error: 'UNKNOWN_ERROR',
  };
};

export const getErrorMessage = (error: unknown): string => {
  const apiError = handleApiError(error);
  return apiError.message;
};

export const isAuthError = (error: unknown): boolean => {
  if (error instanceof AxiosError) {
    return error.response?.status === 401;
  }
  return false;
};

export const isForbiddenError = (error: unknown): boolean => {
  if (error instanceof AxiosError) {
    return error.response?.status === 403;
  }
  return false;
};

export const isNetworkError = (error: unknown): boolean => {
  if (error instanceof AxiosError) {
    return error.code === 'NETWORK_ERROR' || !error.response;
  }
  return false;
};

export const isValidationError = (error: unknown): boolean => {
  if (error instanceof AxiosError) {
    return error.response?.status === 400;
  }
  return false;
};

export const isServerError = (error: unknown): boolean => {
  if (error instanceof AxiosError) {
    const status = error.response?.status;
    return status ? status >= 500 : false;
  }
  return false;
};

// Error messages for common scenarios
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Unable to connect to the server. Please check your internet connection.',
  TIMEOUT: 'Request timed out. Please try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action. Please log in.',
  FORBIDDEN: 'You do not have permission to access this resource.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'A server error occurred. Please try again later.',
  UNKNOWN_ERROR: 'An unexpected error occurred. Please try again.',
} as const;

export const getErrorMessageByType = (error: unknown): string => {
  if (isNetworkError(error)) return ERROR_MESSAGES.NETWORK_ERROR;
  if (isAuthError(error)) return ERROR_MESSAGES.UNAUTHORIZED;
  if (isForbiddenError(error)) return ERROR_MESSAGES.FORBIDDEN;
  if (isValidationError(error)) return ERROR_MESSAGES.VALIDATION_ERROR;
  if (isServerError(error)) return ERROR_MESSAGES.SERVER_ERROR;
  
  return getErrorMessage(error) || ERROR_MESSAGES.UNKNOWN_ERROR;
};
