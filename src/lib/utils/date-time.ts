import { format, parseISO, isValid } from 'date-fns';
import { formatInTimeZone } from 'date-fns-tz';

/**
 * Get user's timezone
 */
export const getUserTimezone = (): string => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone;
};

/**
 * Format date to local timezone
 */
export const formatToLocalTime = (
  dateString: string | Date,
  formatStr: string = 'dd/MM/yyyy HH:mm'
): string => {
  try {
    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;

    if (!isValid(date)) {
      return 'Invalid Date';
    }

    const userTimezone = getUserTimezone();
    return formatInTimeZone(date, userTimezone, formatStr);
  } catch (error) {
    console.error('Error formatting date to local time:', error);
    return 'Invalid Date';
  }
};

/**
 * Format date to UTC
 */
export const formatToUTC = (
  dateString: string | Date,
  formatStr: string = 'dd/MM/yyyy HH:mm'
): string => {
  try {
    const date = typeof dateString === 'string' ? parseISO(dateString) : dateString;

    if (!isValid(date)) {
      return 'Invalid Date';
    }

    return formatInTimeZone(date, 'UTC', formatStr);
  } catch (error) {
    console.error('Error formatting date to UTC:', error);
    return 'Invalid Date';
  }
};

/**
 * Get timezone offset display
 */
export const getTimezoneOffset = (): string => {
  const offset = new Date().getTimezoneOffset();
  const hours = Math.floor(Math.abs(offset) / 60);
  const minutes = Math.abs(offset) % 60;
  const sign = offset <= 0 ? '+' : '-';

  return `GMT${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
};

/**
 * Get timezone display name
 */
export const getTimezoneDisplayName = (): string => {
  const timezone = getUserTimezone();
  const offset = getTimezoneOffset();

  // Get short timezone name
  const shortName = new Intl.DateTimeFormat('en', {
    timeZoneName: 'short',
    timeZone: timezone,
  })
    .formatToParts(new Date())
    .find(part => part.type === 'timeZoneName')?.value || '';

  return `${shortName} (${offset})`;
};

/**
 * Check if date matches selected date (ignoring time)
 */
export const isSameDate = (date1: string | Date, date2: string | Date): boolean => {
  try {
    const d1 = typeof date1 === 'string' ? parseISO(date1) : date1;
    const d2 = typeof date2 === 'string' ? parseISO(date2) : date2;

    if (!isValid(d1) || !isValid(d2)) {
      return false;
    }

    return format(d1, 'yyyy-MM-dd') === format(d2, 'yyyy-MM-dd');
  } catch (error) {
    return false;
  }
};

/**
 * Convert local date to UTC for API filtering
 */
export const convertLocalDateToUTC = (localDate: Date): string => {
  try {
    // Convert local date to UTC by adjusting for timezone offset
    const utcDate = new Date(localDate.getTime() - (localDate.getTimezoneOffset() * 60000));
    return format(utcDate, 'yyyy-MM-dd');
  } catch (error) {
    console.error('Error converting local date to UTC:', error);
    return format(localDate, 'yyyy-MM-dd');
  }
};

/**
 * Parse UTC date string and convert to local Date object
 */
export const parseUTCToLocal = (utcDateString: string): Date | null => {
  try {
    const utcDate = parseISO(utcDateString);

    if (!isValid(utcDate)) {
      return null;
    }

    // Return the date as-is since parseISO already handles UTC conversion
    return utcDate;
  } catch (error) {
    console.error('Error parsing UTC to local:', error);
    return null;
  }
};
