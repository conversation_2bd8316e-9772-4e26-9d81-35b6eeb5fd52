// API Response Types
export interface ApiResponse<T> {
  data: T;
  status: number;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
}

// Authentication Types
export interface SystemUser {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  role: 'admin' | 'editor' | 'moderator';
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  accessToken: string;
  refreshToken: string;
  user: SystemUser;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

// Football Data Types
export interface League {
  id: number;
  externalId: number;
  name: string;
  country: string;
  logo: string;
  flag: string;
  season: number;
  active: boolean;
  type: string;
}

export interface Team {
  id: number;
  externalId: number;
  name: string;
  code?: string;
  country: string;
  founded?: number;
  logo: string;
}

export interface Fixture {
  id: number;
  externalId: number;
  date: string;
  status: string;
  homeTeam: Team;
  awayTeam: Team;
  league: League;
  venue?: {
    name: string;
    city: string;
  };
  score: {
    home: number | null;
    away: number | null;
  };
}

// Broadcast Link Types
export interface BroadcastLink {
  id: number;
  fixtureId: number;
  linkName: string;
  linkUrl: string;
  linkComment: string;
  addedBy: number;
  createdAt: string;
  updatedAt: string;
}

// User Management Types
export interface RegisteredUser {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  tier: 'free' | 'premium' | 'enterprise';
  isActive: boolean;
  isEmailVerified: boolean;
  apiCallsUsed: number;
  apiCallsLimit: number | null;
  apiCallsRemaining: number | null;
  hasActiveSubscription: boolean;
  subscriptionEndDate?: string;
  lastLoginAt?: string;
  createdAt: string;
}

// Image Upload Types
export interface UploadedImage {
  id: string;
  originalName: string;
  filename: string;
  size: number;
  mimeType: string;
  category: 'leagues' | 'teams' | 'flags' | 'venues' | 'general';
  url: string;
  path: string;
  uploadedAt: string;
  uploadedBy: number;
  description?: string;
}
