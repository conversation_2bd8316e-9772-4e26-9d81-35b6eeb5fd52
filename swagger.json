{"openapi": "3.0.0", "paths": {"/football/fixtures/upcoming-and-live": {"get": {"description": "\n    Retrieve upcoming and live football fixtures with smart filtering.\n\n    **Features:**\n    - Real-time fixture status updates\n    - Smart time-based filtering (2.5-hour window)\n    - Optimized performance (96% API call reduction)\n    - Pagination support\n    - No authentication required\n\n    **Status Logic:**\n    - UPCOMING: Fixtures starting in 5-10 minutes\n    - LIVE: Fixtures currently in progress\n    - Real-time updates every 10 seconds\n    ", "operationId": "FixtureController_getUpcomingAndLiveFixtures", "parameters": [{"name": "leagueId", "required": false, "in": "query", "description": "Filter by specific league ID", "schema": {"example": 39, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of fixtures per page (default: 10, max: 100)", "schema": {"example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination (default: 1)", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Upcoming and live fixtures retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 868847, "date": "2025-05-24T15:00:00.000Z", "status": "UPCOMING", "homeTeam": {"id": 33, "name": "Manchester United", "logo": "https://media.api-sports.io/football/teams/33.png"}, "awayTeam": {"id": 34, "name": "Newcastle", "logo": "https://media.api-sports.io/football/teams/34.png"}, "league": {"id": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png"}, "venue": {"name": "Old Trafford", "city": "Manchester"}, "score": {"home": null, "away": null}}], "meta": {"totalItems": 25, "totalPages": 3, "currentPage": 1, "limit": 10}, "status": 200}}}}}, "summary": "Get Upcoming and Live Fixtures", "tags": ["Football - Fixtures"]}}, "/football/fixtures/sync/fixtures": {"get": {"description": "\n    Manually trigger synchronization of fixtures for active seasons.\n\n    **🔒 AUTHENTICATION REQUIRED:**\n    This endpoint requires System User authentication with Admin role.\n\n    **IMPORTANT: Swagger UI Authentication Setup:**\n    1. First login to get token: POST /system-auth/login\n    2. <PERSON><PERSON> the \"accessToken\" from response\n    3. Click \"Authorize\" button (🔓) at top of Swagger UI\n    4. In \"Value\" field, enter: Bearer YOUR_ACCESS_TOKEN\n    5. Click \"Authorize\" and \"Close\"\n    6. Now all protected endpoints will include Authorization header\n\n    **Step-by-Step Authentication:**\n\n    **Step 1: Get Admin Token**\n    ```bash\n    curl -X POST http://localhost:3000/system-auth/login \\\n      -H \"Content-Type: application/json\" \\\n      -d '{\"username\": \"admin\", \"password\": \"admin123456\"}'\n    ```\n\n    **Step 2: Copy accessToken from response:**\n    ```json\n    {\n      \"accessToken\": \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************.example_token_here\"\n    }\n    ```\n\n    **Step 3: Use in Swagger UI:**\n    - Click \"Authorize\" button (🔓)\n    - Enter: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************.example_token_here`\n    - Click \"Authorize\"\n\n    **Admin Credentials for Testing:**\n    - Username: admin\n    - Password: admin123456\n    - Role: admin (full system access)\n\n    **Features:**\n    - Syncs current and previous year fixtures (2024, 2025)\n    - Only processes active leagues (18 leagues)\n    - Batch processing with error isolation\n    - Returns detailed sync statistics\n    - Comprehensive error handling and logging\n\n    **Technical Details:**\n    - Processes ~1,250 fixtures per sync\n    - Uses smart upsert with time-based filtering\n    - Batch size: 50 fixtures per API call\n    - Estimated duration: 30-60 seconds\n    - Memory efficient with streaming processing\n\n    **Use Cases:**\n    - Initial data population for new deployments\n    - Manual data refresh after API issues\n    - Recovery from failed automated sync jobs\n    - Testing sync functionality in development\n    - Maintenance and data consistency checks\n\n    **Expected Response Time:**\n    - Small leagues: 5-15 seconds\n    - Large leagues: 30-60 seconds\n    - Full sync: 1-2 minutes\n\n    **Rate Limiting:**\n    - Admin users: No rate limits\n    - Respects API Football rate limits (100 calls/day)\n\n    **Example Request Headers:**\n    ```\n    GET /football/fixtures/sync/fixtures HTTP/1.1\n    Host: localhost:3000\n    Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************.example_token_here\n    Content-Type: application/json\n    ```\n\n    **Troubleshooting Swagger UI:**\n    - If still getting 401 after authorization, refresh the page and re-authorize\n    - Make sure token starts with \"Bearer \" (with space)\n    - Check token expiration (tokens expire after 1 hour)\n    - Verify you're using admin credentials (not editor/moderator)\n    ", "operationId": "FixtureController_triggerSeasonFixturesSync", "parameters": [], "responses": {"200": {"description": "Season fixtures sync triggered successfully", "content": {"application/json": {"example": {"status": "Sync triggered", "fixturesUpserted": 1250, "message": "Successfully synced fixtures for 2024 and 2025 seasons", "details": {"seasonsProcessed": [2024, 2025], "leaguesProcessed": 18, "totalFixtures": 1250, "duration": "45.2 seconds", "timestamp": "2025-05-25T10:30:00.000Z"}}}}}, "401": {"description": "Unauthorized - System authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401, "help": {"solution": "You need to authenticate with a valid System User account", "steps": ["1. Login: POST /system-auth/login with {\"username\": \"admin\", \"password\": \"admin123456\"}", "2. <PERSON><PERSON> the accessToken from response", "3. Add header: Authorization: Bearer YOUR_ACCESS_TOKEN", "4. Or use Swagger UI \"Authorize\" button"], "testCredentials": {"admin": {"username": "admin", "password": "admin123456", "role": "admin"}, "editor": {"username": "editor1", "password": "editor123456", "role": "editor"}, "moderator": {"username": "moderator1", "password": "moderator123456", "role": "moderator"}}, "note": "This endpoint requires Admin role - only admin credentials will work"}}}}}, "403": {"description": "Forbidden - Admin access required", "content": {"application/json": {"example": {"message": "Forbidden resource", "error": "Forbidden", "statusCode": 403, "help": {"reason": "Your account does not have sufficient permissions", "required": "Admin role", "yourRole": "editor", "solution": "Contact system administrator to upgrade your role to admin", "adminActions": ["Data synchronization operations", "System configuration changes", "User management", "Manual sync triggers"], "alternativeEndpoints": ["GET /football/fixtures/sync/status (Editor+ can view sync status)", "GET /football/fixtures (Public - no auth required)", "GET /football/fixtures/:id (Public - no auth required)"]}}}}}, "500": {"description": "Sync operation failed", "content": {"application/json": {"example": {"status": "Error", "message": "Failed to sync season fixtures: API rate limit exceeded", "error": {"type": "API_RATE_LIMIT_EXCEEDED", "details": "API Football rate limit reached (100 calls/day)", "timestamp": "2025-05-25T10:30:00.000Z", "retryAfter": "24 hours", "troubleshooting": ["Check API Football subscription status", "Verify API key is valid and active", "Wait for rate limit reset (daily at 00:00 UTC)", "Contact API Football support if issue persists"], "commonErrors": {"API_RATE_LIMIT_EXCEEDED": "Daily API call limit reached", "API_KEY_INVALID": "API Football key is invalid or expired", "NETWORK_ERROR": "Connection to API Football failed", "DATABASE_ERROR": "Database connection or query failed", "TIMEOUT_ERROR": "Sync operation timed out (>5 minutes)"}, "monitoring": {"Check logs": "docker logs api-container | grep \"sync\"", "Check API status": "GET /football/fixtures/sync/status", "Manual retry": "Wait and retry this endpoint"}}}}}}}, "security": [{"bearer": []}], "summary": "Trigger Season Fixtures Sync (Admin Only)", "tags": ["Football - Fixtures", "Data Synchronization"]}}, "/football/fixtures/sync/daily": {"get": {"description": "\n    Manually trigger daily synchronization of all active league fixtures.\n\n    **Features:**\n    - Syncs all active leagues for current day\n    - Full data refresh with smart upsert protection\n    - Batch processing with error isolation\n    - Returns detailed sync statistics\n    - Requires admin authentication\n\n    **Use Cases:**\n    - Manual daily data refresh\n    - Recovery from failed cronjobs\n    - Testing sync functionality\n    - Initial data population\n\n    **Performance:**\n    - Smart time-based filtering\n    - Batch processing (50 fixtures per batch)\n    - Error isolation per league\n    - Cache invalidation after sync\n    ", "operationId": "FixtureController_triggerDailySync", "parameters": [], "responses": {"200": {"description": "Daily sync triggered successfully", "content": {"application/json": {"example": {"status": "Success", "message": "Daily sync completed successfully", "success": true, "stats": {"leaguesProcessed": 18, "fixturesUpserted": 245, "errors": 0, "duration": "2.3s"}}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "403": {"description": "Forbidden - Admin access required", "content": {"application/json": {"example": {"message": "Forbidden resource", "error": "Forbidden", "statusCode": 403}}}}, "500": {"description": "Daily sync failed", "content": {"application/json": {"example": {"status": "Error", "message": "Failed to trigger daily sync: API rate limit exceeded", "success": false}}}}}, "security": [{"bearer": []}], "summary": "<PERSON>gger Daily Sync (Admin Only)", "tags": ["Football - Fixtures", "Data Synchronization"]}}, "/football/fixtures/sync/status": {"get": {"description": "\n    Retrieve current synchronization status and statistics.\n\n    **Features:**\n    - Last sync timestamp (UTC)\n    - Today's fixtures count\n    - Error tracking and reporting\n    - Real-time status monitoring\n    - Requires editor+ authentication\n\n    **Use Cases:**\n    - Monitor sync health\n    - Debug sync issues\n    - Performance monitoring\n    - System status dashboard\n\n    **Response Data:**\n    - lastSync: ISO timestamp of last sync\n    - fixtures: Count of today's fixtures\n    - errors: Array of recent sync errors\n    ", "operationId": "FixtureController_getSyncStatus", "parameters": [], "responses": {"200": {"description": "Sync status retrieved successfully", "content": {"application/json": {"example": {"lastSync": "2025-05-24T10:48:24.216Z", "fixtures": 245, "errors": []}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "403": {"description": "Forbidden - Editor+ access required", "content": {"application/json": {"example": {"message": "Forbidden resource", "error": "Forbidden", "statusCode": 403}}}}}, "security": [{"bearer": []}], "summary": "Get Sync Status (Editor+)", "tags": ["Football - Fixtures", "Data Synchronization"]}}, "/football/fixtures/schedules/{teamId}": {"get": {"description": "\n    Retrieve fixtures schedule for a specific team.\n\n    **Features:**\n    - Complete team fixture history\n    - Upcoming and past matches\n    - Pagination support\n    - Date range filtering\n    - Authentication required for API usage tracking\n\n    **Tier Access:**\n    - Free: 100 API calls/month\n    - Premium: 10,000 API calls/month\n    - Enterprise: Unlimited API calls\n\n    **Use Cases:**\n    - Team fixture calendar\n    - Match history analysis\n    - Upcoming games preview\n    - Season schedule overview\n    ", "operationId": "FixtureController_getTeamSchedule", "parameters": [{"name": "teamId", "required": true, "in": "path", "description": "Team external ID (positive integer)", "schema": {"example": 33, "type": "number"}}, {"name": "to", "required": false, "in": "query", "description": "End date filter (YYYY-MM-DD)", "schema": {"example": "2025-12-31", "type": "string"}}, {"name": "from", "required": false, "in": "query", "description": "Start date filter (YYYY-MM-DD)", "schema": {"example": "2025-01-01", "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of fixtures per page", "schema": {"example": 20, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Team schedule retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 868847, "date": "2025-05-24T15:00:00.000Z", "status": "FT", "homeTeam": {"id": 33, "name": "Manchester United"}, "awayTeam": {"id": 34, "name": "Newcastle"}, "score": {"home": 2, "away": 1}, "league": {"name": "Premier League"}}], "meta": {"totalItems": 38, "totalPages": 2, "currentPage": 1, "limit": 20}}}}}, "400": {"description": "Invalid team ID", "content": {"application/json": {"example": {"message": "Invalid teamId: must be a positive integer", "error": "Bad Request", "statusCode": 400}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}}, "security": [{"bearer": []}], "summary": "Get Team Schedule", "tags": ["Football - Fixtures"]}}, "/football/fixtures/statistics/{externalId}": {"get": {"description": "\n    Retrieve detailed statistics for a specific fixture.\n\n    **Features:**\n    - Team performance statistics\n    - Match statistics and metrics\n    - Historical data analysis\n    - Authentication required for API usage tracking\n\n    **Tier Access:**\n    - Free: 100 API calls/month\n    - Premium: 10,000 API calls/month\n    - Enterprise: Unlimited API calls\n\n    **Use Cases:**\n    - Match analysis and insights\n    - Team performance comparison\n    - Statistical data for applications\n    - Sports analytics and reporting\n    ", "operationId": "FixtureController_getFixtureStatistics", "parameters": [{"name": "externalId", "required": true, "in": "path", "description": "Fixture external ID (positive integer)", "schema": {"example": 868847, "type": "number"}}], "responses": {"200": {"description": "Fixture statistics retrieved successfully", "content": {"application/json": {"example": {"data": [{"team": {"id": 33, "name": "Manchester United"}, "statistics": [{"type": "Shots on Goal", "value": 6}, {"type": "Shots off Goal", "value": 4}, {"type": "Total Shots", "value": 10}, {"type": "Ball Possession", "value": "65%"}]}], "status": 200}}}}, "400": {"description": "Invalid external ID", "content": {"application/json": {"example": {"message": "Invalid externalId: must be a positive integer", "error": "Bad Request", "statusCode": 400}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "404": {"description": "Fixture not found", "content": {"application/json": {"example": {"message": "Fixture not found", "error": "Not Found", "statusCode": 404}}}}}, "security": [{"bearer": []}], "summary": "Get Fixture Statistics", "tags": ["Football - Fixtures"]}}, "/football/fixtures": {"get": {"description": "\n    Retrieve fixtures with comprehensive filtering options.\n\n    **Access:** Public endpoint (no authentication required)\n\n    **Query Parameters:**\n    - page, limit: Pagination\n    - league, season: Filter by league/season\n    - team, venue: Filter by team/venue\n    - date: Filter by specific date\n    - status: Filter by status (NS, LIVE, FT, etc.)\n    - timezone: Timezone (default: UTC)\n    - from, to: Date range filtering\n\n    **Examples:**\n    - ?league=39&season=2024 (Premier League 2024)\n    - ?team=33&status=FT (Manchester United finished matches)\n    - ?from=2024-01-01&to=2024-12-31 (Year 2024)\n    - ?date=2024-05-24 (Specific date)\n    ", "operationId": "FixtureController_getFixtures", "parameters": [{"name": "to", "required": false, "in": "query", "description": "End date (YYYY-MM-DD)", "schema": {"example": "2024-12-31", "type": "string"}}, {"name": "from", "required": false, "in": "query", "description": "Start date (YYYY-MM-DD)", "schema": {"example": "2024-01-01", "type": "string"}}, {"name": "timezone", "required": false, "in": "query", "description": "Timezone", "schema": {"example": "UTC", "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Status (NS,LIVE,FT)", "schema": {"example": "LIVE,FT", "type": "string"}}, {"name": "date", "required": false, "in": "query", "description": "Date (YYYY-MM-DD)", "schema": {"example": "2024-05-24", "type": "string"}}, {"name": "venue", "required": false, "in": "query", "description": "Venue ID", "schema": {"example": 556, "type": "number"}}, {"name": "team", "required": false, "in": "query", "description": "Team ID", "schema": {"example": 33, "type": "number"}}, {"name": "season", "required": false, "in": "query", "description": "Season year", "schema": {"example": 2024, "type": "number"}}, {"name": "league", "required": false, "in": "query", "description": "League ID (e.g., 39 for Premier League)", "schema": {"example": 39, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Fixtures retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 868847, "date": "2025-05-24T15:00:00.000Z", "status": "FT", "homeTeam": {"id": 33, "name": "Manchester United"}, "awayTeam": {"id": 34, "name": "Newcastle"}, "score": {"home": 2, "away": 1}, "league": {"name": "Premier League"}}], "meta": {"totalItems": 1250, "totalPages": 125, "currentPage": 1, "limit": 10}}}}}}, "summary": "Get Fixtures with Filters (Public)", "tags": ["Football - Fixtures"]}, "post": {"operationId": "FixtureController_createFixture", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFixtureDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Football - Fixtures"]}}, "/football/fixtures/{externalId}": {"get": {"description": "\n    Retrieve detailed information for a specific fixture by external ID.\n\n    **Access:** Public endpoint (no authentication required)\n\n    **Parameter:**\n    - externalId: Fixture external ID (positive integer)\n\n    **Examples:**\n    - /1274453 (Dreams vs Samartex)\n    - /868847 (Manchester United vs Liverpool)\n    - /1234567 (Any fixture external ID)\n    ", "operationId": "FixtureController_getFixtureById", "parameters": [{"name": "externalId", "required": true, "in": "path", "description": "Fixture external ID", "schema": {"example": 1274453, "type": "number"}}], "responses": {"200": {"description": "Fixture retrieved successfully", "content": {"application/json": {"example": {"data": {"id": 11, "externalId": 1274453, "leagueId": 570, "leagueName": "Premier League", "season": 2024, "homeTeamName": "Dreams", "awayTeamName": "Samartex", "date": "2024-09-07T15:00:00.000Z", "status": "FT", "goalsHome": 0, "goalsAway": 0}, "status": 200}}}}, "400": {"description": "Invalid external ID"}, "404": {"description": "Fixture not found"}}, "summary": "Get Fixture by ID (Public)", "tags": ["Football - Fixtures"]}, "patch": {"operationId": "FixtureController_updateFixture", "parameters": [{"name": "externalId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateFixtureDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Football - Fixtures"]}}, "/football/leagues": {"get": {"description": "\n        Retrieve all football leagues with filtering and pagination.\n\n        **Features:**\n        - Complete league database\n        - Country-based filtering\n        - Active/inactive status filtering\n        - Pagination support\n        - No authentication required\n\n        **Popular Leagues:**\n        - Premier League (England) - ID: 39\n        - La Liga (Spain) - ID: 140\n        - Serie A (Italy) - ID: 135\n        - Bundesliga (Germany) - ID: 78\n        - Ligue 1 (France) - ID: 61\n        ", "operationId": "LeagueController_getLeagues", "parameters": [{"name": "active", "required": false, "in": "query", "description": "Filter by active status", "schema": {"example": true, "type": "boolean"}}, {"name": "country", "required": false, "in": "query", "description": "Filter by country name", "schema": {"example": "England", "type": "string"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of leagues per page", "schema": {"example": 20, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number for pagination", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Leagues retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb.svg", "season": 2025, "active": true, "type": "League"}], "meta": {"totalItems": 150, "totalPages": 8, "currentPage": 1, "limit": 20}}}}}}, "summary": "Get All Leagues", "tags": ["Football - Leagues"]}, "post": {"description": "\n        Create a new league in the system.\n\n        **Access:** Editor+ (Editor and Admin roles)\n        **Features:**\n        - Add new leagues to database\n        - Complete league information\n        - Validation and error handling\n        - Audit logging\n\n        **Use Cases:**\n        - Add new leagues from API Football\n        - Manual league creation\n        - System administration\n        - Data management\n        ", "operationId": "LeagueController_createLeague", "parameters": [], "requestBody": {"required": true, "description": "League creation data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateLeagueDto"}, "examples": {"premierLeague": {"summary": "Premier League Example", "value": {"externalId": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb.svg", "season": 2025, "active": true, "type": "League"}}}}}}, "responses": {"201": {"description": "League created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeagueResponseDto"}}}}, "400": {"description": "Invalid league data", "content": {"application/json": {"example": {"message": "Validation failed", "error": "Bad Request", "statusCode": 400}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "403": {"description": "Forbidden - Editor+ access required", "content": {"application/json": {"example": {"message": "Forbidden resource", "error": "Forbidden", "statusCode": 403}}}}, "409": {"description": "League already exists", "content": {"application/json": {"example": {"message": "League with external ID 39 already exists", "error": "Conflict", "statusCode": 409}}}}}, "security": [{"bearer": []}], "summary": "Create League (Editor+)", "tags": ["Football - Leagues"]}}, "/football/leagues/{externalId}": {"get": {"description": "\n        Retrieve detailed information for a specific league by external ID.\n\n        **Features:**\n        - Complete league information\n        - Season-specific data\n        - Team listings for the league\n        - Authentication required for API usage tracking\n\n        **Tier Access:**\n        - Free: 100 API calls/month\n        - Premium: 10,000 API calls/month\n        - Enterprise: Unlimited API calls\n\n        **Use Cases:**\n        - League profile pages\n        - Season information display\n        - Team listings for league\n        - League statistics and data\n        ", "operationId": "LeagueController_getLeagueById", "parameters": [{"name": "externalId", "required": true, "in": "path", "description": "League external ID (positive integer)", "schema": {"example": 39, "type": "number"}}, {"name": "season", "required": false, "in": "query", "description": "Season year (optional)", "schema": {"example": 2024, "type": "number"}}], "responses": {"200": {"description": "League retrieved successfully", "content": {"application/json": {"example": {"id": 1, "externalId": 39, "name": "Premier League", "country": "England", "logo": "https://media.api-sports.io/football/leagues/39.png", "flag": "https://media.api-sports.io/flags/gb.svg", "season": 2025, "active": true, "type": "League"}}}}, "400": {"description": "Invalid league ID", "content": {"application/json": {"example": {"message": "Invalid externalId: must be a positive integer", "error": "Bad Request", "statusCode": 400}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "404": {"description": "League not found", "content": {"application/json": {"example": {"message": "League not found", "error": "Not Found", "statusCode": 404}}}}}, "security": [{"bearer": []}], "summary": "Get League by ID", "tags": ["Football - Leagues"]}}, "/football/leagues/{id}": {"patch": {"description": "\n        Update an existing league in the system.\n\n        **Access:** Editor+ (Editor and Admin roles)\n        **Features:**\n        - Update league information\n        - Partial updates supported\n        - Validation and error handling\n        - Audit logging\n\n        **Use Cases:**\n        - Update league details\n        - Activate/deactivate leagues\n        - Correct league information\n        - System maintenance\n        ", "operationId": "LeagueController_updateLeague", "parameters": [{"name": "id", "required": true, "in": "path", "description": "League internal ID (positive integer)", "schema": {"example": 1, "type": "number"}}], "requestBody": {"required": true, "description": "League update data (partial)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateLeagueDto"}, "examples": {"updateActive": {"summary": "Update Active Status", "value": {"active": false}}, "updateSeason": {"summary": "Update Season", "value": {"season": 2025, "active": true}}}}}}, "responses": {"200": {"description": "League updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LeagueResponseDto"}}}}, "400": {"description": "Invalid league data or ID", "content": {"application/json": {"example": {"message": "Invalid league ID", "error": "Bad Request", "statusCode": 400}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "403": {"description": "Forbidden - Editor+ access required", "content": {"application/json": {"example": {"message": "Forbidden resource", "error": "Forbidden", "statusCode": 403}}}}, "404": {"description": "League not found", "content": {"application/json": {"example": {"message": "League not found", "error": "Not Found", "statusCode": 404}}}}}, "security": [{"bearer": []}], "summary": "Update League (Editor+)", "tags": ["Football - Leagues"]}}, "/football/teams": {"get": {"description": "\n        Retrieve teams with comprehensive filtering options.\n\n        **🔒 AUTHENTICATION REQUIRED:**\n        This endpoint requires System User authentication (any role: admin/moderator/editor).\n\n        **Quick Auth Setup in Swagger UI:**\n        1. Login: POST /system-auth/login with admin/editor/moderator credentials\n        2. Co<PERSON> \"accessToken\" from response\n        3. Click \"Authorize\" button (🔓) at top of page\n        4. Enter: Bearer YOUR_ACCESS_TOKEN\n        5. Click \"Authorize\" and \"Close\"\n\n        **Test Credentials:**\n        - Admin: {\"username\": \"admin\", \"password\": \"admin123456\"}\n        - Editor: {\"username\": \"editor1\", \"password\": \"editor123456\"}\n        - Moderator: {\"username\": \"moderator1\", \"password\": \"moderator123456\"}\n\n        **Features:**\n        - Complete team database\n        - League-based filtering\n        - Country-based filtering\n        - Pagination support\n        - Authentication required for API usage tracking\n\n        **Tier Access:**\n        - Free: 100 API calls/month\n        - Premium: 10,000 API calls/month\n        - Enterprise: Unlimited API calls\n\n        **Query Parameters:**\n        - page, limit: Pagination\n        - league, season: Filter by league/season\n        - country: Filter by country name\n\n        **Examples:**\n        - ?league=39&season=2024 (Premier League 2024 teams)\n        - ?country=England (All English teams)\n        - ?league=140&season=2024 (La Liga 2024 teams)\n\n        **Example Request:**\n        ```\n        GET /football/teams?league=39&season=2024 HTTP/1.1\n        Authorization: Bearer YOUR_ACCESS_TOKEN\n        ```\n        ", "operationId": "TeamController_getTeams", "parameters": [{"name": "country", "required": false, "in": "query", "description": "Country name", "schema": {"example": "England", "type": "string"}}, {"name": "season", "required": false, "in": "query", "description": "Season year", "schema": {"example": 2024, "type": "number"}}, {"name": "league", "required": false, "in": "query", "description": "League ID", "schema": {"example": 39, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"example": 10, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"example": 1, "type": "number"}}], "responses": {"200": {"description": "Teams retrieved successfully", "content": {"application/json": {"example": {"data": [{"id": 1, "externalId": 33, "name": "Manchester United", "code": "MUN", "country": "England", "founded": 1878, "logo": "https://media.api-sports.io/football/teams/33.png"}], "meta": {"totalItems": 20, "totalPages": 2, "currentPage": 1, "limit": 10}}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}}, "security": [{"bearer": []}], "summary": "Get Teams with Filters", "tags": ["Football - Teams"]}}, "/football/teams/statistics": {"get": {"description": "\n        Retrieve detailed team statistics for a specific league and season.\n\n        **Features:**\n        - Complete team performance data\n        - League-specific statistics\n        - Season-based analysis\n        - Authentication required for API usage tracking\n\n        **Tier Access:**\n        - Free: 100 API calls/month\n        - Premium: 10,000 API calls/month\n        - Enterprise: Unlimited API calls\n\n        **Required Parameters:**\n        - league: League ID (e.g., 39 for Premier League)\n        - season: Season year (e.g., 2024)\n        - team: Team ID (e.g., 33 for Manchester United)\n\n        **Examples:**\n        - ?league=39&season=2024&team=33 (Manchester United in Premier League 2024)\n        - ?league=140&season=2024&team=529 (Barcelona in La Liga 2024)\n        ", "operationId": "TeamController_getTeamStatistics", "parameters": [{"name": "team", "required": true, "in": "query", "description": "Team ID", "schema": {"example": 33, "type": "number"}}, {"name": "season", "required": true, "in": "query", "description": "Season year", "schema": {"example": 2024, "type": "number"}}, {"name": "league", "required": true, "in": "query", "description": "League ID", "schema": {"example": 39, "type": "number"}}], "responses": {"200": {"description": "Team statistics retrieved successfully", "content": {"application/json": {"example": {"data": {"teamId": 33, "leagueId": 39, "season": 2024, "fixtures": {"played": {"home": 19, "away": 19, "total": 38}, "wins": {"home": 12, "away": 8, "total": 20}, "draws": {"home": 4, "away": 6, "total": 10}, "loses": {"home": 3, "away": 5, "total": 8}}, "goals": {"for": {"total": {"home": 35, "away": 22, "total": 57}}, "against": {"total": {"home": 18, "away": 25, "total": 43}}}}, "status": 200}}}}, "400": {"description": "Invalid query parameters", "content": {"application/json": {"example": {"message": "league, season, and team parameters are required", "error": "Bad Request", "statusCode": 400}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "404": {"description": "Team statistics not found", "content": {"application/json": {"example": {"message": "Team statistics not found for the specified parameters", "error": "Not Found", "statusCode": 404}}}}}, "security": [{"bearer": []}], "summary": "Get Team Statistics", "tags": ["Football - Teams"]}}, "/football/teams/{externalId}": {"get": {"description": "\n        Retrieve detailed information for a specific team by external ID.\n\n        **Features:**\n        - Complete team profile\n        - Team statistics and information\n        - Logo and branding data\n        - Authentication required for API usage tracking\n\n        **Tier Access:**\n        - Free: 100 API calls/month\n        - Premium: 10,000 API calls/month\n        - Enterprise: Unlimited API calls\n\n        **Parameter:**\n        - externalId: Team external ID (positive integer)\n\n        **Examples:**\n        - /33 (Manchester United)\n        - /529 (Barcelona)\n        - /50 (Manchester City)\n        ", "operationId": "TeamController_getTeamById", "parameters": [{"name": "externalId", "required": true, "in": "path", "description": "Team external ID", "schema": {"example": 33, "type": "number"}}], "responses": {"200": {"description": "Team retrieved successfully", "content": {"application/json": {"example": {"data": {"id": 1, "externalId": 33, "name": "Manchester United", "code": "MUN", "country": "England", "founded": 1878, "national": false, "logo": "https://media.api-sports.io/football/teams/33.png"}, "status": 200}}}}, "400": {"description": "Invalid team ID", "content": {"application/json": {"example": {"message": "Invalid externalId: must be a positive integer", "error": "Bad Request", "statusCode": 400}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "404": {"description": "Team not found", "content": {"application/json": {"example": {"message": "Team not found", "error": "Not Found", "statusCode": 404}}}}}, "security": [{"bearer": []}], "summary": "Get Team by ID", "tags": ["Football - Teams"]}}, "/broadcast-links": {"post": {"description": "\n        Create a new broadcast link for a fixture with role-based permissions.\n\n        **🔒 AUTHENTICATION REQUIRED:**\n        SystemUser authentication required (Admin/Moderator/Editor roles accepted).\n\n        **Quick Auth Setup in Swagger UI:**\n        1. Login: POST /system-auth/login\n        2. Co<PERSON> \"accessToken\" from response\n        3. Click \"Authorize\" button (🔓) at top of page\n        4. Enter: Bearer YOUR_ACCESS_TOKEN\n        5. Click \"Authorize\" and \"Close\"\n\n        **Test Credentials (All roles can create):**\n        - Admin: {\"username\": \"admin\", \"password\": \"admin123456\"}\n        - Editor: {\"username\": \"editor1\", \"password\": \"editor123456\"}\n        - Moderator: {\"username\": \"moderator1\", \"password\": \"moderator123456\"}\n\n        **Role Permissions:**\n        - **Admin**: Can create broadcast links for any fixture\n        - **Moderator**: Can create broadcast links for any fixture\n        - **Editor**: Can create broadcast links for any fixture\n\n        **Note:** All SystemUser roles can create BroadcastLinks, but viewing/editing permissions differ:\n        - Admin/Moderator: Can view/edit/delete all BroadcastLinks\n        - Editor: Can only view/edit/delete own BroadcastLinks\n\n        **Example Request:**\n        ```\n        POST /broadcast-links HTTP/1.1\n        Authorization: Bearer YOUR_ACCESS_TOKEN\n        Content-Type: application/json\n\n        {\n          \"fixtureId\": 1274453,\n          \"linkName\": \"Live Stream HD\",\n          \"linkUrl\": \"https://youtube.com/watch?v=example123\",\n          \"linkComment\": \"Official broadcast link\"\n        }\n        ```\n\n        **Security:**\n        - addedBy automatically set to current user ID\n        - Fixture existence validation\n        - URL format validation\n        - Input sanitization\n        ", "operationId": "BroadcastLinkController_createBroadcastLink", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBroadcastLinkDto"}}}}, "responses": {"201": {"description": "Broadcast link created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastLinkResponseDto"}}}}, "403": {"description": "SystemUser access required"}, "404": {"description": "Fixture not found"}}, "security": [{"bearer": []}], "summary": "Create Broadcast Link (SystemUser Only)", "tags": ["Broadcast Links"]}}, "/broadcast-links/fixture/{fixtureId}": {"get": {"description": "\n        Get broadcast links for a specific fixture with role-based data filtering.\n\n        **Role-Based Permissions:**\n        - **Admin**: See all broadcast links for the fixture (full access)\n        - **Moderator**: See all broadcast links for the fixture (full access)\n        - **Editor**: See only broadcast links they created (own only)\n\n        **Features:**\n        - Automatic role-based data filtering\n        - Fixture existence validation\n        - Ownership-based access control\n        - Comprehensive audit logging\n\n        **Data Filtering Logic:**\n        - Admin/Moderator: Query returns all BroadcastLinks for fixture\n        - Editor: Query filtered by addedBy = currentUser.id\n\n        **Security:**\n        - No sensitive data exposure\n        - Role-based query filtering\n        - User context validation\n        ", "operationId": "BroadcastLinkController_getBroadcastLinksByFixtureId", "parameters": [{"name": "fixtureId", "required": true, "in": "path", "description": "Fixture external ID", "schema": {"example": 868847, "type": "number"}}], "responses": {"200": {"description": "Broadcast links retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BroadcastLinkResponseDto"}}}}}, "404": {"description": "Fixture not found"}}, "security": [{"bearer": []}], "summary": "Get Broadcast Links by Fixture", "tags": ["Broadcast Links"]}}, "/broadcast-links/{id}": {"patch": {"description": "\n        Update a broadcast link with role-based ownership permissions.\n\n        **Role-Based Permissions:**\n        - **Admin**: Can update any broadcast link (full access)\n        - **Moderator**: Can update any broadcast link (full access)\n        - **Editor**: Can only update broadcast links they created (own only)\n\n        **Features:**\n        - Automatic ownership validation for editors\n        - URL format validation and sanitization\n        - Comprehensive audit logging\n        - addedBy field preservation (cannot be changed)\n\n        **Security:**\n        - Ownership checks: Editor can only modify own BroadcastLinks\n        - Permission validation before any updates\n        - Input sanitization and validation\n        - Audit trail for all modifications\n\n        **Validation:**\n        - BroadcastLink existence check\n        - User permission verification\n        - URL format validation\n        - Input data sanitization\n        ", "operationId": "BroadcastLinkController_updateBroadcastLink", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Broadcast link ID", "schema": {"example": 1, "type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBroadcastLinkDto"}}}}, "responses": {"200": {"description": "Broadcast link updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BroadcastLinkResponseDto"}}}}, "403": {"description": "Insufficient permissions"}, "404": {"description": "Broadcast link not found"}}, "security": [{"bearer": []}], "summary": "Update Broadcast Link", "tags": ["Broadcast Links"]}, "delete": {"description": "\n        Delete a broadcast link with role-based ownership permissions.\n\n        **Role-Based Permissions:**\n        - **Admin**: Can delete any broadcast link (full access)\n        - **Moderator**: Can delete any broadcast link (full access)\n        - **Editor**: Can only delete broadcast links they created (own only)\n\n        **Features:**\n        - Automatic ownership validation for editors\n        - Hard delete with comprehensive audit logging\n        - Permission enforcement before deletion\n        - Complete removal from database\n\n        **Security:**\n        - Ownership checks: Editor can only delete own BroadcastLinks\n        - Permission validation before any deletion\n        - Comprehensive audit trail\n        - User context validation\n\n        **Validation:**\n        - BroadcastLink existence check\n        - User permission verification\n        - Ownership validation for editors\n        - Audit logging before deletion\n        ", "operationId": "BroadcastLinkController_deleteBroadcastLink", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Broadcast link ID", "schema": {"example": 1, "type": "number"}}], "responses": {"204": {"description": "Broadcast link deleted successfully"}, "403": {"description": "Insufficient permissions"}, "404": {"description": "Broadcast link not found"}}, "security": [{"bearer": []}], "summary": "Delete Broadcast Link", "tags": ["Broadcast Links"]}}, "/upload/file": {"post": {"description": "\n        Upload an image file to the server with automatic categorization and storage.\n\n        **🔒 AUTHENTICATION REQUIRED:**\n        SystemUser authentication required (Admin/Moderator/Editor roles accepted).\n\n        **Quick Auth Setup in Swagger UI:**\n        1. Login: POST /system-auth/login\n        2. Co<PERSON> \"accessToken\" from response\n        3. Click \"Authorize\" button (🔓) at top of page\n        4. Enter: Bearer YOUR_ACCESS_TOKEN\n        5. Click \"Authorize\" and \"Close\"\n\n        **Test Credentials:**\n        - Admin: {\"username\": \"admin\", \"password\": \"admin123456\"}\n        - Editor: {\"username\": \"editor1\", \"password\": \"editor123456\"}\n        - Moderator: {\"username\": \"moderator1\", \"password\": \"moderator123456\"}\n\n        **Features:**\n        - File validation (PNG, JPG, JPEG, GIF, SVG)\n        - Size limit: 10MB maximum\n        - Automatic filename generation\n        - Category-based organization\n        - Duplicate prevention\n        - Audit logging\n\n        **Supported File Types:**\n        - PNG (.png)\n        - JPEG/JPG (.jpg, .jpeg)\n        - GIF (.gif)\n        - SVG (.svg)\n\n        **Categories:**\n        - leagues: League logos and images\n        - teams: Team logos and images\n        - flags: Country flags\n        - venues: Stadium and venue images\n        - general: General purpose images\n\n        **Example Request:**\n        ```\n        POST /upload/file HTTP/1.1\n        Authorization: Bearer YOUR_ACCESS_TOKEN\n        Content-Type: multipart/form-data\n\n        file: [binary data]\n        category: leagues\n        description: Premier League official logo\n        ```\n        ", "operationId": "UploadController_uploadFile", "parameters": [], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "Image file to upload"}, "category": {"type": "string", "enum": ["leagues", "teams", "flags", "venues", "general"], "description": "Category for organizing the image"}, "description": {"type": "string", "description": "Optional description for the image", "maxLength": 255}}, "required": ["file", "category"]}}}}, "responses": {"201": {"description": "Image uploaded successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadImageResponseDto"}, "example": {"id": "img_a1b2c3d4e5f6g7h8", "originalName": "premier-league-logo.png", "filename": "leagues/premier-league-logo-1640995200000.png", "size": 15420, "mimeType": "image/png", "category": "leagues", "url": "http://localhost:3000/uploads/leagues/premier-league-logo-1640995200000.png", "path": "./public/images/leagues/premier-league-logo-1640995200000.png", "uploadedAt": "2025-05-25T10:30:00.000Z", "uploadedBy": 1, "description": "Premier League official logo"}}}}, "400": {"description": "Invalid file or request data", "content": {"application/json": {"example": {"message": "Invalid file type. Only PNG, JPG, JPEG, GIF, SVG are allowed.", "error": "Bad Request", "statusCode": 400}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "413": {"description": "File too large", "content": {"application/json": {"example": {"message": "File size too large. Maximum 10MB allowed.", "error": "Payload Too Large", "statusCode": 413}}}}}, "security": [{"bearer": []}], "summary": "Upload Image from File (SystemUser Only)", "tags": ["Image Upload"]}}, "/upload/url": {"post": {"description": "\n        Upload an image from a remote URL with automatic download and storage.\n\n        **🔒 AUTHENTICATION REQUIRED:**\n        SystemUser authentication required (Admin/Moderator/Editor roles accepted).\n\n        **Features:**\n        - Remote image download\n        - URL validation\n        - File type validation\n        - Size limit: 10MB maximum\n        - Custom filename support\n        - Category-based organization\n        - Audit logging\n\n        **Supported Image URLs:**\n        - Direct image links (PNG, JPG, JPEG, GIF, SVG)\n        - HTTPS and HTTP protocols\n        - Maximum file size: 10MB\n        - Timeout: 30 seconds\n\n        **Example Request:**\n        ```\n        POST /upload/url HTTP/1.1\n        Authorization: Bearer YOUR_ACCESS_TOKEN\n        Content-Type: application/json\n\n        {\n          \"imageUrl\": \"https://media.api-sports.io/football/leagues/39.png\",\n          \"category\": \"leagues\",\n          \"filename\": \"premier-league-logo\",\n          \"description\": \"Premier League official logo\"\n        }\n        ```\n        ", "operationId": "UploadController_uploadFromUrl", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadImageByUrlDto"}, "examples": {"leagueLogo": {"summary": "League Logo Example", "value": {"imageUrl": "https://media.api-sports.io/football/leagues/39.png", "category": "leagues", "filename": "premier-league-logo", "description": "Premier League official logo"}}, "teamLogo": {"summary": "Team Logo Example", "value": {"imageUrl": "https://media.api-sports.io/football/teams/33.png", "category": "teams", "filename": "manchester-united-logo", "description": "Manchester United official logo"}}, "countryFlag": {"summary": "Country Flag Example", "value": {"imageUrl": "https://media.api-sports.io/flags/gb.svg", "category": "flags", "filename": "england-flag", "description": "England national flag"}}}}}}, "responses": {"201": {"description": "Image uploaded from URL successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadImageResponseDto"}}}}, "400": {"description": "Invalid URL or image type", "content": {"application/json": {"example": {"message": "Unable to download image from the provided URL.", "error": "Bad Request", "statusCode": 400}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}}, "security": [{"bearer": []}], "summary": "Upload Image from URL (SystemUser Only)", "tags": ["Image Upload"]}}, "/upload": {"get": {"description": "\n        Retrieve uploaded images with pagination and filtering options.\n\n        **Features:**\n        - Pagination support\n        - Category filtering\n        - Search by filename or description\n        - Sorted by upload date (newest first)\n        - Complete image metadata\n\n        **Query Parameters:**\n        - page: Page number (default: 1)\n        - limit: Items per page (default: 20)\n        - category: Filter by image category\n        - search: Search in filename or description\n        ", "operationId": "UploadController_getImages", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"example": 20, "type": "number"}}, {"name": "category", "required": false, "in": "query", "description": "Filter by category", "schema": {"enum": ["leagues", "teams", "flags", "venues", "general"], "type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Search by filename or description", "schema": {"example": "premier", "type": "string"}}], "responses": {"200": {"description": "Images retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageListResponseDto"}}}}, "401": {"description": "Unauthorized - Authentication required"}}, "security": [{"bearer": []}], "summary": "Get Uploaded Images (SystemUser Only)", "tags": ["Image Upload"]}}, "/upload/{imageId}": {"get": {"description": "Retrieve detailed information about a specific uploaded image.", "operationId": "UploadController_getImageById", "parameters": [{"name": "imageId", "required": true, "in": "path", "description": "Unique image identifier", "schema": {"example": "img_a1b2c3d4e5f6g7h8", "type": "string"}}], "responses": {"200": {"description": "Image details retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UploadImageResponseDto"}}}}, "404": {"description": "Image not found"}}, "security": [{"bearer": []}], "summary": "Get Image by ID (SystemUser Only)", "tags": ["Image Upload"]}, "delete": {"description": "\n        Delete an uploaded image from both database and filesystem.\n\n        **Features:**\n        - Complete image removal\n        - File system cleanup\n        - Database record deletion\n        - Audit logging\n\n        **Note:** This action is irreversible.\n        ", "operationId": "UploadController_deleteImage", "parameters": [{"name": "imageId", "required": true, "in": "path", "description": "Unique image identifier", "schema": {"example": "img_a1b2c3d4e5f6g7h8", "type": "string"}}], "responses": {"204": {"description": "Image deleted successfully"}, "404": {"description": "Image not found"}}, "security": [{"bearer": []}], "summary": "Delete Image (SystemUser Only)", "tags": ["Image Upload"]}}, "/system-auth/login": {"post": {"description": "\n        Login endpoint for system users with 3-tier role system.\n\n        **User Roles:**\n        - **Admin**: Full system access, user management, all operations\n        - **Moderator**: Content moderation, full BroadcastLink access, user monitoring\n        - **Editor**: Content creation, own BroadcastLink management only\n\n        **Features:**\n        - Username/password authentication\n        - JWT token generation with role information\n        - Device tracking and session management\n        - Comprehensive audit logging\n        - Refresh token support\n\n        **Security:**\n        - Rate limiting applied (5 attempts per minute)\n        - Password validation and hashing\n        - Account status verification\n        - Login attempt logging and monitoring\n\n        **Examples:**\n        - Admin: username: \"admin\", password: \"admin123456\"\n        - Editor: username: \"editor1\", password: \"editor123456\"\n        - Moderator: username: \"moderator1\", password: \"moderator123456\"\n        ", "operationId": "SystemAuthController_login", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserLoginDto"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemAuthResponseDto"}}}}, "401": {"description": "Invalid credentials"}, "429": {"description": "Too many login attempts"}}, "summary": "System User Login", "tags": ["System Authentication"]}}, "/system-auth/create-user": {"post": {"description": "\n        Create a new system user account with role-based permissions.\n\n        **Access:** Admin only\n        **Features:**\n        - Create users with different roles\n        - Password hashing and validation\n        - Email uniqueness validation\n        - Username uniqueness check\n        - Comprehensive audit logging\n\n        **Available Roles:**\n        - **admin**: Full system access, user management, all operations\n        - **moderator**: Content moderation, full BroadcastLink access, user monitoring\n        - **editor**: Content creation, own BroadcastLink management only\n\n        **Role Permissions:**\n        - Admin: Can create/edit/delete any user, full system control\n        - Moderator: Can moderate content, manage all BroadcastLinks, view users\n        - Editor: Can create content, manage own BroadcastLinks only\n\n        **Examples:**\n        - Create Admin: {\"username\": \"admin2\", \"email\": \"<EMAIL>\", \"password\": \"admin123456\", \"role\": \"admin\"}\n        - Create Moderator: {\"username\": \"mod1\", \"email\": \"<EMAIL>\", \"password\": \"mod123456\", \"role\": \"moderator\"}\n        - Create Editor: {\"username\": \"editor2\", \"email\": \"<EMAIL>\", \"password\": \"editor123456\", \"role\": \"editor\"}\n        ", "operationId": "SystemAuthController_createUser", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserCreateDto"}}}}, "responses": {"201": {"description": "System user created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserProfileDto"}}}}, "403": {"description": "Admin access required"}, "409": {"description": "Username or email already exists"}}, "security": [{"bearer": []}], "summary": "Create System User (Admin Only)", "tags": ["System Authentication"]}}, "/system-auth/refresh": {"post": {"description": "\n        Refresh an expired access token using a valid refresh token.\n\n        **Features:**\n        - Refresh token validation\n        - New access token generation\n        - Token expiry checking\n        - User status verification\n        ", "operationId": "SystemAuthController_refreshToken", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "Access token refreshed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "New JWT access token"}}}}}}, "401": {"description": "Invalid or expired refresh token"}}, "summary": "Refresh Access Token", "tags": ["System Authentication"]}}, "/system-auth/logout": {"post": {"description": "\n        Logout current session by revoking the refresh token.\n\n        **Features:**\n        - Refresh token revocation\n        - Session termination\n        - Audit logging\n        ", "operationId": "SystemAuthController_logout", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "Logout successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Logout successful"}}}}}}}, "summary": "Logout", "tags": ["System Authentication"]}}, "/system-auth/logout-all": {"post": {"description": "\n        Logout from all devices by revoking all refresh tokens for the current user.\n\n        **Features:**\n        - All refresh tokens revocation\n        - Multi-device session termination\n        - Security enhancement\n        ", "operationId": "SystemAuthController_logoutFromAllDevices", "parameters": [], "responses": {"200": {"description": "Logged out from all devices successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Logged out from all devices successfully"}}}}}}}, "security": [{"bearer": []}], "summary": "<PERSON><PERSON><PERSON> from All Devices", "tags": ["System Authentication"]}}, "/system-auth/profile": {"get": {"description": "\n        Get the profile information of the currently authenticated system user.\n\n        **Features:**\n        - Current user information\n        - Role and permissions\n        - Account status\n        - Last login information\n        ", "operationId": "SystemAuthController_getProfile", "parameters": [], "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserProfileDto"}}}}, "401": {"description": "Authentication required"}}, "security": [{"bearer": []}], "summary": "Get Current User Profile", "tags": ["System Authentication"]}, "put": {"description": "\n        Update system user profile information.\n\n        **Features:**\n        - Update email and full name (all users)\n        - Update role and active status (admin only)\n        - Email uniqueness validation\n        - Permission-based field updates\n\n        **Permissions:**\n        - All users: Can update email, fullName\n        - Admin only: Can update role, isActive\n\n        **Security:**\n        - Email uniqueness check\n        - Role-based access control\n        - Audit logging\n        ", "operationId": "SystemAuthController_updateProfile", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserUpdateDto"}}}}, "responses": {"200": {"description": "User profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserProfileDto"}}}}, "403": {"description": "Forbidden - Insufficient permissions"}, "409": {"description": "Email already exists"}}, "security": [{"bearer": []}], "summary": "Update User Profile", "tags": ["System Authentication"]}}, "/system-auth/users/{id}": {"put": {"description": "\n        Update any system user profile by ID (admin only).\n\n        **Features:**\n        - Update any user's information\n        - Full admin control over all fields\n        - Email uniqueness validation\n        - Audit logging\n\n        **Admin Permissions:**\n        - Update email, fullName, role, isActive\n        - Manage any user account\n        - Activate/deactivate users\n        ", "operationId": "SystemAuthController_updateUser", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserUpdateDto"}}}}, "responses": {"200": {"description": "User updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserProfileDto"}}}}, "403": {"description": "Admin access required"}, "404": {"description": "User not found"}, "409": {"description": "Email already exists"}}, "security": [{"bearer": []}], "summary": "Update User by ID (Admin Only)", "tags": ["System Authentication"]}}, "/system-auth/change-password": {"post": {"description": "\n        Change current user's password.\n\n        **Features:**\n        - Current password verification\n        - New password confirmation\n        - Password strength validation\n        - Automatic logout from all devices\n\n        **Security:**\n        - Current password required\n        - Password confirmation match\n        - All sessions invalidated after change\n        - Audit logging\n        ", "operationId": "SystemAuthController_changePassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemUserChangePasswordDto"}}}}, "responses": {"200": {"description": "Password changed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password changed successfully. Please login again."}}}}}}, "400": {"description": "Invalid password or confirmation mismatch"}}, "security": [{"bearer": []}], "summary": "Change Password", "tags": ["System Authentication"]}}, "/users/register": {"post": {"description": "Register a new user account. Email verification required. Rate limited to 3 attempts per 5 minutes per IP.", "operationId": "RegisteredUserController_register", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserRegisterDto"}}}}, "responses": {"201": {"description": "User registered successfully. Email verification required.", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Registration successful. Please check your email for verification."}, "user": {"type": "object", "properties": {"id": {"type": "number", "example": 1}, "username": {"type": "string", "example": "john_doe"}, "email": {"type": "string", "example": "<EMAIL>"}, "isEmailVerified": {"type": "boolean", "example": false}}}}}}}}, "400": {"description": "Invalid input data"}, "409": {"description": "Username or email already exists"}, "429": {"description": "Too many registration attempts"}}, "summary": "User Registration", "tags": ["Registered Users"]}}, "/users/login": {"post": {"description": "Authenticate user and receive JWT tokens. Email verification required. Rate limited to 5 attempts per minute per IP.", "operationId": "RegisteredUserController_login", "parameters": [{"name": "user-agent", "required": true, "in": "header", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserLoginDto"}}}}, "responses": {"200": {"description": "Login successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserAuthResponseDto"}}}}, "401": {"description": "Invalid credentials or email not verified"}, "429": {"description": "Too many login attempts"}}, "summary": "User Login", "tags": ["Registered Users"]}}, "/users/verify-email": {"post": {"description": "Verify user email with verification token", "operationId": "RegisteredUserController_verifyEmail", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailVerificationDto"}}}}, "responses": {"200": {"description": "Email verified successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Email verified successfully"}, "user": {"$ref": "#/components/schemas/RegisteredUserProfileDto"}}}}}}, "400": {"description": "Invalid or expired verification token"}}, "summary": "<PERSON><PERSON><PERSON>", "tags": ["Registered Users"]}}, "/users/resend-verification": {"post": {"description": "Resend email verification token to user email", "operationId": "RegisteredUserController_resendVerification", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResendEmailVerificationDto"}}}}, "responses": {"200": {"description": "Verification email sent", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Verification email sent successfully"}}}}}}, "400": {"description": "Email already verified"}, "404": {"description": "User not found"}}, "summary": "Resend Email Verification", "tags": ["Registered Users"]}}, "/users/forgot-password": {"post": {"description": "Request password reset token via email", "operationId": "RegisteredUserController_forgotPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetRequestDto"}}}}, "responses": {"200": {"description": "Password reset email sent (if email exists)", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "If the email exists, a password reset link has been sent"}}}}}}}, "summary": "Request Password Reset", "tags": ["Registered Users"]}}, "/users/reset-password": {"post": {"description": "Reset password using reset token", "operationId": "RegisteredUserController_resetPassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PasswordResetDto"}}}}, "responses": {"200": {"description": "Password reset successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password reset successfully"}}}}}}, "400": {"description": "Invalid or expired reset token"}}, "summary": "Reset Password", "tags": ["Registered Users"]}}, "/users/profile": {"get": {"description": "Get current user profile information. Requires authentication.", "operationId": "RegisteredUserController_getProfile", "parameters": [], "responses": {"200": {"description": "User profile retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserProfileDto"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Get User Profile", "tags": ["Registered Users"]}, "put": {"description": "Update user profile information. Requires authentication.", "operationId": "RegisteredUserController_updateProfile", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateProfileDto"}}}}, "responses": {"200": {"description": "Profile updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisteredUserProfileDto"}}}}, "400": {"description": "Invalid input data"}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Update User Profile", "tags": ["Registered Users"]}}, "/users/change-password": {"post": {"description": "Change user password. Requires current password verification.", "operationId": "RegisteredUserController_changePassword", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordDto"}}}}, "responses": {"200": {"description": "Password changed successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Password changed successfully"}}}}}}, "400": {"description": "Invalid current password"}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Change Password", "tags": ["Registered Users"]}}, "/users/api-usage": {"get": {"description": "Get current month API usage statistics for the user", "operationId": "RegisteredUserController_getApiUsage", "parameters": [], "responses": {"200": {"description": "API usage statistics retrieved successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"tier": {"type": "string", "example": "premium", "enum": ["free", "premium", "enterprise"]}, "apiCallsUsed": {"type": "number", "example": 150}, "apiCallsLimit": {"type": "number", "example": 10000, "nullable": true}, "apiCallsRemaining": {"type": "number", "example": 9850, "nullable": true}, "lastApiCallAt": {"type": "string", "format": "date-time", "nullable": true}, "resetDate": {"type": "string", "format": "date-time", "example": "2024-02-01T00:00:00.000Z"}}}}}}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Get API Usage Statistics", "tags": ["Registered Users"]}}, "/admin/tiers/statistics": {"get": {"description": "\n        Get statistics about user distribution across tiers.\n\n        **Access:** Admin only\n        **Features:**\n        - User count by tier (free, premium, enterprise)\n        - Total registered users\n        - Real-time statistics\n        - Administrative insights\n\n        **Use Cases:**\n        - Admin dashboard metrics\n        - Business analytics\n        - User tier monitoring\n        - Revenue tracking\n        ", "operationId": "AdminController_getTierStatistics", "parameters": [], "responses": {"200": {"description": "Tier statistics retrieved successfully", "content": {"application/json": {"example": {"free": 150, "premium": 45, "enterprise": 12, "total": 207}}}}, "401": {"description": "Unauthorized - Authentication required", "content": {"application/json": {"example": {"message": "System authentication required", "error": "Unauthorized", "statusCode": 401}}}}, "403": {"description": "Forbidden - Admin access required", "content": {"application/json": {"example": {"message": "Forbidden resource", "error": "Forbidden", "statusCode": 403}}}}}, "security": [{"bearer": []}], "summary": "Get Tier Statistics (Admin Only)", "tags": ["Admin - User Management"]}}, "/admin/users/approaching-limits": {"get": {"description": "Get list of users who are approaching their API usage limits", "operationId": "AdminController_getUsersApproachingLimits", "parameters": [{"name": "threshold", "required": false, "in": "query", "description": "Usage percentage threshold (default: 80)", "schema": {"example": 80, "type": "number"}}], "responses": {"200": {"description": "Users approaching limits retrieved successfully"}}, "security": [{"bearer": []}], "summary": "Get Users Approaching API Limits", "tags": ["Admin - User Management"]}}, "/admin/users/{userId}/upgrade-tier": {"post": {"description": "Upgrade a user to a higher tier with optional subscription duration", "operationId": "AdminController_upgradeTier", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "User ID to upgrade", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"newTier": {"type": "string", "enum": ["premium", "enterprise"], "description": "New tier to upgrade to"}, "subscriptionMonths": {"type": "number", "description": "Subscription duration in months (for premium/enterprise)", "example": 12}}, "required": ["newTier"]}}}}, "responses": {"200": {"description": "User tier upgraded successfully"}, "400": {"description": "Invalid tier upgrade"}, "404": {"description": "User not found"}}, "security": [{"bearer": []}], "summary": "Upgrade User Tier", "tags": ["Admin - User Management"]}}, "/admin/users/{userId}/downgrade-tier": {"post": {"description": "Downgrade a user to a lower tier", "operationId": "AdminController_downgradeTier", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "User ID to downgrade", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"newTier": {"type": "string", "enum": ["free", "premium"], "description": "New tier to downgrade to"}}, "required": ["newTier"]}}}}, "responses": {"200": {"description": "User tier downgraded successfully"}}, "security": [{"bearer": []}], "summary": "Downgrade User Tier", "tags": ["Admin - User Management"]}}, "/admin/users/{userId}/extend-subscription": {"post": {"description": "Extend a user subscription by additional months", "operationId": "AdminController_extendSubscription", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "User ID to extend subscription", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"additionalMonths": {"type": "number", "description": "Additional months to extend", "example": 6}}, "required": ["additionalMonths"]}}}}, "responses": {"200": {"description": "Subscription extended successfully"}}, "security": [{"bearer": []}], "summary": "Extend User Subscription", "tags": ["Admin - User Management"]}}, "/admin/reset-api-usage": {"post": {"description": "Reset API usage counters for all users (typically run monthly)", "operationId": "AdminController_resetMonthlyApiUsage", "parameters": [], "responses": {"200": {"description": "API usage reset successfully"}}, "security": [{"bearer": []}], "summary": "Reset Monthly API Usage", "tags": ["Admin - User Management"]}}, "/admin/check-usage-warnings": {"post": {"description": "Check and send API usage warnings to users approaching limits", "operationId": "AdminController_checkApiUsageWarnings", "parameters": [], "responses": {"200": {"description": "API usage warnings checked and sent"}}, "security": [{"bearer": []}], "summary": "Check API Usage Warnings", "tags": ["Admin - User Management"]}}, "/admin/users/{userId}/subscription": {"get": {"description": "Get detailed subscription information for a user", "operationId": "AdminController_getSubscriptionInfo", "parameters": [{"name": "userId", "required": true, "in": "path", "description": "User ID to get subscription info", "schema": {"type": "number"}}], "responses": {"200": {"description": "Subscription info retrieved successfully"}}, "security": [{"bearer": []}], "summary": "Get User Subscription Info", "tags": ["Admin - User Management"]}}, "/admin/users": {"get": {"description": "Get paginated list of all registered users with filtering", "operationId": "AdminController_getAllUsers", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Page number", "schema": {"example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Items per page", "schema": {"example": 20, "type": "number"}}, {"name": "tier", "required": false, "in": "query", "description": "Filter by tier", "schema": {"enum": ["free", "premium", "enterprise"], "type": "string"}}, {"name": "isActive", "required": false, "in": "query", "description": "Filter by active status", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Users retrieved successfully"}}, "security": [{"bearer": []}], "summary": "Get All Registered Users", "tags": ["Admin - User Management"]}}}, "info": {"title": "APISportsGame API", "description": "Enterprise Sports API với complete authentication system và optimized sync performance. Features: Enhanced JWT authentication, 3-tier role system (Admin/Moderator/Editor), rate limiting, audit logging, smart live sync với 96% API call reduction.", "version": "2.0.0", "contact": {"name": "APISportsGame Team", "url": "https://apisportsgame.com", "email": "<EMAIL>"}}, "tags": [{"name": "System Authentication", "description": "System Authentication - 3-tier role system: <PERSON><PERSON> (full access), Moderator (content moderation), Editor (content creation)"}, {"name": "Football", "description": "Football data endpoints - Fixtures, teams, leagues (some public, some role-based)"}, {"name": "Admin Management", "description": "Admin management - User administration, system controls (Admin only)"}, {"name": "Public", "description": "Public endpoints - No authentication required"}, {"name": "Data Synchronization", "description": "Data synchronization - Manual sync triggers (Admin), status monitoring (Editor+)"}, {"name": "Broadcast Links", "description": "Broadcast Links - Role-based permissions: Admin/Moderator (full access), Editor (own only)"}], "servers": [{"url": "http://*************", "description": "Production Server"}, {"url": "http://localhost:3000", "description": "Local Development"}], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http", "name": "Authorization", "description": "Enter JWT token", "in": "header"}}, "schemas": {"CreateFixtureDto": {"type": "object", "properties": {}}, "UpdateFixtureDto": {"type": "object", "properties": {}}, "CreateLeagueDto": {"type": "object", "properties": {}}, "LeagueResponseDto": {"type": "object", "properties": {}}, "UpdateLeagueDto": {"type": "object", "properties": {}}, "CreateBroadcastLinkDto": {"type": "object", "properties": {"fixtureId": {"type": "number", "description": "Fixture external ID", "example": 868847}, "linkName": {"type": "string", "description": "Name of the broadcast link", "example": "YouTube Live Stream"}, "linkUrl": {"type": "string", "description": "URL of the broadcast link", "example": "https://youtube.com/watch?v=abc123"}, "linkComment": {"type": "string", "description": "Comment or description for the link", "example": "Official HD stream with English commentary"}}, "required": ["fixtureId", "linkName", "linkUrl", "linkComment"]}, "BroadcastLinkResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "Broadcast link ID", "example": 1}, "fixtureId": {"type": "number", "description": "Fixture external ID", "example": 868847}, "linkName": {"type": "string", "description": "Name of the broadcast link", "example": "YouTube Live Stream"}, "linkComment": {"type": "string", "description": "Comment or description for the link", "example": "Official HD stream with English commentary"}, "linkUrl": {"type": "string", "description": "URL of the broadcast link", "example": "https://youtube.com/watch?v=abc123"}, "addedBy": {"type": "number", "description": "ID of user who added this link", "example": 1}, "createdAt": {"type": "string", "description": "Creation timestamp", "example": "2024-05-24T10:30:00.000Z"}, "updatedAt": {"type": "string", "description": "Last update timestamp", "example": "2024-05-24T10:30:00.000Z"}}, "required": ["id", "fixtureId", "linkName", "linkComment", "linkUrl", "added<PERSON>y", "createdAt", "updatedAt"]}, "UpdateBroadcastLinkDto": {"type": "object", "properties": {"linkName": {"type": "string", "description": "Name of the broadcast link", "example": "Updated YouTube Live Stream"}, "linkUrl": {"type": "string", "description": "URL of the broadcast link", "example": "https://youtube.com/watch?v=updated123"}, "linkComment": {"type": "string", "description": "Comment or description for the link", "example": "Updated HD stream with multiple languages"}}}, "UploadImageResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the uploaded image", "example": "img_1234567890"}, "originalName": {"type": "string", "description": "Original filename", "example": "premier-league-logo.png"}, "filename": {"type": "string", "description": "Stored filename", "example": "leagues/premier-league-logo-1234567890.png"}, "size": {"type": "number", "description": "File size in bytes", "example": 15420}, "mimeType": {"type": "string", "description": "MIME type", "example": "image/png"}, "category": {"type": "string", "description": "Image category", "enum": ["leagues", "teams", "flags", "venues", "general"], "example": "leagues"}, "url": {"type": "string", "description": "Public URL to access the image", "example": "http://localhost:3000/uploads/leagues/premier-league-logo-1234567890.png"}, "path": {"type": "string", "description": "Local file path", "example": "./public/images/leagues/premier-league-logo-1234567890.png"}, "uploadedAt": {"format": "date-time", "type": "string", "description": "Upload timestamp", "example": "2025-05-25T10:30:00.000Z"}, "uploadedBy": {"type": "number", "description": "User who uploaded the image", "example": 1}, "description": {"type": "string", "description": "Image description", "example": "Premier League official logo"}}, "required": ["id", "originalName", "filename", "size", "mimeType", "category", "url", "path", "uploadedAt", "uploadedBy"]}, "UploadImageByUrlDto": {"type": "object", "properties": {"imageUrl": {"type": "string", "description": "URL of the image to upload", "example": "https://media.api-sports.io/football/leagues/39.png"}, "category": {"type": "string", "description": "Category for organizing images", "enum": ["leagues", "teams", "flags", "venues", "general"], "example": "leagues"}, "filename": {"type": "string", "description": "Custom filename (optional, will auto-generate if not provided)", "example": "premier-league-logo"}, "description": {"type": "string", "description": "Description or alt text for the image", "example": "Premier League official logo"}}, "required": ["imageUrl", "category"]}, "ImageListResponseDto": {"type": "object", "properties": {"data": {"description": "Array of uploaded images", "type": "array", "items": {"$ref": "#/components/schemas/UploadImageResponseDto"}}, "meta": {"type": "object", "description": "Pagination metadata", "example": {"totalItems": 150, "totalPages": 8, "currentPage": 1, "limit": 20}}}, "required": ["data", "meta"]}, "SystemUserLoginDto": {"type": "object", "properties": {"username": {"type": "string", "description": "Username for system user", "example": "admin"}, "password": {"type": "string", "description": "Password for system user", "example": "admin123456"}}, "required": ["username", "password"]}, "SystemAuthResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "JWT access token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "refreshToken": {"type": "string", "description": "JWT refresh token", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}, "user": {"type": "object", "description": "System user information"}}, "required": ["accessToken", "refreshToken", "user"]}, "SystemUserCreateDto": {"type": "object", "properties": {"username": {"type": "string", "description": "Username for new system user", "example": "editor1"}, "email": {"type": "string", "description": "Email for new system user", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password for new system user", "example": "SecurePassword123!"}, "role": {"type": "string", "description": "Role for new system user", "enum": ["admin", "editor", "moderator"], "example": "editor"}, "fullName": {"type": "string", "description": "Full name of the system user", "example": "John <PERSON>"}}, "required": ["username", "email", "password", "role"]}, "SystemUserProfileDto": {"type": "object", "properties": {"id": {"type": "number", "description": "User ID", "example": 1}, "username": {"type": "string", "description": "Username", "example": "admin"}, "email": {"type": "string", "description": "Email address", "example": "<EMAIL>"}, "fullName": {"type": "string", "description": "Full name", "example": "System Administrator"}, "role": {"type": "string", "description": "User role", "enum": ["admin", "editor", "moderator"], "example": "admin"}, "isActive": {"type": "boolean", "description": "Account status", "example": true}, "lastLoginAt": {"format": "date-time", "type": "string", "description": "Last login timestamp", "example": "2024-01-15T10:30:00Z"}, "createdAt": {"format": "date-time", "type": "string", "description": "Account creation timestamp", "example": "2024-01-01T00:00:00Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update timestamp", "example": "2024-01-15T10:30:00Z"}}, "required": ["id", "username", "email", "role", "isActive", "createdAt", "updatedAt"]}, "RefreshTokenDto": {"type": "object", "properties": {"refreshToken": {"type": "string", "description": "Refresh token string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}, "required": ["refreshToken"]}, "SystemUserUpdateDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email for system user", "example": "<EMAIL>"}, "fullName": {"type": "string", "description": "Full name of the system user", "example": "<PERSON> Updated Editor"}, "role": {"type": "string", "description": "Role for system user (admin only)", "enum": ["admin", "editor", "moderator"], "example": "editor"}, "isActive": {"type": "boolean", "description": "Active status (admin only)", "example": true}}}, "SystemUserChangePasswordDto": {"type": "object", "properties": {"currentPassword": {"type": "string", "description": "Current password", "example": "CurrentPassword123!"}, "newPassword": {"type": "string", "description": "New password", "example": "NewSecurePassword123!"}, "confirmPassword": {"type": "string", "description": "Confirm new password", "example": "NewSecurePassword123!"}}, "required": ["currentPassword", "newPassword", "confirmPassword"]}, "RegisteredUserRegisterDto": {"type": "object", "properties": {"username": {"type": "string", "description": "Username for the account", "example": "john_doe", "minLength": 3, "maxLength": 50}, "email": {"type": "string", "description": "Email address", "example": "<EMAIL>"}, "password": {"type": "string", "description": "Password for the account", "example": "SecurePassword123!", "minLength": 8, "maxLength": 128}, "fullName": {"type": "string", "description": "Full name of the user", "example": "<PERSON>", "maxLength": 100}, "firstName": {"type": "string", "description": "First name", "example": "<PERSON>", "maxLength": 50}, "lastName": {"type": "string", "description": "Last name", "example": "<PERSON><PERSON>", "maxLength": 50}, "displayName": {"type": "string", "description": "Display name", "example": "John<PERSON>", "maxLength": 50}}, "required": ["username", "email", "password"]}, "RegisteredUserLoginDto": {"type": "object", "properties": {"usernameOrEmail": {"type": "string", "description": "Username or email", "example": "john_doe"}, "password": {"type": "string", "description": "Password", "example": "SecurePassword123!"}}, "required": ["usernameOrEmail", "password"]}, "RegisteredUserProfileDto": {"type": "object", "properties": {"id": {"type": "number", "description": "User ID", "example": 1}, "username": {"type": "string", "description": "Username", "example": "john_doe"}, "email": {"type": "string", "description": "Email address", "example": "<EMAIL>"}, "fullName": {"type": "string", "description": "Full name", "example": "<PERSON>"}, "firstName": {"type": "string", "description": "First name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name", "example": "<PERSON><PERSON>"}, "displayName": {"type": "string", "description": "Display name", "example": "John<PERSON>"}, "tier": {"type": "string", "description": "User tier", "example": "free", "enum": ["free", "premium", "enterprise"]}, "isActive": {"type": "boolean", "description": "Account active status", "example": true}, "isEmailVerified": {"type": "boolean", "description": "Email verification status", "example": true}, "apiCallsUsed": {"type": "number", "description": "API calls used this month", "example": 150}, "apiCallsLimit": {"type": "object", "description": "API calls limit per month", "example": 1000, "nullable": true}, "apiCallsRemaining": {"type": "object", "description": "API calls remaining this month", "example": 850, "nullable": true}, "hasActiveSubscription": {"type": "boolean", "description": "Subscription active status", "example": true}, "subscriptionEndDate": {"format": "date-time", "type": "string", "description": "Subscription end date", "example": "2024-12-31T23:59:59.000Z"}, "lastLoginAt": {"format": "date-time", "type": "string", "description": "Last login timestamp"}, "createdAt": {"format": "date-time", "type": "string", "description": "Account creation timestamp"}}, "required": ["id", "username", "email", "fullName", "tier", "isActive", "isEmailVerified", "apiCallsUsed", "apiCallsLimit", "apiCallsRemaining", "hasActiveSubscription", "createdAt"]}, "RegisteredUserAuthResponseDto": {"type": "object", "properties": {"accessToken": {"type": "string", "description": "Access token"}, "refreshToken": {"type": "string", "description": "Refresh token"}, "user": {"description": "User profile information", "allOf": [{"$ref": "#/components/schemas/RegisteredUserProfileDto"}]}}, "required": ["accessToken", "refreshToken", "user"]}, "EmailVerificationDto": {"type": "object", "properties": {"token": {"type": "string", "description": "Email verification token", "example": "abc123def456"}}, "required": ["token"]}, "ResendEmailVerificationDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email address to resend verification", "example": "<EMAIL>"}}, "required": ["email"]}, "PasswordResetRequestDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email address for password reset", "example": "<EMAIL>"}}, "required": ["email"]}, "PasswordResetDto": {"type": "object", "properties": {"token": {"type": "string", "description": "Password reset token", "example": "reset123token456"}, "newPassword": {"type": "string", "description": "New password", "example": "NewSecurePassword123!", "minLength": 8, "maxLength": 128}}, "required": ["token", "newPassword"]}, "UpdateProfileDto": {"type": "object", "properties": {"fullName": {"type": "string", "description": "Full name", "example": "<PERSON>"}, "firstName": {"type": "string", "description": "First name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name", "example": "<PERSON><PERSON>"}, "displayName": {"type": "string", "description": "Display name", "example": "JohnD_Updated"}}}, "ChangePasswordDto": {"type": "object", "properties": {"currentPassword": {"type": "string", "description": "Current password", "example": "CurrentPassword123!"}, "newPassword": {"type": "string", "description": "New password", "example": "NewPassword123!", "minLength": 8, "maxLength": 128}}, "required": ["currentPassword", "newPassword"]}}}}