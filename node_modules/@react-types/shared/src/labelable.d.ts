/*
 * Copyright 2020 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */

import {ReactNode} from 'react';

export type LabelPosition = 'top' | 'side';
export type Alignment = 'start' | 'end';
export type NecessityIndicator = 'icon' | 'label';

export interface LabelableProps {
  /** The content to display as the label. */
  label?: ReactNode
}

export interface SpectrumLabelableProps extends LabelableProps {
  /**
   * The label's overall position relative to the element it is labeling.
   * @default 'top'
   */
  labelPosition?: LabelPosition,
  /**
   * The label's horizontal alignment relative to the element it is labeling.
   * @default 'start'
   */
  labelAlign?: Alignment,
  /**
   * Whether the required state should be shown as an icon or text.
   * @default 'icon'
   */
  necessityIndicator?: NecessityIndicator,
  /**
   * Whether the label is labeling a required field or group.
   */
  isRequired?: boolean,
  /**
   * A ContextualHelp element to place next to the label.
   */
  contextualHelp?: ReactNode
}
