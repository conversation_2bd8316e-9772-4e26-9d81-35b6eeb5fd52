import{useEffect as l}from"react";import{disposables as u}from'../utils/disposables.js';import*as c from'../utils/dom.js';import{useLatestValue as d}from'./use-latest-value.js';function p(s,n,o){let i=d(t=>{let e=t.getBoundingClientRect();e.x===0&&e.y===0&&e.width===0&&e.height===0&&o()});l(()=>{if(!s)return;let t=n===null?null:c.isHTMLElement(n)?n:n.current;if(!t)return;let e=u();if(typeof ResizeObserver!="undefined"){let r=new ResizeObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}if(typeof IntersectionObserver!="undefined"){let r=new IntersectionObserver(()=>i.current(t));r.observe(t),e.add(()=>r.disconnect())}return()=>e.dispose()},[n,i,s])}export{p as useOnDisappear};
