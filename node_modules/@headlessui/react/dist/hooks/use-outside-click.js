import{useCallback as T,useRef as E}from"react";import*as d from'../utils/dom.js';import{FocusableMode as g,isFocusableElement as y}from'../utils/focus-management.js';import{isMobile as p}from'../utils/platform.js';import{useDocumentEvent as a}from'./use-document-event.js';import{useLatestValue as L}from'./use-latest-value.js';import{useWindowEvent as x}from'./use-window-event.js';const C=30;function k(o,f,h){let m=L(h),s=T(function(e,c){if(e.defaultPrevented)return;let r=c(e);if(r===null||!r.getRootNode().contains(r)||!r.isConnected)return;let M=function u(n){return typeof n=="function"?u(n()):Array.isArray(n)||n instanceof Set?n:[n]}(f);for(let u of M)if(u!==null&&(u.contains(r)||e.composed&&e.composedPath().includes(u)))return;return!y(r,g.Loose)&&r.tabIndex!==-1&&e.preventDefault(),m.current(e,r)},[m,f]),i=E(null);a(o,"pointerdown",t=>{var e,c;p()||(i.current=((c=(e=t.composedPath)==null?void 0:e.call(t))==null?void 0:c[0])||t.target)},!0),a(o,"pointerup",t=>{if(p()||!i.current)return;let e=i.current;return i.current=null,s(t,()=>e)},!0);let l=E({x:0,y:0});a(o,"touchstart",t=>{l.current.x=t.touches[0].clientX,l.current.y=t.touches[0].clientY},!0),a(o,"touchend",t=>{let e={x:t.changedTouches[0].clientX,y:t.changedTouches[0].clientY};if(!(Math.abs(e.x-l.current.x)>=C||Math.abs(e.y-l.current.y)>=C))return s(t,()=>d.isHTMLorSVGElement(t.target)?t.target:null)},!0),x(o,"blur",t=>s(t,()=>d.isHTMLIframeElement(window.document.activeElement)?window.document.activeElement:null),!0)}export{k as useOutsideClick};
