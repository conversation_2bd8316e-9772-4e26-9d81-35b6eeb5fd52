"use client";import l,{Fragment as $,createContext as se,createRef as pe,use<PERSON><PERSON>back as de,useContext as ue,useEffect as fe,useMemo as A,useReducer as Te,useRef as j}from"react";import{useEscape as ge}from'../../hooks/use-escape.js';import{useEvent as _}from'../../hooks/use-event.js';import{useId as k}from'../../hooks/use-id.js';import{useInertOthers as ce}from'../../hooks/use-inert-others.js';import{useIsTouchDevice as me}from'../../hooks/use-is-touch-device.js';import{useIsoMorphicEffect as De}from'../../hooks/use-iso-morphic-effect.js';import{useOnDisappear as Pe}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ye}from'../../hooks/use-outside-click.js';import{useOwnerDocument as Ee}from'../../hooks/use-owner.js';import{MainTreeProvider as Y,useMainTreeNode as Ae,useRootContainers as _e}from'../../hooks/use-root-containers.js';import{useScrollLock as Ce}from'../../hooks/use-scroll-lock.js';import{useServerHandoffComplete as Re}from'../../hooks/use-server-handoff-complete.js';import{useSyncRefs as G}from'../../hooks/use-sync-refs.js';import{CloseProvider as Fe}from'../../internal/close-provider.js';import{ResetOpenClosedProvider as be,State as x,useOpenClosed as J}from'../../internal/open-closed.js';import{ForcePortalRoot as K}from'../../internal/portal-force-root.js';import{stackMachines as ve}from'../../machines/stack-machine.js';import{useSlice as Le}from'../../react-glue.js';import{match as xe}from'../../utils/match.js';import{RenderFeatures as X,forwardRefWithAs as C,useRender as h}from'../../utils/render.js';import{Description as V,useDescriptions as he}from'../description/description.js';import{FocusTrap as Oe,FocusTrapFeatures as R}from'../focus-trap/focus-trap.js';import{Portal as Se,PortalGroup as Ie,useNestedPortals as Me}from'../portal/portal.js';import{Transition as ke,TransitionChild as q}from'../transition/transition.js';var Ge=(o=>(o[o.Open=0]="Open",o[o.Closed=1]="Closed",o))(Ge||{}),we=(t=>(t[t.SetTitleId=0]="SetTitleId",t))(we||{});let Be={[0](e,t){return e.titleId===t.id?e:{...e,titleId:t.id}}},w=se(null);w.displayName="DialogContext";function O(e){let t=ue(w);if(t===null){let o=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,O),o}return t}function Ue(e,t){return xe(t.type,Be,e,t)}let z=C(function(t,o){let a=k(),{id:n=`headlessui-dialog-${a}`,open:i,onClose:s,initialFocus:d,role:p="dialog",autoFocus:T=!0,__demoMode:u=!1,unmount:y=!1,...S}=t,F=j(!1);p=function(){return p==="dialog"||p==="alertdialog"?p:(F.current||(F.current=!0,console.warn(`Invalid role [${p}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog")}();let c=J();i===void 0&&c!==null&&(i=(c&x.Open)===x.Open);let f=j(null),I=G(f,o),b=Ee(f),g=i?0:1,[v,Q]=Te(Ue,{titleId:null,descriptionId:null,panelRef:pe()}),m=_(()=>s(!1)),B=_(r=>Q({type:0,id:r})),D=Re()?g===0:!1,[Z,ee]=Me(),te={get current(){var r;return(r=v.panelRef.current)!=null?r:f.current}},L=Ae(),{resolveContainers:M}=_e({mainTreeNode:L,portals:Z,defaultContainers:[te]}),U=c!==null?(c&x.Closing)===x.Closing:!1;ce(u||U?!1:D,{allowed:_(()=>{var r,W;return[(W=(r=f.current)==null?void 0:r.closest("[data-headlessui-portal]"))!=null?W:null]}),disallowed:_(()=>{var r;return[(r=L==null?void 0:L.closest("body > *:not(#headlessui-portal-root)"))!=null?r:null]})});let P=ve.get(null);De(()=>{if(D)return P.actions.push(n),()=>P.actions.pop(n)},[P,n,D]);let H=Le(P,de(r=>P.selectors.isTop(r,n),[P,n]));ye(H,M,r=>{r.preventDefault(),m()}),ge(H,b==null?void 0:b.defaultView,r=>{r.preventDefault(),r.stopPropagation(),document.activeElement&&"blur"in document.activeElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur(),m()}),Ce(u||U?!1:D,b,M),Pe(D,f,m);let[oe,ne]=he(),re=A(()=>[{dialogState:g,close:m,setTitleId:B,unmount:y},v],[g,v,m,B,y]),N=A(()=>({open:g===0}),[g]),le={ref:I,id:n,role:p,tabIndex:-1,"aria-modal":u?void 0:g===0?!0:void 0,"aria-labelledby":v.titleId,"aria-describedby":oe,unmount:y},ae=!me(),E=R.None;D&&!u&&(E|=R.RestoreFocus,E|=R.TabLock,T&&(E|=R.AutoFocus),ae&&(E|=R.InitialFocus));let ie=h();return l.createElement(be,null,l.createElement(K,{force:!0},l.createElement(Se,null,l.createElement(w.Provider,{value:re},l.createElement(Ie,{target:f},l.createElement(K,{force:!1},l.createElement(ne,{slot:N},l.createElement(ee,null,l.createElement(Oe,{initialFocus:d,initialFocusFallback:f,containers:M,features:E},l.createElement(Fe,{value:m},ie({ourProps:le,theirProps:S,slot:N,defaultTag:He,features:Ne,visible:g===0,name:"Dialog"})))))))))))}),He="div",Ne=X.RenderStrategy|X.Static;function We(e,t){let{transition:o=!1,open:a,...n}=e,i=J(),s=e.hasOwnProperty("open")||i!==null,d=e.hasOwnProperty("onClose");if(!s&&!d)throw new Error("You have to provide an `open` and an `onClose` prop to the `Dialog` component.");if(!s)throw new Error("You provided an `onClose` prop to the `Dialog`, but forgot an `open` prop.");if(!d)throw new Error("You provided an `open` prop to the `Dialog`, but forgot an `onClose` prop.");if(!i&&typeof e.open!="boolean")throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${e.open}`);if(typeof e.onClose!="function")throw new Error(`You provided an \`onClose\` prop to the \`Dialog\`, but the value is not a function. Received: ${e.onClose}`);return(a!==void 0||o)&&!n.static?l.createElement(Y,null,l.createElement(ke,{show:a,transition:o,unmount:n.unmount},l.createElement(z,{ref:t,...n}))):l.createElement(Y,null,l.createElement(z,{ref:t,open:a,...n}))}let $e="div";function je(e,t){let o=k(),{id:a=`headlessui-dialog-panel-${o}`,transition:n=!1,...i}=e,[{dialogState:s,unmount:d},p]=O("Dialog.Panel"),T=G(t,p.panelRef),u=A(()=>({open:s===0}),[s]),y=_(I=>{I.stopPropagation()}),S={ref:T,id:a,onClick:y},F=n?q:$,c=n?{unmount:d}:{},f=h();return l.createElement(F,{...c},f({ourProps:S,theirProps:i,slot:u,defaultTag:$e,name:"Dialog.Panel"}))}let Ye="div";function Je(e,t){let{transition:o=!1,...a}=e,[{dialogState:n,unmount:i}]=O("Dialog.Backdrop"),s=A(()=>({open:n===0}),[n]),d={ref:t,"aria-hidden":!0},p=o?q:$,T=o?{unmount:i}:{},u=h();return l.createElement(p,{...T},u({ourProps:d,theirProps:a,slot:s,defaultTag:Ye,name:"Dialog.Backdrop"}))}let Ke="h2";function Xe(e,t){let o=k(),{id:a=`headlessui-dialog-title-${o}`,...n}=e,[{dialogState:i,setTitleId:s}]=O("Dialog.Title"),d=G(t);fe(()=>(s(a),()=>s(null)),[a,s]);let p=A(()=>({open:i===0}),[i]),T={ref:d,id:a};return h()({ourProps:T,theirProps:n,slot:p,defaultTag:Ke,name:"Dialog.Title"})}let Ve=C(We),qe=C(je),bt=C(Je),ze=C(Xe),vt=V,Lt=Object.assign(Ve,{Panel:qe,Title:ze,Description:V});export{Lt as Dialog,bt as DialogBackdrop,vt as DialogDescription,qe as DialogPanel,ze as DialogTitle};
