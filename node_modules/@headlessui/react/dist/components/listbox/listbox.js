"use client";import{useFocusRing as he}from"@react-aria/focus";import{useHover as De}from"@react-aria/interactions";import F,{Fragment as ce,createContext as fe,useCallback as j,useContext as Te,useEffect as Ae,useMemo as V,useRef as pe,useState as Se}from"react";import{flushSync as J}from"react-dom";import{useActivePress as Re}from'../../hooks/use-active-press.js';import{useByComparator as _e}from'../../hooks/use-by-comparator.js';import{useControllable as Fe}from'../../hooks/use-controllable.js';import{useDefaultValue as Ce}from'../../hooks/use-default-value.js';import{useDidElementMove as Me}from'../../hooks/use-did-element-move.js';import{useDisposables as we}from'../../hooks/use-disposables.js';import{useElementSize as Ie}from'../../hooks/use-element-size.js';import{useEvent as C}from'../../hooks/use-event.js';import{useId as ee}from'../../hooks/use-id.js';import{useInertOthers as Be}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as ue}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as ke}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ue}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ne}from'../../hooks/use-outside-click.js';import{useOwnerDocument as be}from'../../hooks/use-owner.js';import{Action as te,useQuickRelease as He}from'../../hooks/use-quick-release.js';import{useResolveButtonType as Ge}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Ve}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as Q}from'../../hooks/use-sync-refs.js';import{useTextValue as Ke}from'../../hooks/use-text-value.js';import{useTrackedPointer as ze}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as We,useTransition as Xe}from'../../hooks/use-transition.js';import{useDisabled as je}from'../../internal/disabled.js';import{FloatingProvider as Je,useFloatingPanel as Qe,useFloatingPanelProps as $e,useFloatingReference as qe,useFloatingReferenceProps as Ye,useResolvedAnchor as Ze}from'../../internal/floating.js';import{FormFields as et}from'../../internal/form-fields.js';import{useFrozenData as tt}from'../../internal/frozen.js';import{useProvidedId as ot}from'../../internal/id.js';import{OpenClosedProvider as nt,State as oe,useOpenClosed as rt}from'../../internal/open-closed.js';import{stackMachines as at}from'../../machines/stack-machine.js';import{useSlice as M}from'../../react-glue.js';import{isDisabledReactIssue7711 as lt}from'../../utils/bugs.js';import{Focus as P}from'../../utils/calculate-active-index.js';import{disposables as it}from'../../utils/disposables.js';import*as st from'../../utils/dom.js';import{Focus as me,FocusableMode as pt,focusFrom as ut,isFocusableElement as dt}from'../../utils/focus-management.js';import{attemptSubmit as ct}from'../../utils/form.js';import{match as ne}from'../../utils/match.js';import{getOwnerDocument as ft}from'../../utils/owner.js';import{RenderFeatures as ye,forwardRefWithAs as $,mergeProps as xe,useRender as q}from'../../utils/render.js';import{useDescribedBy as Tt}from'../description/description.js';import{Keys as f}from'../keyboard.js';import{Label as bt,useLabelledBy as mt,useLabels as yt}from'../label/label.js';import{Portal as xt}from'../portal/portal.js';import{ActionTypes as Ot,ActivationTrigger as Oe,ListboxStates as T,ValueMode as k}from'./listbox-machine.js';import{ListboxContext as Lt,useListboxMachine as Pt,useListboxMachineContext as de}from'./listbox-machine-glue.js';let re=fe(null);re.displayName="ListboxDataContext";function Y(g){let D=Te(re);if(D===null){let x=new Error(`<${g} /> is missing a parent <Listbox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(x,Y),x}return D}let gt=ce;function vt(g,D){let x=ee(),u=je(),{value:l,defaultValue:p,form:R,name:i,onChange:b,by:o,invalid:d=!1,disabled:m=u||!1,horizontal:a=!1,multiple:t=!1,__demoMode:s=!1,...A}=g;const v=a?"horizontal":"vertical";let U=Q(D),w=Ce(p),[c=t?[]:void 0,O]=Fe(l,b,w),y=Pt({id:x,__demoMode:s}),I=pe({static:!1,hold:!1}),N=pe(new Map),_=_e(o),H=j(h=>ne(n.mode,{[k.Multi]:()=>c.some(W=>_(W,h)),[k.Single]:()=>_(c,h)}),[c]),n=V(()=>({value:c,disabled:m,invalid:d,mode:t?k.Multi:k.Single,orientation:v,onChange:O,compare:_,isSelected:H,optionsPropsRef:I,listRef:N}),[c,m,d,t,v,O,_,H,I,N]);ue(()=>{y.state.dataRef.current=n},[n]);let L=M(y,h=>h.listboxState),G=at.get(null),K=M(G,j(h=>G.selectors.isTop(h,x),[G,x])),[E,z]=M(y,h=>[h.buttonElement,h.optionsElement]);Ne(K,[E,z],(h,W)=>{y.send({type:Ot.CloseListbox}),dt(W,pt.Loose)||(h.preventDefault(),E==null||E.focus())});let r=V(()=>({open:L===T.Open,disabled:m,invalid:d,value:c}),[L,m,d,c]),[B,ae]=yt({inherit:!0}),le={ref:U},ie=j(()=>{if(w!==void 0)return O==null?void 0:O(w)},[O,w]),Z=q();return F.createElement(ae,{value:B,props:{htmlFor:E==null?void 0:E.id},slot:{open:L===T.Open,disabled:m}},F.createElement(Je,null,F.createElement(Lt.Provider,{value:y},F.createElement(re.Provider,{value:n},F.createElement(nt,{value:ne(L,{[T.Open]:oe.Open,[T.Closed]:oe.Closed})},i!=null&&c!=null&&F.createElement(et,{disabled:m,data:{[i]:c},form:R,onReset:ie}),Z({ourProps:le,theirProps:A,slot:r,defaultTag:gt,name:"Listbox"}))))))}let Et="button";function ht(g,D){let x=ee(),u=ot(),l=Y("Listbox.Button"),p=de("Listbox.Button"),{id:R=u||`headlessui-listbox-button-${x}`,disabled:i=l.disabled||!1,autoFocus:b=!1,...o}=g,d=Q(D,qe(),p.actions.setButtonElement),m=Ye(),[a,t,s]=M(p,r=>[r.listboxState,r.buttonElement,r.optionsElement]),A=a===T.Open;He(A,{trigger:t,action:j(r=>{if(t!=null&&t.contains(r.target))return te.Ignore;let B=r.target.closest('[role="option"]:not([data-disabled])');return st.isHTMLElement(B)?te.Select(B):s!=null&&s.contains(r.target)?te.Ignore:te.Close},[t,s]),close:p.actions.closeListbox,select:p.actions.selectActiveOption});let v=C(r=>{switch(r.key){case f.Enter:ct(r.currentTarget);break;case f.Space:case f.ArrowDown:r.preventDefault(),p.actions.openListbox({focus:l.value?P.Nothing:P.First});break;case f.ArrowUp:r.preventDefault(),p.actions.openListbox({focus:l.value?P.Nothing:P.Last});break}}),U=C(r=>{switch(r.key){case f.Space:r.preventDefault();break}}),w=C(r=>{var B;if(r.button===0){if(lt(r.currentTarget))return r.preventDefault();p.state.listboxState===T.Open?(J(()=>p.actions.closeListbox()),(B=p.state.buttonElement)==null||B.focus({preventScroll:!0})):(r.preventDefault(),p.actions.openListbox({focus:P.Nothing}))}}),c=C(r=>r.preventDefault()),O=mt([R]),y=Tt(),{isFocusVisible:I,focusProps:N}=he({autoFocus:b}),{isHovered:_,hoverProps:H}=De({isDisabled:i}),{pressed:n,pressProps:L}=Re({disabled:i}),G=V(()=>({open:a===T.Open,active:n||a===T.Open,disabled:i,invalid:l.invalid,value:l.value,hover:_,focus:I,autofocus:b}),[a,l.value,i,_,I,n,l.invalid,b]),K=M(p,r=>r.listboxState===T.Open),E=xe(m(),{ref:d,id:R,type:Ge(g,t),"aria-haspopup":"listbox","aria-controls":s==null?void 0:s.id,"aria-expanded":K,"aria-labelledby":O,"aria-describedby":y,disabled:i||void 0,autoFocus:b,onKeyDown:v,onKeyUp:U,onKeyPress:c,onPointerDown:w},N,H,L);return q()({ourProps:E,theirProps:o,slot:G,defaultTag:Et,name:"Listbox.Button"})}let Le=fe(!1),Dt="div",At=ye.RenderStrategy|ye.Static;function St(g,D){let x=ee(),{id:u=`headlessui-listbox-options-${x}`,anchor:l,portal:p=!1,modal:R=!0,transition:i=!1,...b}=g,o=Ze(l),[d,m]=Se(null);o&&(p=!0);let a=Y("Listbox.Options"),t=de("Listbox.Options"),[s,A,v,U]=M(t,e=>[e.listboxState,e.buttonElement,e.optionsElement,e.__demoMode]),w=be(A),c=be(v),O=rt(),[y,I]=Xe(i,d,O!==null?(O&oe.Open)===oe.Open:s===T.Open);Ue(y,A,t.actions.closeListbox);let N=U?!1:R&&s===T.Open;Ve(N,c);let _=U?!1:R&&s===T.Open;Be(_,{allowed:j(()=>[A,v],[A,v])});let H=s!==T.Open,L=Me(H,A)?!1:y,G=y&&s===T.Closed,K=tt(G,a.value),E=C(e=>a.compare(K,e)),z=M(t,e=>{var X;if(o==null||!((X=o==null?void 0:o.to)!=null&&X.includes("selection")))return null;let S=e.options.findIndex(se=>E(se.dataRef.current.value));return S===-1&&(S=0),S}),r=(()=>{if(o==null)return;if(z===null)return{...o,inner:void 0};let e=Array.from(a.listRef.current.values());return{...o,inner:{listRef:{current:e},index:z}}})(),[B,ae]=Qe(r),le=$e(),ie=Q(D,o?B:null,t.actions.setOptionsElement,m),Z=we();Ae(()=>{var S;let e=v;e&&s===T.Open&&e!==((S=ft(e))==null?void 0:S.activeElement)&&(e==null||e.focus({preventScroll:!0}))},[s,v]);let h=C(e=>{var S,X;switch(Z.dispose(),e.key){case f.Space:if(t.state.searchQuery!=="")return e.preventDefault(),e.stopPropagation(),t.actions.search(e.key);case f.Enter:if(e.preventDefault(),e.stopPropagation(),t.state.activeOptionIndex!==null){let{dataRef:se}=t.state.options[t.state.activeOptionIndex];t.actions.onChange(se.current.value)}a.mode===k.Single&&(J(()=>t.actions.closeListbox()),(S=t.state.buttonElement)==null||S.focus({preventScroll:!0}));break;case ne(a.orientation,{vertical:f.ArrowDown,horizontal:f.ArrowRight}):return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:P.Next});case ne(a.orientation,{vertical:f.ArrowUp,horizontal:f.ArrowLeft}):return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:P.Previous});case f.Home:case f.PageUp:return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:P.First});case f.End:case f.PageDown:return e.preventDefault(),e.stopPropagation(),t.actions.goToOption({focus:P.Last});case f.Escape:e.preventDefault(),e.stopPropagation(),J(()=>t.actions.closeListbox()),(X=t.state.buttonElement)==null||X.focus({preventScroll:!0});return;case f.Tab:e.preventDefault(),e.stopPropagation(),J(()=>t.actions.closeListbox()),ut(t.state.buttonElement,e.shiftKey?me.Previous:me.Next);break;default:e.key.length===1&&(t.actions.search(e.key),Z.setTimeout(()=>t.actions.clearSearch(),350));break}}),W=M(t,e=>{var S;return(S=e.buttonElement)==null?void 0:S.id}),Pe=V(()=>({open:s===T.Open}),[s]),ge=xe(o?le():{},{id:u,ref:ie,"aria-activedescendant":M(t,t.selectors.activeDescendantId),"aria-multiselectable":a.mode===k.Multi?!0:void 0,"aria-labelledby":W,"aria-orientation":a.orientation,onKeyDown:h,role:"listbox",tabIndex:s===T.Open?0:void 0,style:{...b.style,...ae,"--button-width":Ie(A,!0).width},...We(I)}),ve=q(),Ee=V(()=>a.mode===k.Multi?a:{...a,isSelected:E},[a,E]);return F.createElement(xt,{enabled:p?g.static||y:!1,ownerDocument:w},F.createElement(re.Provider,{value:Ee},ve({ourProps:ge,theirProps:b,slot:Pe,defaultTag:Dt,features:At,visible:L,name:"Listbox.Options"})))}let Rt="div";function _t(g,D){let x=ee(),{id:u=`headlessui-listbox-option-${x}`,disabled:l=!1,value:p,...R}=g,i=Te(Le)===!0,b=Y("Listbox.Option"),o=de("Listbox.Option"),d=M(o,n=>o.selectors.isActive(n,u)),m=b.isSelected(p),a=pe(null),t=Ke(a),s=ke({disabled:l,value:p,domRef:a,get textValue(){return t()}}),A=Q(D,a,n=>{n?b.listRef.current.set(u,n):b.listRef.current.delete(u)}),v=M(o,n=>o.selectors.shouldScrollIntoView(n,u));ue(()=>{if(v)return it().requestAnimationFrame(()=>{var n,L;(L=(n=a.current)==null?void 0:n.scrollIntoView)==null||L.call(n,{block:"nearest"})})},[v,a]),ue(()=>{if(!i)return o.actions.registerOption(u,s),()=>o.actions.unregisterOption(u)},[s,u,i]);let U=C(n=>{var L;if(l)return n.preventDefault();o.actions.onChange(p),b.mode===k.Single&&(J(()=>o.actions.closeListbox()),(L=o.state.buttonElement)==null||L.focus({preventScroll:!0}))}),w=C(()=>{if(l)return o.actions.goToOption({focus:P.Nothing});o.actions.goToOption({focus:P.Specific,id:u})}),c=ze(),O=C(n=>{c.update(n),!l&&(d||o.actions.goToOption({focus:P.Specific,id:u},Oe.Pointer))}),y=C(n=>{c.wasMoved(n)&&(l||d||o.actions.goToOption({focus:P.Specific,id:u},Oe.Pointer))}),I=C(n=>{c.wasMoved(n)&&(l||d&&o.actions.goToOption({focus:P.Nothing}))}),N=V(()=>({active:d,focus:d,selected:m,disabled:l,selectedOption:m&&i}),[d,m,l,i]),_=i?{}:{id:u,ref:A,role:"option",tabIndex:l===!0?void 0:-1,"aria-disabled":l===!0?!0:void 0,"aria-selected":m,disabled:void 0,onClick:U,onFocus:w,onPointerEnter:O,onMouseEnter:O,onPointerMove:y,onMouseMove:y,onPointerLeave:I,onMouseLeave:I},H=q();return!m&&i?null:H({ourProps:_,theirProps:R,slot:N,defaultTag:Rt,name:"Listbox.Option"})}let Ft=ce;function Ct(g,D){let{options:x,placeholder:u,...l}=g,R={ref:Q(D)},i=Y("ListboxSelectedOption"),b=V(()=>({}),[]),o=i.value===void 0||i.value===null||i.mode===k.Multi&&Array.isArray(i.value)&&i.value.length===0,d=q();return F.createElement(Le.Provider,{value:!0},d({ourProps:R,theirProps:{...l,children:F.createElement(F.Fragment,null,u&&o?u:x)},slot:b,defaultTag:Ft,name:"ListboxSelectedOption"}))}let Mt=$(vt),wt=$(ht),It=bt,Bt=$(St),kt=$(_t),Ut=$(Ct),wo=Object.assign(Mt,{Button:wt,Label:It,Options:Bt,Option:kt,SelectedOption:Ut});export{wo as Listbox,wt as ListboxButton,It as ListboxLabel,kt as ListboxOption,Bt as ListboxOptions,Ut as ListboxSelectedOption};
