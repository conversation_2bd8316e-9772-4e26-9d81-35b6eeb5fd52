"use client";import r,{use<PERSON>emo as f}from"react";import{useId as P}from'../../hooks/use-id.js';import{Disabled<PERSON>rovider as y,useDisabled as u}from'../../internal/disabled.js';import{FormFieldsProvider as D}from'../../internal/form-fields.js';import{IdProvider as v}from'../../internal/id.js';import{forwardRefWithAs as b,useRender as E}from'../../utils/render.js';import{useDescriptions as A}from'../description/description.js';import{useLabels as L}from'../label/label.js';let _="div";function c(d,l){let t=`headlessui-control-${P()}`,[s,p]=L(),[n,a]=A(),m=u(),{disabled:e=m||!1,...i}=d,o=f(()=>({disabled:e}),[e]),F={ref:l,disabled:e||void 0,"aria-disabled":e||void 0},T=E();return r.createElement(y,{value:e},r.createElement(p,{value:s},r.createElement(a,{value:n},r.createElement(v,{id:t},T({ourProps:F,theirProps:{...i,children:r.createElement(D,null,typeof i.children=="function"?i.children(o):i.children)},slot:o,defaultTag:_,name:"Field"})))))}let H=b(c);export{H as Field};
