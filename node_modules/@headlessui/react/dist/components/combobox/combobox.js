"use client";import{useFocusRing as ve}from"@react-aria/focus";import{useHover as Pe}from"@react-aria/interactions";import{useVirtualizer as Le}from"@tanstack/react-virtual";import F,{Fragment as Ee,create<PERSON>ontex<PERSON> as <PERSON><PERSON>,use<PERSON><PERSON><PERSON> as <PERSON>,use<PERSON>ontex<PERSON> as he,use<PERSON>em<PERSON> as K,useRef as me,useState as xe}from"react";import{flushSync as re}from"react-dom";import{useActivePress as Ve}from'../../hooks/use-active-press.js';import{useByComparator as we}from'../../hooks/use-by-comparator.js';import{useControllable as Be}from'../../hooks/use-controllable.js';import{useDefaultValue as Ne}from'../../hooks/use-default-value.js';import{useDisposables as ke}from'../../hooks/use-disposables.js';import{useElementSize as Ae}from'../../hooks/use-element-size.js';import{useEvent as O}from'../../hooks/use-event.js';import{useId as le}from'../../hooks/use-id.js';import{useInertOthers as Ue}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as ee}from'../../hooks/use-iso-morphic-effect.js';import{useLatestValue as He}from'../../hooks/use-latest-value.js';import{useOnDisappear as Ge}from'../../hooks/use-on-disappear.js';import{useOutsideClick as ze}from'../../hooks/use-outside-click.js';import{useOwnerDocument as ge}from'../../hooks/use-owner.js';import{Action as ie,useQuickRelease as Ke}from'../../hooks/use-quick-release.js';import{useRefocusableInput as Ie}from'../../hooks/use-refocusable-input.js';import{useResolveButtonType as We}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as Xe}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as ce}from'../../hooks/use-sync-refs.js';import{useTrackedPointer as $e}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as Je,useTransition as je}from'../../hooks/use-transition.js';import{useTreeWalker as qe}from'../../hooks/use-tree-walker.js';import{useWatch as Re}from'../../hooks/use-watch.js';import{useDisabled as Qe}from'../../internal/disabled.js';import{FloatingProvider as Ye,useFloatingPanel as Ze,useFloatingPanelProps as eo,useFloatingReference as oo,useResolvedAnchor as to}from'../../internal/floating.js';import{FormFields as no}from'../../internal/form-fields.js';import{Frozen as ro,useFrozenData as De}from'../../internal/frozen.js';import{useProvidedId as ao}from'../../internal/id.js';import{OpenClosedProvider as lo,State as fe,useOpenClosed as io}from'../../internal/open-closed.js';import{stackMachines as so}from'../../machines/stack-machine.js';import{useSlice as D}from'../../react-glue.js';import{history as _e}from'../../utils/active-element-history.js';import{isDisabledReactIssue7711 as uo}from'../../utils/bugs.js';import{Focus as L}from'../../utils/calculate-active-index.js';import{disposables as po}from'../../utils/disposables.js';import*as bo from'../../utils/dom.js';import{match as Te}from'../../utils/match.js';import{isMobile as mo}from'../../utils/platform.js';import{RenderFeatures as Fe,forwardRefWithAs as se,mergeProps as ye,useRender as ue}from'../../utils/render.js';import{useDescribedBy as co}from'../description/description.js';import{Keys as V}from'../keyboard.js';import{Label as fo,useLabelledBy as Ce,useLabels as To}from'../label/label.js';import{MouseButton as Se}from'../mouse.js';import{Portal as xo}from'../portal/portal.js';import{ActionTypes as go,ActivationTrigger as oe,ComboboxState as l,ValueMode as N}from'./combobox-machine.js';import{ComboboxContext as yo,useComboboxMachine as Co,useComboboxMachineContext as pe}from'./combobox-machine-glue.js';let de=Oe(null);de.displayName="ComboboxDataContext";function ae(C){let h=he(de);if(h===null){let e=new Error(`<${C} /> is missing a parent <Combobox /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(e,ae),e}return h}let Me=Oe(null);function vo(C){let h=pe("VirtualProvider"),e=ae("VirtualProvider"),{options:o}=e.virtual,A=D(h,a=>a.optionsElement),[R,v]=K(()=>{let a=A;if(!a)return[0,0];let u=window.getComputedStyle(a);return[parseFloat(u.paddingBlockStart||u.paddingTop),parseFloat(u.paddingBlockEnd||u.paddingBottom)]},[A]),T=Le({enabled:o.length!==0,scrollPaddingStart:R,scrollPaddingEnd:v,count:o.length,estimateSize(){return 40},getScrollElement(){return h.state.optionsElement},overscan:12}),[I,m]=xe(0);ee(()=>{m(a=>a+1)},[o]);let g=T.getVirtualItems(),n=D(h,a=>a.activationTrigger===oe.Pointer),f=D(h,h.selectors.activeOptionIndex);return g.length===0?null:F.createElement(Me.Provider,{value:T},F.createElement("div",{style:{position:"relative",width:"100%",height:`${T.getTotalSize()}px`},ref:a=>{a&&(n||f!==null&&o.length>f&&T.scrollToIndex(f))}},g.map(a=>{var u;return F.createElement(Ee,{key:a.key},F.cloneElement((u=C.children)==null?void 0:u.call(C,{...C.slot,option:o[a.index]}),{key:`${I}-${a.key}`,"data-index":a.index,"aria-setsize":o.length,"aria-posinset":a.index+1,style:{position:"absolute",top:0,left:0,transform:`translateY(${a.start}px)`,overflowAnchor:"none"}}))})))}let Po=Ee;function Eo(C,h){let e=le(),o=Qe(),{value:A,defaultValue:R,onChange:v,form:T,name:I,by:m,invalid:g=!1,disabled:n=o||!1,onClose:f,__demoMode:a=!1,multiple:u=!1,immediate:S=!1,virtual:d=null,nullable:k,...W}=C,y=Ne(R),[x=u?[]:void 0,P]=Be(A,v,y),b=Co({id:e,virtual:d,__demoMode:a}),G=me({static:!1,hold:!1}),_=we(m),z=O(s=>d?m===null?d.options.indexOf(s):d.options.findIndex(c=>_(c,s)):b.state.options.findIndex(c=>_(c.dataRef.current.value,s))),U=Z(s=>Te(r.mode,{[N.Multi]:()=>x.some(c=>_(c,s)),[N.Single]:()=>_(x,s)}),[x]),w=D(b,s=>s.virtual),J=O(()=>f==null?void 0:f()),r=K(()=>({__demoMode:a,immediate:S,optionsPropsRef:G,value:x,defaultValue:y,disabled:n,invalid:g,mode:u?N.Multi:N.Single,virtual:d?w:null,onChange:P,isSelected:U,calculateIndex:z,compare:_,onClose:J}),[x,y,n,g,u,P,U,a,b,d,w,J]);ee(()=>{var s;d&&b.send({type:go.UpdateVirtualConfiguration,options:d.options,disabled:(s=d.disabled)!=null?s:null})},[d,d==null?void 0:d.options,d==null?void 0:d.disabled]),ee(()=>{b.state.dataRef.current=r},[r]);let[M,X,i,H]=D(b,s=>[s.comboboxState,s.buttonElement,s.inputElement,s.optionsElement]),j=so.get(null),q=D(j,Z(s=>j.selectors.isTop(s,e),[j,e]));ze(q,[X,i,H],()=>b.actions.closeCombobox());let Q=D(b,b.selectors.activeOptionIndex),$=D(b,b.selectors.activeOption),be=K(()=>({open:M===l.Open,disabled:n,invalid:g,activeIndex:Q,activeOption:$,value:x}),[r,n,x,g,$,M]),[Y,te]=To(),t=h===null?{}:{ref:h},B=Z(()=>{if(y!==void 0)return P==null?void 0:P(y)},[P,y]),E=ue();return F.createElement(te,{value:Y,props:{htmlFor:i==null?void 0:i.id},slot:{open:M===l.Open,disabled:n}},F.createElement(Ye,null,F.createElement(de.Provider,{value:r},F.createElement(yo.Provider,{value:b},F.createElement(lo,{value:Te(M,{[l.Open]:fe.Open,[l.Closed]:fe.Closed})},I!=null&&F.createElement(no,{disabled:n,data:x!=null?{[I]:x}:{},form:T,onReset:B}),E({ourProps:t,theirProps:W,slot:be,defaultTag:Po,name:"Combobox"}))))))}let Oo="input";function ho(C,h){var Y,te;let e=pe("Combobox.Input"),o=ae("Combobox.Input"),A=le(),R=ao(),{id:v=R||`headlessui-combobox-input-${A}`,onChange:T,displayValue:I,disabled:m=o.disabled||!1,autoFocus:g=!1,type:n="text",...f}=C,[a]=D(e,t=>[t.inputElement]),u=me(null),S=ce(u,h,oo(),e.actions.setInputElement),d=ge(a),[k,W]=D(e,t=>[t.comboboxState,t.isTyping]),y=ke(),x=O(()=>{e.actions.onChange(null),e.state.optionsElement&&(e.state.optionsElement.scrollTop=0),e.actions.goToOption({focus:L.Nothing})}),P=K(()=>{var t;return typeof I=="function"&&o.value!==void 0?(t=I(o.value))!=null?t:"":typeof o.value=="string"?o.value:""},[o.value,I]);Re(([t,B],[E,s])=>{if(e.state.isTyping)return;let c=u.current;c&&((s===l.Open&&B===l.Closed||t!==E)&&(c.value=t),requestAnimationFrame(()=>{if(e.state.isTyping||!c||(d==null?void 0:d.activeElement)!==c)return;let{selectionStart:p,selectionEnd:ne}=c;Math.abs((ne!=null?ne:0)-(p!=null?p:0))===0&&p===0&&c.setSelectionRange(c.value.length,c.value.length)}))},[P,k,d,W]),Re(([t],[B])=>{if(t===l.Open&&B===l.Closed){if(e.state.isTyping)return;let E=u.current;if(!E)return;let s=E.value,{selectionStart:c,selectionEnd:p,selectionDirection:ne}=E;E.value="",E.value=s,ne!==null?E.setSelectionRange(c,p,ne):E.setSelectionRange(c,p)}},[k]);let b=me(!1),G=O(()=>{b.current=!0}),_=O(()=>{y.nextFrame(()=>{b.current=!1})}),z=O(t=>{switch(e.actions.setIsTyping(!0),t.key){case V.Enter:if(e.state.comboboxState!==l.Open||b.current)return;if(t.preventDefault(),t.stopPropagation(),e.selectors.activeOptionIndex(e.state)===null){e.actions.closeCombobox();return}e.actions.selectActiveOption(),o.mode===N.Single&&e.actions.closeCombobox();break;case V.ArrowDown:return t.preventDefault(),t.stopPropagation(),Te(e.state.comboboxState,{[l.Open]:()=>e.actions.goToOption({focus:L.Next}),[l.Closed]:()=>e.actions.openCombobox()});case V.ArrowUp:return t.preventDefault(),t.stopPropagation(),Te(e.state.comboboxState,{[l.Open]:()=>e.actions.goToOption({focus:L.Previous}),[l.Closed]:()=>{re(()=>e.actions.openCombobox()),o.value||e.actions.goToOption({focus:L.Last})}});case V.Home:if(t.shiftKey)break;return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.First});case V.PageUp:return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.First});case V.End:if(t.shiftKey)break;return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.Last});case V.PageDown:return t.preventDefault(),t.stopPropagation(),e.actions.goToOption({focus:L.Last});case V.Escape:return e.state.comboboxState!==l.Open?void 0:(t.preventDefault(),e.state.optionsElement&&!o.optionsPropsRef.current.static&&t.stopPropagation(),o.mode===N.Single&&o.value===null&&x(),e.actions.closeCombobox());case V.Tab:if(e.state.comboboxState!==l.Open)return;o.mode===N.Single&&e.state.activationTrigger!==oe.Focus&&e.actions.selectActiveOption(),e.actions.closeCombobox();break}}),U=O(t=>{T==null||T(t),o.mode===N.Single&&t.target.value===""&&x(),e.actions.openCombobox()}),w=O(t=>{var E,s,c;let B=(E=t.relatedTarget)!=null?E:_e.find(p=>p!==t.currentTarget);if(!((s=e.state.optionsElement)!=null&&s.contains(B))&&!((c=e.state.buttonElement)!=null&&c.contains(B))&&e.state.comboboxState===l.Open)return t.preventDefault(),o.mode===N.Single&&o.value===null&&x(),e.actions.closeCombobox()}),J=O(t=>{var E,s,c;let B=(E=t.relatedTarget)!=null?E:_e.find(p=>p!==t.currentTarget);(s=e.state.buttonElement)!=null&&s.contains(B)||(c=e.state.optionsElement)!=null&&c.contains(B)||o.disabled||o.immediate&&e.state.comboboxState!==l.Open&&y.microTask(()=>{re(()=>e.actions.openCombobox()),e.actions.setActivationTrigger(oe.Focus)})}),r=Ce(),M=co(),{isFocused:X,focusProps:i}=ve({autoFocus:g}),{isHovered:H,hoverProps:j}=Pe({isDisabled:m}),q=D(e,t=>t.optionsElement),Q=K(()=>({open:k===l.Open,disabled:m,invalid:o.invalid,hover:H,focus:X,autofocus:g}),[o,H,X,g,m,o.invalid]),$=ye({ref:S,id:v,role:"combobox",type:n,"aria-controls":q==null?void 0:q.id,"aria-expanded":k===l.Open,"aria-activedescendant":D(e,e.selectors.activeDescendantId),"aria-labelledby":r,"aria-describedby":M,"aria-autocomplete":"list",defaultValue:(te=(Y=C.defaultValue)!=null?Y:o.defaultValue!==void 0?I==null?void 0:I(o.defaultValue):null)!=null?te:o.defaultValue,disabled:m||void 0,autoFocus:g,onCompositionStart:G,onCompositionEnd:_,onKeyDown:z,onChange:U,onFocus:J,onBlur:w},i,j);return ue()({ourProps:$,theirProps:f,slot:Q,defaultTag:Oo,name:"Combobox.Input"})}let Ao="button";function Io(C,h){let e=pe("Combobox.Button"),o=ae("Combobox.Button"),[A,R]=xe(null),v=ce(h,R,e.actions.setButtonElement),T=le(),{id:I=`headlessui-combobox-button-${T}`,disabled:m=o.disabled||!1,autoFocus:g=!1,...n}=C,[f,a,u]=D(e,r=>[r.comboboxState,r.inputElement,r.optionsElement]),S=Ie(a),d=f===l.Open;Ke(d,{trigger:A,action:Z(r=>{if(A!=null&&A.contains(r.target))return ie.Ignore;if(a!=null&&a.contains(r.target))return ie.Ignore;let M=r.target.closest('[role="option"]:not([data-disabled])');return bo.isHTMLElement(M)?ie.Select(M):u!=null&&u.contains(r.target)?ie.Ignore:ie.Close},[A,a,u]),close:e.actions.closeCombobox,select:e.actions.selectActiveOption});let k=O(r=>{switch(r.key){case V.Space:case V.Enter:r.preventDefault(),r.stopPropagation(),e.state.comboboxState===l.Closed&&re(()=>e.actions.openCombobox()),S();return;case V.ArrowDown:r.preventDefault(),r.stopPropagation(),e.state.comboboxState===l.Closed&&(re(()=>e.actions.openCombobox()),e.state.dataRef.current.value||e.actions.goToOption({focus:L.First})),S();return;case V.ArrowUp:r.preventDefault(),r.stopPropagation(),e.state.comboboxState===l.Closed&&(re(()=>e.actions.openCombobox()),e.state.dataRef.current.value||e.actions.goToOption({focus:L.Last})),S();return;case V.Escape:if(e.state.comboboxState!==l.Open)return;r.preventDefault(),e.state.optionsElement&&!o.optionsPropsRef.current.static&&r.stopPropagation(),re(()=>e.actions.closeCombobox()),S();return;default:return}}),W=O(r=>{r.preventDefault(),!uo(r.currentTarget)&&(r.button===Se.Left&&(e.state.comboboxState===l.Open?e.actions.closeCombobox():e.actions.openCombobox()),S())}),y=Ce([I]),{isFocusVisible:x,focusProps:P}=ve({autoFocus:g}),{isHovered:b,hoverProps:G}=Pe({isDisabled:m}),{pressed:_,pressProps:z}=Ve({disabled:m}),U=K(()=>({open:f===l.Open,active:_||f===l.Open,disabled:m,invalid:o.invalid,value:o.value,hover:b,focus:x}),[o,b,x,_,m,f]),w=ye({ref:v,id:I,type:We(C,A),tabIndex:-1,"aria-haspopup":"listbox","aria-controls":u==null?void 0:u.id,"aria-expanded":f===l.Open,"aria-labelledby":y,disabled:m||void 0,autoFocus:g,onPointerDown:W,onKeyDown:k},P,G,z);return ue()({ourProps:w,theirProps:n,slot:U,defaultTag:Ao,name:"Combobox.Button"})}let Ro="div",Do=Fe.RenderStrategy|Fe.Static;function _o(C,h){var E,s,c;let e=le(),{id:o=`headlessui-combobox-options-${e}`,hold:A=!1,anchor:R,portal:v=!1,modal:T=!0,transition:I=!1,...m}=C,g=pe("Combobox.Options"),n=ae("Combobox.Options"),f=to(R);f&&(v=!0);let[a,u]=Ze(f),[S,d]=xe(null),k=eo(),W=ce(h,f?a:null,g.actions.setOptionsElement,d),[y,x,P,b,G]=D(g,p=>[p.comboboxState,p.inputElement,p.buttonElement,p.optionsElement,p.activationTrigger]),_=ge(x||P),z=ge(b),U=io(),[w,J]=je(I,S,U!==null?(U&fe.Open)===fe.Open:y===l.Open);Ge(w,x,g.actions.closeCombobox);let r=n.__demoMode?!1:T&&y===l.Open;Xe(r,z);let M=n.__demoMode?!1:T&&y===l.Open;Ue(M,{allowed:Z(()=>[x,P,b],[x,P,b])}),ee(()=>{var p;n.optionsPropsRef.current.static=(p=C.static)!=null?p:!1},[n.optionsPropsRef,C.static]),ee(()=>{n.optionsPropsRef.current.hold=A},[n.optionsPropsRef,A]),qe(y===l.Open,{container:b,accept(p){return p.getAttribute("role")==="option"?NodeFilter.FILTER_REJECT:p.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(p){p.setAttribute("role","none")}});let X=Ce([P==null?void 0:P.id]),i=K(()=>({open:y===l.Open,option:void 0}),[y]),H=O(()=>{g.actions.setActivationTrigger(oe.Pointer)}),j=O(p=>{p.preventDefault(),g.actions.setActivationTrigger(oe.Pointer)}),q=ye(f?k():{},{"aria-labelledby":X,role:"listbox","aria-multiselectable":n.mode===N.Multi?!0:void 0,id:o,ref:W,style:{...m.style,...u,"--input-width":Ae(x,!0).width,"--button-width":Ae(P,!0).width},onWheel:G===oe.Pointer?void 0:H,onMouseDown:j,...Je(J)}),Q=w&&y===l.Closed,$=De(Q,(E=n.virtual)==null?void 0:E.options),be=De(Q,n.value),Y=O(p=>n.compare(be,p)),te=K(()=>{if(!n.virtual)return n;if($===void 0)throw new Error("Missing `options` in virtual mode");return $!==n.virtual.options?{...n,virtual:{...n.virtual,options:$}}:n},[n,$,(s=n.virtual)==null?void 0:s.options]);n.virtual&&Object.assign(m,{children:F.createElement(de.Provider,{value:te},F.createElement(vo,{slot:i},m.children))});let t=ue(),B=K(()=>n.mode===N.Multi?n:{...n,isSelected:Y},[n,Y]);return F.createElement(xo,{enabled:v?C.static||w:!1,ownerDocument:_},F.createElement(de.Provider,{value:B},t({ourProps:q,theirProps:{...m,children:F.createElement(ro,{freeze:Q},typeof m.children=="function"?(c=m.children)==null?void 0:c.call(m,i):m.children)},slot:i,defaultTag:Ro,features:Do,visible:w,name:"Combobox.Options"})))}let Fo="div";function So(C,h){var r,M,X;let e=ae("Combobox.Option"),o=pe("Combobox.Option"),A=le(),{id:R=`headlessui-combobox-option-${A}`,value:v,disabled:T=(X=(M=(r=e.virtual)==null?void 0:r.disabled)==null?void 0:M.call(r,v))!=null?X:!1,order:I=null,...m}=C,[g]=D(o,i=>[i.inputElement]),n=Ie(g),f=D(o,Z(i=>o.selectors.isActive(i,v,R),[v,R])),a=e.isSelected(v),u=me(null),S=He({disabled:T,value:v,domRef:u,order:I}),d=he(Me),k=ce(h,u,d?d.measureElement:null),W=O(()=>{o.actions.setIsTyping(!1),o.actions.onChange(v)});ee(()=>o.actions.registerOption(R,S),[S,R]);let y=D(o,Z(i=>o.selectors.shouldScrollIntoView(i,v,R),[v,R]));ee(()=>{if(y)return po().requestAnimationFrame(()=>{var i,H;(H=(i=u.current)==null?void 0:i.scrollIntoView)==null||H.call(i,{block:"nearest"})})},[y,u]);let x=O(i=>{i.preventDefault(),i.button===Se.Left&&(T||(W(),mo()||requestAnimationFrame(()=>n()),e.mode===N.Single&&o.actions.closeCombobox()))}),P=O(()=>{if(T)return o.actions.goToOption({focus:L.Nothing});let i=e.calculateIndex(v);o.actions.goToOption({focus:L.Specific,idx:i})}),b=$e(),G=O(i=>b.update(i)),_=O(i=>{if(!b.wasMoved(i)||T||f)return;let H=e.calculateIndex(v);o.actions.goToOption({focus:L.Specific,idx:H},oe.Pointer)}),z=O(i=>{b.wasMoved(i)&&(T||f&&(e.optionsPropsRef.current.hold||o.actions.goToOption({focus:L.Nothing})))}),U=K(()=>({active:f,focus:f,selected:a,disabled:T}),[f,a,T]),w={id:R,ref:k,role:"option",tabIndex:T===!0?void 0:-1,"aria-disabled":T===!0?!0:void 0,"aria-selected":a,disabled:void 0,onMouseDown:x,onFocus:P,onPointerEnter:G,onMouseEnter:G,onPointerMove:_,onMouseMove:_,onPointerLeave:z,onMouseLeave:z};return ue()({ourProps:w,theirProps:m,slot:U,defaultTag:Fo,name:"Combobox.Option"})}let Mo=se(Eo),Lo=se(Io),Vo=se(ho),wo=fo,Bo=se(_o),No=se(So),wt=Object.assign(Mo,{Input:Vo,Button:Lo,Label:wo,Options:Bo,Option:No});export{wt as Combobox,Lo as ComboboxButton,Vo as ComboboxInput,wo as ComboboxLabel,No as ComboboxOption,Bo as ComboboxOptions};
