import*as l from"react";const s=l.createContext(null);function a(){return{groups:new Map,get(o,e){var i;let t=this.groups.get(o);t||(t=new Map,this.groups.set(o,t));let n=(i=t.get(e))!=null?i:0;t.set(e,n+1);let r=Array.from(t.keys()).indexOf(e);function u(){let c=t.get(e);c>1?t.set(e,c-1):t.delete(e)}return[r,u]}}}function f({children:o}){let e=l.useRef(a());return l.createElement(s.Provider,{value:e},o)}function C(o){let e=l.useContext(s);if(!e)throw new Error("You must wrap your component in a <StableCollection>");let t=l.useId(),[n,r]=e.current.get(o,t);return l.useEffect(()=>r,[]),n}export{f as StableCollection,C as useStableCollectionIndex};
