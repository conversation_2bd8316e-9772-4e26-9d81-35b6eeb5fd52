{"version": 3, "file": "is.js", "sourceRoot": "", "sources": ["../src/is.ts"], "names": [], "mappings": ";;;AAAA,IAAM,OAAO,GAAG,UAAC,OAAY;IAC3B,OAAA,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAApD,CAAoD,CAAC;AAEhD,IAAM,WAAW,GAAG,UAAC,OAAY;IACtC,OAAA,OAAO,OAAO,KAAK,WAAW;AAA9B,CAA8B,CAAC;AADpB,QAAA,WAAW,eACS;AAE1B,IAAM,MAAM,GAAG,UAAC,OAAY,IAAsB,OAAA,OAAO,KAAK,IAAI,EAAhB,CAAgB,CAAC;AAA7D,QAAA,MAAM,UAAuD;AAEnE,IAAM,aAAa,GAAG,UAC3B,OAAY;IAEZ,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI;QAAE,OAAO,KAAK,CAAC;IAClE,IAAI,OAAO,KAAK,MAAM,CAAC,SAAS;QAAE,OAAO,KAAK,CAAC;IAC/C,IAAI,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,IAAI;QAAE,OAAO,IAAI,CAAC;IAEzD,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC;AAC7D,CAAC,CAAC;AARW,QAAA,aAAa,iBAQxB;AAEK,IAAM,aAAa,GAAG,UAAC,OAAY;IACxC,OAAA,qBAAa,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC;AAA3D,CAA2D,CAAC;AADjD,QAAA,aAAa,iBACoC;AAEvD,IAAM,OAAO,GAAG,UAAC,OAAY;IAClC,OAAA,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC;AAAtB,CAAsB,CAAC;AADZ,QAAA,OAAO,WACK;AAElB,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,OAAO,KAAK,QAAQ;AAA3B,CAA2B,CAAC;AADjB,QAAA,QAAQ,YACS;AAEvB,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,OAAO,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AAA9C,CAA8C,CAAC;AADpC,QAAA,QAAQ,YAC4B;AAE1C,IAAM,SAAS,GAAG,UAAC,OAAY;IACpC,OAAA,OAAO,OAAO,KAAK,SAAS;AAA5B,CAA4B,CAAC;AADlB,QAAA,SAAS,aACS;AAExB,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,YAAY,MAAM;AAAzB,CAAyB,CAAC;AADf,QAAA,QAAQ,YACO;AAErB,IAAM,KAAK,GAAG,UAAC,OAAY;IAChC,OAAA,OAAO,YAAY,GAAG;AAAtB,CAAsB,CAAC;AADZ,QAAA,KAAK,SACO;AAElB,IAAM,KAAK,GAAG,UAAC,OAAY;IAChC,OAAA,OAAO,YAAY,GAAG;AAAtB,CAAsB,CAAC;AADZ,QAAA,KAAK,SACO;AAElB,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,CAAC,OAAO,CAAC,KAAK,QAAQ;AAA7B,CAA6B,CAAC;AADnB,QAAA,QAAQ,YACW;AAEzB,IAAM,MAAM,GAAG,UAAC,OAAY;IACjC,OAAA,OAAO,YAAY,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AAApD,CAAoD,CAAC;AAD1C,QAAA,MAAM,UACoC;AAEhD,IAAM,OAAO,GAAG,UAAC,OAAY;IAClC,OAAA,OAAO,YAAY,KAAK;AAAxB,CAAwB,CAAC;AADd,QAAA,OAAO,WACO;AAEpB,IAAM,UAAU,GAAG,UAAC,OAAY;IACrC,OAAA,OAAO,OAAO,KAAK,QAAQ,IAAI,KAAK,CAAC,OAAO,CAAC;AAA7C,CAA6C,CAAC;AADnC,QAAA,UAAU,cACyB;AAEzC,IAAM,WAAW,GAAG,UACzB,OAAY;IAEZ,OAAA,iBAAS,CAAC,OAAO,CAAC;QAClB,cAAM,CAAC,OAAO,CAAC;QACf,mBAAW,CAAC,OAAO,CAAC;QACpB,gBAAQ,CAAC,OAAO,CAAC;QACjB,gBAAQ,CAAC,OAAO,CAAC;QACjB,gBAAQ,CAAC,OAAO,CAAC;AALjB,CAKiB,CAAC;AARP,QAAA,WAAW,eAQJ;AAEb,IAAM,QAAQ,GAAG,UAAC,OAAY;IACnC,OAAA,OAAO,OAAO,KAAK,QAAQ;AAA3B,CAA2B,CAAC;AADjB,QAAA,QAAQ,YACS;AAEvB,IAAM,UAAU,GAAG,UAAC,OAAY;IACrC,OAAA,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,CAAC,QAAQ;AAA7C,CAA6C,CAAC;AADnC,QAAA,UAAU,cACyB;AAezC,IAAM,YAAY,GAAG,UAAC,OAAY;IACvC,OAAA,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,YAAY,QAAQ,CAAC;AAA7D,CAA6D,CAAC;AADnD,QAAA,YAAY,gBACuC;AAEzD,IAAM,KAAK,GAAG,UAAC,OAAY,IAAqB,OAAA,OAAO,YAAY,GAAG,EAAtB,CAAsB,CAAC;AAAjE,QAAA,KAAK,SAA4D"}