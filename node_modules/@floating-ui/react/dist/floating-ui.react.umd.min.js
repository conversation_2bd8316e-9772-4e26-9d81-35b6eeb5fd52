!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("@floating-ui/react-dom")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","@floating-ui/react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).FloatingUIReact={},e.<PERSON><PERSON>,e.ReactDOM,e.FloatingUIReactDOM)}(this,(function(e,t,n,r){"use strict";function o(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=o(t),i=o(n);function c(e){return u.useMemo((()=>e.every((e=>null==e))?null:t=>{e.forEach((e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)}))}),e)}const l={...u},s=l.useInsertionEffect||(e=>e());function a(e){const t=u.useRef((()=>{}));return s((()=>{t.current=e})),u.useCallback((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)}),[])}function f(){return"undefined"!=typeof window}function d(e){return v(e)?(e.nodeName||"").toLowerCase():"#document"}function m(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function v(e){return!!f()&&(e instanceof Node||e instanceof m(e).Node)}function p(e){return!!f()&&(e instanceof Element||e instanceof m(e).Element)}function g(e){return!!f()&&(e instanceof HTMLElement||e instanceof m(e).HTMLElement)}function h(e){return!(!f()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof m(e).ShadowRoot)}function y(e){return["html","body","#document"].includes(d(e))}function b(e){return m(e).getComputedStyle(e)}function w(e){if("html"===d(e))return e;const t=e.assignedSlot||e.parentNode||h(e)&&e.host||function(e){var t;return null==(t=(v(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}(e);return h(t)?t.host:t}function E(e){let t=e.activeElement;for(;null!=(null==(n=t)||null==(n=n.shadowRoot)?void 0:n.activeElement);){var n;t=t.shadowRoot.activeElement}return t}function R(e,t){if(!e||!t)return!1;const n=null==t.getRootNode?void 0:t.getRootNode();if(e.contains(t))return!0;if(n&&h(n)){let n=t;for(;n;){if(e===n)return!0;n=n.parentNode||n.host}}return!1}function x(){const e=navigator.userAgentData;return null!=e&&e.platform?e.platform:navigator.platform}function I(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map((e=>{let{brand:t,version:n}=e;return t+"/"+n})).join(" "):navigator.userAgent}function k(e){return!(0!==e.mozInputSource||!e.isTrusted)||(O()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function M(e){return!I().includes("jsdom/")&&(!O()&&0===e.width&&0===e.height||O()&&1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType||e.width<1&&e.height<1&&0===e.pressure&&0===e.detail&&"touch"===e.pointerType)}function C(){return/apple/i.test(navigator.vendor)}function O(){const e=/android/i;return e.test(x())||e.test(I())}function T(){return x().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints}function S(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function P(e){return(null==e?void 0:e.ownerDocument)||document}function L(e,t){if(null==t)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return null!=n.target&&t.contains(n.target)}function A(e){return"composedPath"in e?e.composedPath()[0]:e.target}const N="input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";function D(e){return g(e)&&e.matches(N)}function F(e){e.preventDefault(),e.stopPropagation()}function j(e){return!!e&&("combobox"===e.getAttribute("role")&&D(e))}const K=Math.min,H=Math.max,q=Math.round,_=Math.floor;const W="ArrowUp",B="ArrowDown",U="ArrowLeft",z="ArrowRight";function X(e,t,n){return Math.floor(e/t)!==n}function Y(e,t){return t<0||t>=e.current.length}function V(e,t){return Z(e,{disabledIndices:t})}function G(e,t){return Z(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function Z(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:o,amount:u=1}=void 0===t?{}:t;const i=e.current;let c=n;do{c+=r?-u:u}while(c>=0&&c<=i.length-1&&te(i,c,o));return c}function $(e,t){let{event:n,orientation:r,loop:o,rtl:u,cols:i,disabledIndices:c,minIndex:l,maxIndex:s,prevIndex:a,stopEvent:f=!1}=t,d=a;if(n.key===W){if(f&&F(n),-1===a)d=s;else if(d=Z(e,{startingIndex:d,amount:i,decrement:!0,disabledIndices:c}),o&&(a-i<l||d<0)){const e=a%i,t=s%i,n=s-(t-e);d=t===e?s:t>e?n:n-i}Y(e,d)&&(d=a)}if(n.key===B&&(f&&F(n),-1===a?d=l:(d=Z(e,{startingIndex:a,amount:i,disabledIndices:c}),o&&a+i>s&&(d=Z(e,{startingIndex:a%i-i,amount:i,disabledIndices:c}))),Y(e,d)&&(d=a)),"both"===r){const t=_(a/i);n.key===(u?U:z)&&(f&&F(n),a%i!=i-1?(d=Z(e,{startingIndex:a,disabledIndices:c}),o&&X(d,i,t)&&(d=Z(e,{startingIndex:a-a%i-1,disabledIndices:c}))):o&&(d=Z(e,{startingIndex:a-a%i-1,disabledIndices:c})),X(d,i,t)&&(d=a)),n.key===(u?z:U)&&(f&&F(n),a%i!=0?(d=Z(e,{startingIndex:a,decrement:!0,disabledIndices:c}),o&&X(d,i,t)&&(d=Z(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:c}))):o&&(d=Z(e,{startingIndex:a+(i-a%i),decrement:!0,disabledIndices:c})),X(d,i,t)&&(d=a));const r=_(s/i)===t;Y(e,d)&&(d=o&&r?n.key===(u?z:U)?s:Z(e,{startingIndex:a-a%i-1,disabledIndices:c}):a)}return d}function Q(e,t,n){const r=[];let o=0;return e.forEach(((e,u)=>{let{width:i,height:c}=e,l=!1;for(n&&(o=0);!l;){const e=[];for(let n=0;n<i;n++)for(let r=0;r<c;r++)e.push(o+n+r*t);o%t+i<=t&&e.every((e=>null==r[e]))?(e.forEach((e=>{r[e]=u})),l=!0):o++}})),[...r]}function J(e,t,n,r,o){if(-1===e)return-1;const u=n.indexOf(e),i=t[e];switch(o){case"tl":return u;case"tr":return i?u+i.width-1:u;case"bl":return i?u+(i.height-1)*r:u;case"br":return n.lastIndexOf(e)}}function ee(e,t){return t.flatMap(((t,n)=>e.includes(t)?[n]:[]))}function te(e,t,n){if(n)return n.includes(t);const r=e[t];return null==r||r.hasAttribute("disabled")||"true"===r.getAttribute("aria-disabled")}var ne="undefined"!=typeof document?t.useLayoutEffect:t.useEffect;function re(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}const oe=u.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function ue(e){const{children:t,elementsRef:n,labelsRef:r}=e,[o,i]=u.useState((()=>new Map)),c=u.useCallback((e=>{i((t=>new Map(t).set(e,null)))}),[]),l=u.useCallback((e=>{i((t=>{const n=new Map(t);return n.delete(e),n}))}),[]);return ne((()=>{const e=new Map(o);Array.from(e.keys()).sort(re).forEach(((t,n)=>{e.set(t,n)})),function(e,t){if(e.size!==t.size)return!1;for(const[n,r]of e.entries())if(r!==t.get(n))return!1;return!0}(o,e)||i(e)}),[o]),u.createElement(oe.Provider,{value:u.useMemo((()=>({register:c,unregister:l,map:o,elementsRef:n,labelsRef:r})),[c,l,o,n,r])},t)}function ie(e){void 0===e&&(e={});const{label:t}=e,{register:n,unregister:r,map:o,elementsRef:i,labelsRef:c}=u.useContext(oe),[l,s]=u.useState(null),a=u.useRef(null),f=u.useCallback((e=>{if(a.current=e,null!==l&&(i.current[l]=e,c)){var n;const r=void 0!==t;c.current[l]=r?t:null!=(n=null==e?void 0:e.textContent)?n:null}}),[l,i,c,t]);return ne((()=>{const e=a.current;if(e)return n(e),()=>{r(e)}}),[n,r]),ne((()=>{const e=a.current?o.get(a.current):null;null!=e&&s(e)}),[o]),u.useMemo((()=>({ref:f,index:null==l?-1:l})),[l,f])}function ce(e,t){return"function"==typeof e?e(t):e?u.cloneElement(e,t):u.createElement("div",t)}const le=u.createContext({activeIndex:0,onNavigate:()=>{}}),se=[U,z],ae=[W,B],fe=[...se,...ae],de=u.forwardRef((function(e,t){const{render:n,orientation:r="both",loop:o=!0,rtl:i=!1,cols:c=1,disabledIndices:l,activeIndex:s,onNavigate:f,itemSizes:d,dense:m=!1,...v}=e,[p,g]=u.useState(0),h=null!=s?s:p,y=a(null!=f?f:g),b=u.useRef([]),w=n&&"function"!=typeof n?n.props:{},E=u.useMemo((()=>({activeIndex:h,onNavigate:y})),[h,y]),R=c>1;const x={...v,...w,ref:t,"aria-orientation":"both"===r?void 0:r,onKeyDown(e){null==v.onKeyDown||v.onKeyDown(e),null==w.onKeyDown||w.onKeyDown(e),function(e){if(!fe.includes(e.key))return;let t=h;const n=V(b,l),u=G(b,l),s=i?U:z,a=i?z:U;if(R){const a=d||Array.from({length:b.current.length},(()=>({width:1,height:1}))),f=Q(a,c,m),v=f.findIndex((e=>null!=e&&!te(b.current,e,l))),p=f.reduce(((e,t,n)=>null==t||te(b.current,t,l)?e:n),-1),g=f[$({current:f.map((e=>e?b.current[e]:null))},{event:e,orientation:r,loop:o,rtl:i,cols:c,disabledIndices:ee([...l||b.current.map(((e,t)=>te(b.current,t)?t:void 0)),void 0],f),minIndex:v,maxIndex:p,prevIndex:J(h>u?n:h,a,f,c,e.key===B?"bl":e.key===s?"tr":"tl")})];null!=g&&(t=g)}const f={horizontal:[s],vertical:[B],both:[s,B]}[r],v={horizontal:[a],vertical:[W],both:[a,W]}[r],p=R?fe:{horizontal:se,vertical:ae,both:fe}[r];var g;t===h&&[...f,...v].includes(e.key)&&(t=o&&t===u&&f.includes(e.key)?n:o&&t===n&&v.includes(e.key)?u:Z(b,{startingIndex:t,decrement:v.includes(e.key),disabledIndices:l})),t===h||Y(b,t)||(e.stopPropagation(),p.includes(e.key)&&e.preventDefault(),y(t),null==(g=b.current[t])||g.focus())}(e)}};return u.createElement(le.Provider,{value:E},u.createElement(ue,{elementsRef:b},ce(n,x)))})),me=u.forwardRef((function(e,t){const{render:n,...r}=e,o=n&&"function"!=typeof n?n.props:{},{activeIndex:i,onNavigate:l}=u.useContext(le),{ref:s,index:a}=ie(),f=c([s,t,o.ref]),d=i===a;return ce(n,{...r,...o,ref:f,tabIndex:d?0:-1,"data-active":d?"":void 0,onFocus(e){null==r.onFocus||r.onFocus(e),null==o.onFocus||o.onFocus(e),l(a)}})}));function ve(){return ve=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ve.apply(this,arguments)}let pe=!1,ge=0;const he=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+ge++;const ye=l.useId||function(){const[e,t]=u.useState((()=>pe?he():void 0));return ne((()=>{null==e&&t(he())}),[]),u.useEffect((()=>{pe=!0}),[]),e},be=u.forwardRef((function(e,t){const{context:{placement:n,elements:{floating:r},middlewareData:{arrow:o,shift:i}},width:c=14,height:l=7,tipRadius:s=0,strokeWidth:a=0,staticOffset:f,stroke:d,d:m,style:{transform:v,...p}={},...g}=e,h=ye(),[y,w]=u.useState(!1);if(ne((()=>{if(!r)return;"rtl"===b(r).direction&&w(!0)}),[r]),!r)return null;const[E,R]=n.split("-"),x="top"===E||"bottom"===E;let I=f;(x&&null!=i&&i.x||!x&&null!=i&&i.y)&&(I=null);const k=2*a,M=k/2,C=c/2*(s/-8+1),O=l/2*s/4,T=!!m,S=I&&"end"===R?"bottom":"top";let P=I&&"end"===R?"right":"left";I&&y&&(P="end"===R?"left":"right");const L=null!=(null==o?void 0:o.x)?I||o.x:"",A=null!=(null==o?void 0:o.y)?I||o.y:"",N=m||"M0,0 H"+c+" L"+(c-C)+","+(l-O)+" Q"+c/2+","+l+" "+C+","+(l-O)+" Z",D={top:T?"rotate(180deg)":"",left:T?"rotate(90deg)":"rotate(-90deg)",bottom:T?"":"rotate(180deg)",right:T?"rotate(-90deg)":"rotate(90deg)"}[E];return u.createElement("svg",ve({},g,{"aria-hidden":!0,ref:t,width:T?c:c+k,height:c,viewBox:"0 0 "+c+" "+(l>c?l:c),style:{position:"absolute",pointerEvents:"none",[P]:L,[S]:A,[E]:x||T?"100%":"calc(100% - "+k/2+"px)",transform:[D,v].filter((e=>!!e)).join(" "),...p}}),k>0&&u.createElement("path",{clipPath:"url(#"+h+")",fill:"none",stroke:d,strokeWidth:k+(m?0:1),d:N}),u.createElement("path",{stroke:k&&!m?g.fill:"none",d:N}),u.createElement("clipPath",{id:h},u.createElement("rect",{x:-M,y:M*(T?-1:1),width:c+k,height:c})))}));function we(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,(null==(r=e.get(t))?void 0:r.filter((e=>e!==n)))||[])}}}const Ee=u.createContext(null),Re=u.createContext(null),xe=()=>{var e;return(null==(e=u.useContext(Ee))?void 0:e.id)||null},Ie=()=>u.useContext(Re);function ke(e){return"data-floating-ui-"+e}function Me(e){const n=t.useRef(e);return ne((()=>{n.current=e})),n}const Ce=ke("safe-polygon");function Oe(e,t,n){return n&&!S(n)?0:"number"==typeof e?e:null==e?void 0:e[t]}const Te=()=>{},Se=u.createContext({delay:0,initialDelay:0,timeoutMs:0,currentId:null,setCurrentId:Te,setState:Te,isInstantPhase:!1}),Pe=()=>u.useContext(Se);
/*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  */
var Le=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"].join(","),Ae="undefined"==typeof Element,Ne=Ae?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,De=!Ae&&Element.prototype.getRootNode?function(e){var t;return null==e||null===(t=e.getRootNode)||void 0===t?void 0:t.call(e)}:function(e){return null==e?void 0:e.ownerDocument},Fe=function e(t,n){var r;void 0===n&&(n=!0);var o=null==t||null===(r=t.getAttribute)||void 0===r?void 0:r.call(t,"inert");return""===o||"true"===o||n&&t&&e(t.parentNode)},je=function e(t,n,r){for(var o=[],u=Array.from(t);u.length;){var i=u.shift();if(!Fe(i,!1))if("SLOT"===i.tagName){var c=i.assignedElements(),l=e(c.length?c:i.children,!0,r);r.flatten?o.push.apply(o,l):o.push({scopeParent:i,candidates:l})}else{Ne.call(i,Le)&&r.filter(i)&&(n||!t.includes(i))&&o.push(i);var s=i.shadowRoot||"function"==typeof r.getShadowRoot&&r.getShadowRoot(i),a=!Fe(s,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(i));if(s&&a){var f=e(!0===s?i.children:s.children,!0,r);r.flatten?o.push.apply(o,f):o.push({scopeParent:i,candidates:f})}else u.unshift.apply(u,i.children)}}return o},Ke=function(e){return!isNaN(parseInt(e.getAttribute("tabindex"),10))},He=function(e){if(!e)throw new Error("No node provided");return e.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(e.tagName)||function(e){var t,n=null==e||null===(t=e.getAttribute)||void 0===t?void 0:t.call(e,"contenteditable");return""===n||"true"===n}(e))&&!Ke(e)?0:e.tabIndex},qe=function(e,t){return e.tabIndex===t.tabIndex?e.documentOrder-t.documentOrder:e.tabIndex-t.tabIndex},_e=function(e){return"INPUT"===e.tagName},We=function(e){return function(e){return _e(e)&&"radio"===e.type}(e)&&!function(e){if(!e.name)return!0;var t,n=e.form||De(e),r=function(e){return n.querySelectorAll('input[type="radio"][name="'+e+'"]')};if("undefined"!=typeof window&&void 0!==window.CSS&&"function"==typeof window.CSS.escape)t=r(window.CSS.escape(e.name));else try{t=r(e.name)}catch(e){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",e.message),!1}var o=function(e,t){for(var n=0;n<e.length;n++)if(e[n].checked&&e[n].form===t)return e[n]}(t,e.form);return!o||o===e}(e)},Be=function(e){var t=e.getBoundingClientRect(),n=t.width,r=t.height;return 0===n&&0===r},Ue=function(e,t){var n=t.displayCheck,r=t.getShadowRoot;if("hidden"===getComputedStyle(e).visibility)return!0;var o=Ne.call(e,"details>summary:first-of-type")?e.parentElement:e;if(Ne.call(o,"details:not([open]) *"))return!0;if(n&&"full"!==n&&"legacy-full"!==n){if("non-zero-area"===n)return Be(e)}else{if("function"==typeof r){for(var u=e;e;){var i=e.parentElement,c=De(e);if(i&&!i.shadowRoot&&!0===r(i))return Be(e);e=e.assignedSlot?e.assignedSlot:i||c===e.ownerDocument?i:c.host}e=u}if(function(e){var t,n,r,o,u=e&&De(e),i=null===(t=u)||void 0===t?void 0:t.host,c=!1;if(u&&u!==e)for(c=!!(null!==(n=i)&&void 0!==n&&null!==(r=n.ownerDocument)&&void 0!==r&&r.contains(i)||null!=e&&null!==(o=e.ownerDocument)&&void 0!==o&&o.contains(e));!c&&i;){var l,s,a;c=!(null===(s=i=null===(l=u=De(i))||void 0===l?void 0:l.host)||void 0===s||null===(a=s.ownerDocument)||void 0===a||!a.contains(i))}return c}(e))return!e.getClientRects().length;if("legacy-full"!==n)return!0}return!1},ze=function(e,t){return!(t.disabled||Fe(t)||function(e){return _e(e)&&"hidden"===e.type}(t)||Ue(t,e)||function(e){return"DETAILS"===e.tagName&&Array.prototype.slice.apply(e.children).some((function(e){return"SUMMARY"===e.tagName}))}(t)||function(e){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(e.tagName))for(var t=e.parentElement;t;){if("FIELDSET"===t.tagName&&t.disabled){for(var n=0;n<t.children.length;n++){var r=t.children.item(n);if("LEGEND"===r.tagName)return!!Ne.call(t,"fieldset[disabled] *")||!r.contains(e)}return!0}t=t.parentElement}return!1}(t))},Xe=function(e,t){return!(We(t)||He(t)<0||!ze(e,t))},Ye=function(e){var t=parseInt(e.getAttribute("tabindex"),10);return!!(isNaN(t)||t>=0)},Ve=function e(t){var n=[],r=[];return t.forEach((function(t,o){var u=!!t.scopeParent,i=u?t.scopeParent:t,c=function(e,t){var n=He(e);return n<0&&t&&!Ke(e)?0:n}(i,u),l=u?e(t.candidates):i;0===c?u?n.push.apply(n,l):n.push(i):r.push({documentOrder:o,tabIndex:c,item:t,isScope:u,content:l})})),r.sort(qe).reduce((function(e,t){return t.isScope?e.push.apply(e,t.content):e.push(t.content),e}),[]).concat(n)},Ge=function(e,t){var n;return n=(t=t||{}).getShadowRoot?je([e],t.includeContainer,{filter:Xe.bind(null,t),flatten:!1,getShadowRoot:t.getShadowRoot,shadowRootFilter:Ye}):function(e,t,n){if(Fe(e))return[];var r=Array.prototype.slice.apply(e.querySelectorAll(Le));return t&&Ne.call(e,Le)&&r.unshift(e),r.filter(n)}(e,t.includeContainer,Xe.bind(null,t)),Ve(n)},Ze=function(e,t){if(t=t||{},!e)throw new Error("No node provided");return!1!==Ne.call(e,Le)&&Xe(t,e)};let $e=0;function Qe(e,t){void 0===t&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:o=!1}=t;r&&cancelAnimationFrame($e);const u=()=>null==e?void 0:e.focus({preventScroll:n});o?u():$e=requestAnimationFrame(u)}function Je(e,t){let n=e.filter((e=>{var n;return e.parentId===t&&(null==(n=e.context)?void 0:n.open)})),r=n;for(;r.length;)r=e.filter((e=>{var t;return null==(t=r)?void 0:t.some((t=>{var n;return e.parentId===t.id&&(null==(n=e.context)?void 0:n.open)}))})),n=n.concat(r);return n}let et=new WeakMap,tt=new WeakSet,nt={},rt=0;const ot=e=>e&&(e.host||ot(e.parentNode)),ut=(e,t)=>t.map((t=>{if(e.contains(t))return t;const n=ot(t);return e.contains(n)?n:null})).filter((e=>null!=e));function it(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);const r=P(e[0]).body;return function(e,t,n,r){const o="data-floating-ui-inert",u=r?"inert":n?"aria-hidden":null,i=ut(t,e),c=new Set,l=new Set(i),s=[];nt[o]||(nt[o]=new WeakMap);const a=nt[o];return i.forEach((function e(t){t&&!c.has(t)&&(c.add(t),t.parentNode&&e(t.parentNode))})),function e(t){t&&!l.has(t)&&[].forEach.call(t.children,(t=>{if("script"!==d(t))if(c.has(t))e(t);else{const e=u?t.getAttribute(u):null,n=null!==e&&"false"!==e,r=(et.get(t)||0)+1,i=(a.get(t)||0)+1;et.set(t,r),a.set(t,i),s.push(t),1===r&&n&&tt.add(t),1===i&&t.setAttribute(o,""),!n&&u&&t.setAttribute(u,"true")}}))}(t),c.clear(),rt++,()=>{s.forEach((e=>{const t=(et.get(e)||0)-1,n=(a.get(e)||0)-1;et.set(e,t),a.set(e,n),t||(!tt.has(e)&&u&&e.removeAttribute(u),tt.delete(e)),n||e.removeAttribute(o)})),rt--,rt||(et=new WeakMap,et=new WeakMap,tt=new WeakSet,nt={})}}(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}const ct=()=>({getShadowRoot:!0,displayCheck:"function"==typeof ResizeObserver&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function lt(e,t){const n=Ge(e,ct());"prev"===t&&n.reverse();const r=n.indexOf(E(P(e)));return n.slice(r+1)[0]}function st(){return lt(document.body,"next")}function at(){return lt(document.body,"prev")}function ft(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!R(n,r)}function dt(e){Ge(e,ct()).forEach((e=>{e.dataset.tabindex=e.getAttribute("tabindex")||"",e.setAttribute("tabindex","-1")}))}function mt(e){e.querySelectorAll("[data-tabindex]").forEach((e=>{const t=e.dataset.tabindex;delete e.dataset.tabindex,t?e.setAttribute("tabindex",t):e.removeAttribute("tabindex")}))}const vt={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0};function pt(e){"Tab"===e.key&&(e.target,clearTimeout(undefined))}const gt=u.forwardRef((function(e,t){const[n,r]=u.useState();ne((()=>(C()&&r("button"),document.addEventListener("keydown",pt),()=>{document.removeEventListener("keydown",pt)})),[]);const o={ref:t,tabIndex:0,role:n,"aria-hidden":!n||void 0,[ke("focus-guard")]:"",style:vt};return u.createElement("span",ve({},e,o))})),ht=u.createContext(null),yt=ke("portal");function bt(e){void 0===e&&(e={});const{id:t,root:n}=e,r=ye(),o=wt(),[i,c]=u.useState(null),l=u.useRef(null);return ne((()=>()=>{null==i||i.remove(),queueMicrotask((()=>{l.current=null}))}),[i]),ne((()=>{if(!r)return;if(l.current)return;const e=t?document.getElementById(t):null;if(!e)return;const n=document.createElement("div");n.id=r,n.setAttribute(yt,""),e.appendChild(n),l.current=n,c(n)}),[t,r]),ne((()=>{if(null===n)return;if(!r)return;if(l.current)return;let e=n||(null==o?void 0:o.portalNode);e&&!p(e)&&(e=e.current),e=e||document.body;let u=null;t&&(u=document.createElement("div"),u.id=t,e.appendChild(u));const i=document.createElement("div");i.id=r,i.setAttribute(yt,""),e=u||e,e.appendChild(i),l.current=i,c(i)}),[t,n,r,o]),i}const wt=()=>u.useContext(ht),Et="data-floating-ui-focusable";function Rt(e){return e?e.hasAttribute(Et)?e:e.querySelector("["+Et+"]")||e:null}const xt=20;let It=[];function kt(e){It=It.filter((e=>e.isConnected));let t=e;if(t&&"body"!==d(t)){if(!Ze(t,ct())){const e=Ge(t,ct())[0];e&&(t=e)}It.push(t),It.length>xt&&(It=It.slice(-xt))}}function Mt(){return It.slice().reverse().find((e=>e.isConnected))}const Ct=u.forwardRef((function(e,t){return u.createElement("button",ve({},e,{type:"button",ref:t,tabIndex:-1,style:vt}))}));let Ot=0;let Tt=()=>{};const St=u.forwardRef((function(e,t){const{lockScroll:n=!1,...r}=e;return ne((()=>{if(n)return Ot++,1===Ot&&(Tt=function(){const e=/iP(hone|ad|od)|iOS/.test(x()),t=document.body.style,n=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",r=window.innerWidth-document.documentElement.clientWidth,o=t.left?parseFloat(t.left):window.scrollX,u=t.top?parseFloat(t.top):window.scrollY;if(t.overflow="hidden",r&&(t[n]=r+"px"),e){var i,c;const e=(null==(i=window.visualViewport)?void 0:i.offsetLeft)||0,n=(null==(c=window.visualViewport)?void 0:c.offsetTop)||0;Object.assign(t,{position:"fixed",top:-(u-Math.floor(n))+"px",left:-(o-Math.floor(e))+"px",right:"0"})}return()=>{Object.assign(t,{overflow:"",[n]:""}),e&&(Object.assign(t,{position:"",top:"",left:"",right:""}),window.scrollTo(o,u))}}()),()=>{Ot--,0===Ot&&Tt()}}),[n]),u.createElement("div",ve({ref:t},r,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...r.style}}))}));function Pt(e){return g(e.target)&&"BUTTON"===e.target.tagName}function Lt(e){return D(e)}function At(e){return null!=e&&null!=e.clientX}const Nt={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},Dt={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},Ft=e=>{var t,n;return{escapeKey:"boolean"==typeof e?e:null!=(t=null==e?void 0:e.escapeKey)&&t,outsidePress:"boolean"==typeof e?e:null==(n=null==e?void 0:e.outsidePress)||n}};function jt(e){const{open:t=!1,onOpenChange:n,elements:r}=e,o=ye(),i=u.useRef({}),[c]=u.useState((()=>we())),l=null!=xe(),[s,f]=u.useState(r.reference),d=a(((e,t,r)=>{i.current.openEvent=e?t:void 0,c.emit("openchange",{open:e,event:t,reason:r,nested:l}),null==n||n(e,t,r)})),m=u.useMemo((()=>({setPositionReference:f})),[]),v=u.useMemo((()=>({reference:s||r.reference||null,floating:r.floating||null,domReference:r.reference})),[s,r.reference,r.floating]);return u.useMemo((()=>({dataRef:i,open:t,onOpenChange:d,elements:v,events:c,floatingId:o,refs:m})),[t,d,v,c,o,m])}const Kt="active",Ht="selected";function qt(e,t,n){const r=new Map,o="item"===n;let u=e;if(o&&e){const{[Kt]:t,[Ht]:n,...r}=e;u=r}return{..."floating"===n&&{tabIndex:-1,[Et]:""},...u,...t.map((t=>{const r=t?t[n]:null;return"function"==typeof r?e?r(e):null:r})).concat(e).reduce(((e,t)=>t?(Object.entries(t).forEach((t=>{let[n,u]=t;var i;o&&[Kt,Ht].includes(n)||(0===n.indexOf("on")?(r.has(n)||r.set(n,[]),"function"==typeof u&&(null==(i=r.get(n))||i.push(u),e[n]=function(){for(var e,t=arguments.length,o=new Array(t),u=0;u<t;u++)o[u]=arguments[u];return null==(e=r.get(n))?void 0:e.map((e=>e(...o))).find((e=>void 0!==e))})):e[n]=u)})),e):e),{})}}let _t=!1;function Wt(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function Bt(e,t){return Wt(t,e===W||e===B,e===U||e===z)}function Ut(e,t,n){return Wt(t,e===B,n?e===U:e===z)||"Enter"===e||" "===e||""===e}function zt(e,t,n){return Wt(t,n?e===z:e===U,e===W)}const Xt=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);const Yt=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,((e,t)=>(t?"-":"")+e.toLowerCase()));function Vt(e,t){return"function"==typeof e?e(t):e}function Gt(e,t){void 0===t&&(t={});const{open:n,elements:{floating:r}}=e,{duration:o=250}=t,i=("number"==typeof o?o:o.close)||0,[c,l]=u.useState("unmounted"),s=function(e,t){const[n,r]=u.useState(e);return e&&!n&&r(!0),u.useEffect((()=>{if(!e&&n){const e=setTimeout((()=>r(!1)),t);return()=>clearTimeout(e)}}),[e,n,t]),n}(n,i);return s||"close"!==c||l("unmounted"),ne((()=>{if(r){if(n){l("initial");const e=requestAnimationFrame((()=>{l("open")}));return()=>{cancelAnimationFrame(e)}}l("close")}}),[n,r]),{isMounted:s,status:c}}function Zt(e,t){return{...e,rects:{...e.rects,floating:{...e.rects.floating,height:t}}}}function $t(e,t){const[n,r]=e;let o=!1;const u=t.length;for(let e=0,i=u-1;e<u;i=e++){const[u,c]=t[e]||[0,0],[l,s]=t[i]||[0,0];c>=r!=s>=r&&n<=(l-u)*(r-c)/(s-c)+u&&(o=!o)}return o}Object.defineProperty(e,"arrow",{enumerable:!0,get:function(){return r.arrow}}),Object.defineProperty(e,"autoPlacement",{enumerable:!0,get:function(){return r.autoPlacement}}),Object.defineProperty(e,"autoUpdate",{enumerable:!0,get:function(){return r.autoUpdate}}),Object.defineProperty(e,"computePosition",{enumerable:!0,get:function(){return r.computePosition}}),Object.defineProperty(e,"detectOverflow",{enumerable:!0,get:function(){return r.detectOverflow}}),Object.defineProperty(e,"flip",{enumerable:!0,get:function(){return r.flip}}),Object.defineProperty(e,"getOverflowAncestors",{enumerable:!0,get:function(){return r.getOverflowAncestors}}),Object.defineProperty(e,"hide",{enumerable:!0,get:function(){return r.hide}}),Object.defineProperty(e,"inline",{enumerable:!0,get:function(){return r.inline}}),Object.defineProperty(e,"limitShift",{enumerable:!0,get:function(){return r.limitShift}}),Object.defineProperty(e,"offset",{enumerable:!0,get:function(){return r.offset}}),Object.defineProperty(e,"platform",{enumerable:!0,get:function(){return r.platform}}),Object.defineProperty(e,"shift",{enumerable:!0,get:function(){return r.shift}}),Object.defineProperty(e,"size",{enumerable:!0,get:function(){return r.size}}),e.Composite=de,e.CompositeItem=me,e.FloatingArrow=be,e.FloatingDelayGroup=function(e){const{children:t,delay:n,timeoutMs:r=0}=e,[o,i]=u.useReducer(((e,t)=>({...e,...t})),{delay:n,timeoutMs:r,initialDelay:n,currentId:null,isInstantPhase:!1}),c=u.useRef(null),l=u.useCallback((e=>{i({currentId:e})}),[]);return ne((()=>{o.currentId?null===c.current?c.current=o.currentId:o.isInstantPhase||i({isInstantPhase:!0}):(o.isInstantPhase&&i({isInstantPhase:!1}),c.current=null)}),[o.currentId,o.isInstantPhase]),u.createElement(Se.Provider,{value:u.useMemo((()=>({...o,setState:i,setCurrentId:l})),[o,l])},t)},e.FloatingFocusManager=function(e){const{context:t,children:n,disabled:r=!1,order:o=["content"],guards:i=!0,initialFocus:c=0,returnFocus:l=!0,restoreFocus:s=!1,modal:f=!0,visuallyHiddenDismiss:d=!1,closeOnFocusOut:m=!0}=e,{open:v,refs:p,nodeId:h,onOpenChange:y,events:b,dataRef:w,floatingId:x,elements:{domReference:I,floating:C}}=t,O="number"==typeof c&&c<0,T=j(I)&&O,S="undefined"==typeof HTMLElement||!("inert"in HTMLElement.prototype)||i,L=Me(o),N=Me(c),D=Me(l),K=Ie(),H=wt(),q=u.useRef(null),_=u.useRef(null),W=u.useRef(!1),B=u.useRef(!1),U=u.useRef(-1),z=null!=H,X=Rt(C),Y=a((function(e){return void 0===e&&(e=X),e?Ge(e,ct()):[]})),V=a((e=>{const t=Y(e);return L.current.map((e=>I&&"reference"===e?I:X&&"floating"===e?X:t)).filter(Boolean).flat()}));function G(e){return!r&&d&&f?u.createElement(Ct,{ref:"start"===e?q:_,onClick:e=>y(!1,e.nativeEvent)},"string"==typeof d?d:"Dismiss"):null}u.useEffect((()=>{if(r)return;if(!f)return;function e(e){if("Tab"===e.key){R(X,E(P(X)))&&0===Y().length&&!T&&F(e);const t=V(),n=A(e);"reference"===L.current[0]&&n===I&&(F(e),e.shiftKey?Qe(t[t.length-1]):Qe(t[1])),"floating"===L.current[1]&&n===X&&e.shiftKey&&(F(e),Qe(t[0]))}}const t=P(X);return t.addEventListener("keydown",e),()=>{t.removeEventListener("keydown",e)}}),[r,I,X,f,L,T,Y,V]),u.useEffect((()=>{if(!r&&C)return C.addEventListener("focusin",e),()=>{C.removeEventListener("focusin",e)};function e(e){const t=A(e),n=Y().indexOf(t);-1!==n&&(U.current=n)}}),[r,C,Y]),u.useEffect((()=>{if(!r&&m)return C&&g(I)?(I.addEventListener("focusout",t),I.addEventListener("pointerdown",e),C.addEventListener("focusout",t),()=>{I.removeEventListener("focusout",t),I.removeEventListener("pointerdown",e),C.removeEventListener("focusout",t)}):void 0;function e(){B.current=!0,setTimeout((()=>{B.current=!1}))}function t(e){const t=e.relatedTarget;queueMicrotask((()=>{const n=!(R(I,t)||R(C,t)||R(t,C)||R(null==H?void 0:H.portalNode,t)||null!=t&&t.hasAttribute(ke("focus-guard"))||K&&(Je(K.nodesRef.current,h).find((e=>{var n,r;return R(null==(n=e.context)?void 0:n.elements.floating,t)||R(null==(r=e.context)?void 0:r.elements.domReference,t)}))||function(e,t){var n;let r=[],o=null==(n=e.find((e=>e.id===t)))?void 0:n.parentId;for(;o;){const t=e.find((e=>e.id===o));o=null==t?void 0:t.parentId,t&&(r=r.concat(t))}return r}(K.nodesRef.current,h).find((e=>{var n,r;return(null==(n=e.context)?void 0:n.elements.floating)===t||(null==(r=e.context)?void 0:r.elements.domReference)===t}))));if(s&&n&&E(P(X))===P(X).body){g(X)&&X.focus();const e=U.current,t=Y(),n=t[e]||t[t.length-1]||X;g(n)&&n.focus()}!T&&f||!t||!n||B.current||t===Mt()||(W.current=!0,y(!1,e,"focus-out"))}))}}),[r,I,C,X,f,h,K,H,y,m,s,Y,T]),u.useEffect((()=>{var e;if(r)return;const t=Array.from((null==H||null==(e=H.portalNode)?void 0:e.querySelectorAll("["+ke("portal")+"]"))||[]);if(C){const e=[C,...t,q.current,_.current,L.current.includes("reference")||T?I:null].filter((e=>null!=e)),n=f||T?it(e,S,!S):it(e);return()=>{n()}}}),[r,I,C,f,L,H,T,S]),ne((()=>{if(r||!g(X))return;const e=E(P(X));queueMicrotask((()=>{const t=V(X),n=N.current,r=("number"==typeof n?t[n]:n.current)||X,o=R(X,e);O||o||!v||Qe(r,{preventScroll:r===X})}))}),[r,v,X,O,V,N]),ne((()=>{if(r||!X)return;let e=!1;const t=P(X),n=E(t);let o=w.current.openEvent;function u(t){let{open:n,reason:r,event:u,nested:i}=t;n&&(o=u),"escape-key"===r&&p.domReference.current&&kt(p.domReference.current),"hover"===r&&"mouseleave"===u.type&&(W.current=!0),"outside-press"===r&&(i?(W.current=!1,e=!0):W.current=!(k(u)||M(u)))}kt(n),b.on("openchange",u);const i=t.createElement("span");return i.setAttribute("tabindex","-1"),i.setAttribute("aria-hidden","true"),Object.assign(i.style,vt),z&&I&&I.insertAdjacentElement("afterend",i),()=>{b.off("openchange",u);const n=E(t),r=R(C,n)||K&&Je(K.nodesRef.current,h).some((e=>{var t;return R(null==(t=e.context)?void 0:t.elements.floating,n)}));(r||o&&["click","mousedown"].includes(o.type))&&p.domReference.current&&kt(p.domReference.current);const c="boolean"==typeof D.current?Mt()||i:D.current.current||i;queueMicrotask((()=>{D.current&&!W.current&&g(c)&&(c===n||n===t.body||r)&&c.focus({preventScroll:e}),i.remove()}))}}),[r,C,X,D,w,p,b,K,h,z,I]),u.useEffect((()=>{queueMicrotask((()=>{W.current=!1}))}),[r]),ne((()=>{if(!r&&H)return H.setFocusManagerState({modal:f,closeOnFocusOut:m,open:v,onOpenChange:y,refs:p}),()=>{H.setFocusManagerState(null)}}),[r,H,f,v,y,p,m]),ne((()=>{if(r)return;if(!X)return;if("function"!=typeof MutationObserver)return;if(O)return;const e=()=>{const e=X.getAttribute("tabindex"),t=Y(),n=E(P(C)),r=t.indexOf(n);-1!==r&&(U.current=r),L.current.includes("floating")||n!==p.domReference.current&&0===t.length?"0"!==e&&X.setAttribute("tabindex","0"):"-1"!==e&&X.setAttribute("tabindex","-1")};e();const t=new MutationObserver(e);return t.observe(X,{childList:!0,subtree:!0,attributes:!0}),()=>{t.disconnect()}}),[r,C,X,p,L,Y,O]);const Z=!r&&S&&(!f||!T)&&(z||f);return u.createElement(u.Fragment,null,Z&&u.createElement(gt,{"data-type":"inside",ref:null==H?void 0:H.beforeInsideRef,onFocus:e=>{if(f){const e=V();Qe("reference"===o[0]?e[0]:e[e.length-1])}else if(null!=H&&H.preserveTabOrder&&H.portalNode)if(W.current=!1,ft(e,H.portalNode)){const e=st()||I;null==e||e.focus()}else{var t;null==(t=H.beforeOutsideRef.current)||t.focus()}}}),!T&&G("start"),n,G("end"),Z&&u.createElement(gt,{"data-type":"inside",ref:null==H?void 0:H.afterInsideRef,onFocus:e=>{if(f)Qe(V()[0]);else if(null!=H&&H.preserveTabOrder&&H.portalNode)if(m&&(W.current=!0),ft(e,H.portalNode)){const e=at()||I;null==e||e.focus()}else{var t;null==(t=H.afterOutsideRef.current)||t.focus()}}}))},e.FloatingList=ue,e.FloatingNode=function(e){const{children:t,id:n}=e,r=xe();return u.createElement(Ee.Provider,{value:u.useMemo((()=>({id:n,parentId:r})),[n,r])},t)},e.FloatingOverlay=St,e.FloatingPortal=function(e){const{children:t,id:n,root:r,preserveTabOrder:o=!0}=e,c=bt({id:n,root:r}),[l,s]=u.useState(null),a=u.useRef(null),f=u.useRef(null),d=u.useRef(null),m=u.useRef(null),v=null==l?void 0:l.modal,p=null==l?void 0:l.open,g=!!l&&!l.modal&&l.open&&o&&!(!r&&!c);return u.useEffect((()=>{if(c&&o&&!v)return c.addEventListener("focusin",e,!0),c.addEventListener("focusout",e,!0),()=>{c.removeEventListener("focusin",e,!0),c.removeEventListener("focusout",e,!0)};function e(e){if(c&&ft(e)){("focusin"===e.type?mt:dt)(c)}}}),[c,o,v]),u.useEffect((()=>{c&&(p||mt(c))}),[p,c]),u.createElement(ht.Provider,{value:u.useMemo((()=>({preserveTabOrder:o,beforeOutsideRef:a,afterOutsideRef:f,beforeInsideRef:d,afterInsideRef:m,portalNode:c,setFocusManagerState:s})),[o,c])},g&&c&&u.createElement(gt,{"data-type":"outside",ref:a,onFocus:e=>{if(ft(e,c)){var t;null==(t=d.current)||t.focus()}else{const e=at()||(null==l?void 0:l.refs.domReference.current);null==e||e.focus()}}}),g&&c&&u.createElement("span",{"aria-owns":c.id,style:vt}),c&&i.createPortal(t,c),g&&c&&u.createElement(gt,{"data-type":"outside",ref:f,onFocus:e=>{if(ft(e,c)){var t;null==(t=m.current)||t.focus()}else{const t=st()||(null==l?void 0:l.refs.domReference.current);null==t||t.focus(),(null==l?void 0:l.closeOnFocusOut)&&(null==l||l.onOpenChange(!1,e.nativeEvent,"focus-out"))}}}))},e.FloatingTree=function(e){const{children:t}=e,n=u.useRef([]),r=u.useCallback((e=>{n.current=[...n.current,e]}),[]),o=u.useCallback((e=>{n.current=n.current.filter((t=>t!==e))}),[]),i=u.useState((()=>we()))[0];return u.createElement(Re.Provider,{value:u.useMemo((()=>({nodesRef:n,addNode:r,removeNode:o,events:i})),[r,o,i])},t)},e.inner=e=>({name:"inner",options:e,async fn(t){const{listRef:n,overflowRef:o,onFallbackChange:u,offset:c=0,index:l=0,minItemsVisible:s=4,referenceOverflowThreshold:a=0,scrollRef:f,...d}=(v=t,"function"==typeof(m=e)?m(v):m);var m,v;const{rects:p,elements:{floating:g}}=t,h=n.current[l],y=(null==f?void 0:f.current)||g,b=g.clientTop||y.clientTop,w=0!==g.clientTop,E=0!==y.clientTop,R=g===y;if(!h)return{};const x={...t,...await r.offset(-h.offsetTop-g.clientTop-p.reference.height/2-h.offsetHeight/2-c).fn(t)},I=await r.detectOverflow(Zt(x,y.scrollHeight+b+g.clientTop),d),k=await r.detectOverflow(x,{...d,elementContext:"reference"}),M=H(0,I.top),C=x.y+M,O=(y.scrollHeight>y.clientHeight?e=>e:q)(H(0,y.scrollHeight+(w&&R||E?2*b:0)-M-H(0,I.bottom)));if(y.style.maxHeight=O+"px",y.scrollTop=M,u){const e=y.offsetHeight<h.offsetHeight*K(s,n.current.length)-1||k.top>=-a||k.bottom>=-a;i.flushSync((()=>u(e)))}return o&&(o.current=await r.detectOverflow(Zt({...x,y:C},y.offsetHeight+b+g.clientTop),d)),{y:C}}}),e.safePolygon=function(e){void 0===e&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let o,u=!1,i=null,c=null,l=performance.now();const s=e=>{let{x:n,y:s,placement:a,elements:f,onClose:d,nodeId:m,tree:v}=e;return function(e){function g(){clearTimeout(o),d()}if(clearTimeout(o),!f.domReference||!f.floating||null==a||null==n||null==s)return;const{clientX:h,clientY:y}=e,b=[h,y],w=A(e),E="mouseleave"===e.type,x=R(f.floating,w),I=R(f.domReference,w),k=f.domReference.getBoundingClientRect(),M=f.floating.getBoundingClientRect(),C=a.split("-")[0],O=n>M.right-M.width/2,T=s>M.bottom-M.height/2,S=function(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}(b,k),P=M.width>k.width,L=M.height>k.height,N=(P?k:M).left,D=(P?k:M).right,F=(L?k:M).top,j=(L?k:M).bottom;if(x&&(u=!0,!E))return;if(I&&(u=!1),I&&!E)return void(u=!0);if(E&&p(e.relatedTarget)&&R(f.floating,e.relatedTarget))return;if(v&&Je(v.nodesRef.current,m).some((e=>{let{context:t}=e;return null==t?void 0:t.open})))return;if("top"===C&&s>=k.bottom-1||"bottom"===C&&s<=k.top+1||"left"===C&&n>=k.right-1||"right"===C&&n<=k.left+1)return g();let K=[];switch(C){case"top":K=[[N,k.top+1],[N,M.bottom-1],[D,M.bottom-1],[D,k.top+1]];break;case"bottom":K=[[N,M.top+1],[N,k.bottom-1],[D,k.bottom-1],[D,M.top+1]];break;case"left":K=[[M.right-1,j],[M.right-1,F],[k.left+1,F],[k.left+1,j]];break;case"right":K=[[k.right-1,j],[k.right-1,F],[M.left+1,F],[M.left+1,j]]}if(!$t([h,y],K)){if(u&&!S)return g();if(!E&&r){const t=function(e,t){const n=performance.now(),r=n-l;if(null===i||null===c||0===r)return i=e,c=t,l=n,null;const o=e-i,u=t-c,s=Math.sqrt(o*o+u*u);return i=e,c=t,l=n,s/r}(e.clientX,e.clientY);if(null!==t&&t<.1)return g()}$t([h,y],function(e){let[n,r]=e;switch(C){case"top":return[[P?n+t/2:O?n+4*t:n-4*t,r+t+1],[P?n-t/2:O?n+4*t:n-4*t,r+t+1],...[[M.left,O||P?M.bottom-t:M.top],[M.right,O?P?M.bottom-t:M.top:M.bottom-t]]];case"bottom":return[[P?n+t/2:O?n+4*t:n-4*t,r-t],[P?n-t/2:O?n+4*t:n-4*t,r-t],...[[M.left,O||P?M.top+t:M.bottom],[M.right,O?P?M.top+t:M.bottom:M.top+t]]];case"left":{const e=[n+t+1,L?r+t/2:T?r+4*t:r-4*t],o=[n+t+1,L?r-t/2:T?r+4*t:r-4*t];return[...[[T||L?M.right-t:M.left,M.top],[T?L?M.right-t:M.left:M.right-t,M.bottom]],e,o]}case"right":return[[n-t,L?r+t/2:T?r+4*t:r-4*t],[n-t,L?r-t/2:T?r+4*t:r-4*t],...[[T||L?M.left+t:M.right,M.top],[T?L?M.left+t:M.right:M.left+t,M.bottom]]]}}([n,s]))?!u&&r&&(o=window.setTimeout(g,40)):g()}}};return s.__options={blockPointerEvents:n},s},e.useClick=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,elements:{domReference:i}}=e,{enabled:c=!0,event:l="click",toggle:s=!0,ignoreMouse:a=!1,keyboardHandlers:f=!0,stickIfOpen:d=!0}=t,m=u.useRef(),v=u.useRef(!1),p=u.useMemo((()=>({onPointerDown(e){m.current=e.pointerType},onMouseDown(e){const t=m.current;0===e.button&&"click"!==l&&(S(t,!0)&&a||(!n||!s||o.current.openEvent&&d&&"mousedown"!==o.current.openEvent.type?(e.preventDefault(),r(!0,e.nativeEvent,"click")):r(!1,e.nativeEvent,"click")))},onClick(e){const t=m.current;"mousedown"===l&&m.current?m.current=void 0:S(t,!0)&&a||(!n||!s||o.current.openEvent&&d&&"click"!==o.current.openEvent.type?r(!0,e.nativeEvent,"click"):r(!1,e.nativeEvent,"click"))},onKeyDown(e){m.current=void 0,e.defaultPrevented||!f||Pt(e)||(" "!==e.key||Lt(i)||(e.preventDefault(),v.current=!0),"Enter"===e.key&&r(!n||!s,e.nativeEvent,"click"))},onKeyUp(e){e.defaultPrevented||!f||Pt(e)||Lt(i)||" "===e.key&&v.current&&(v.current=!1,r(!n||!s,e.nativeEvent,"click"))}})),[o,i,l,a,f,r,n,d,s]);return u.useMemo((()=>c?{reference:p}:{}),[c,p])},e.useClientPoint=function(e,t){void 0===t&&(t={});const{open:n,dataRef:r,elements:{floating:o,domReference:i},refs:c}=e,{enabled:l=!0,axis:s="both",x:f=null,y:d=null}=t,v=u.useRef(!1),p=u.useRef(null),[g,h]=u.useState(),[y,b]=u.useState([]),w=a(((e,t)=>{v.current||r.current.openEvent&&!At(r.current.openEvent)||c.setPositionReference(function(e,t){let n=null,r=null,o=!1;return{contextElement:e||void 0,getBoundingClientRect(){var u;const i=(null==e?void 0:e.getBoundingClientRect())||{width:0,height:0,x:0,y:0},c="x"===t.axis||"both"===t.axis,l="y"===t.axis||"both"===t.axis,s=["mouseenter","mousemove"].includes((null==(u=t.dataRef.current.openEvent)?void 0:u.type)||"")&&"touch"!==t.pointerType;let a=i.width,f=i.height,d=i.x,m=i.y;return null==n&&t.x&&c&&(n=i.x-t.x),null==r&&t.y&&l&&(r=i.y-t.y),d-=n||0,m-=r||0,a=0,f=0,!o||s?(a="y"===t.axis?i.width:0,f="x"===t.axis?i.height:0,d=c&&null!=t.x?t.x:d,m=l&&null!=t.y?t.y:m):o&&!s&&(f="x"===t.axis?i.height:f,a="y"===t.axis?i.width:a),o=!0,{width:a,height:f,x:d,y:m,top:m,right:d+a,bottom:m+f,left:d}}}}(i,{x:e,y:t,axis:s,dataRef:r,pointerType:g}))})),E=a((e=>{null==f&&null==d&&(n?p.current||b([]):w(e.clientX,e.clientY))})),x=S(g)?o:n,I=u.useCallback((()=>{if(!x||!l||null!=f||null!=d)return;const e=m(o);function t(n){const r=A(n);R(o,r)?(e.removeEventListener("mousemove",t),p.current=null):w(n.clientX,n.clientY)}if(!r.current.openEvent||At(r.current.openEvent)){e.addEventListener("mousemove",t);const n=()=>{e.removeEventListener("mousemove",t),p.current=null};return p.current=n,n}c.setPositionReference(i)}),[x,l,f,d,o,r,c,i,w]);u.useEffect((()=>I()),[I,y]),u.useEffect((()=>{l&&!o&&(v.current=!1)}),[l,o]),u.useEffect((()=>{!l&&n&&(v.current=!0)}),[l,n]),ne((()=>{!l||null==f&&null==d||(v.current=!1,w(f,d))}),[l,f,d,w]);const k=u.useMemo((()=>{function e(e){let{pointerType:t}=e;h(t)}return{onPointerDown:e,onPointerEnter:e,onMouseMove:E,onMouseEnter:E}}),[E]);return u.useMemo((()=>l?{reference:k}:{}),[l,k])},e.useDelayGroup=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,floatingId:o}=e,{id:u,enabled:i=!0}=t,c=null!=u?u:o,l=Pe(),{currentId:s,setCurrentId:a,initialDelay:f,setState:d,timeoutMs:m}=l;return ne((()=>{i&&s&&(d({delay:{open:1,close:Oe(f,"close")}}),s!==c&&r(!1))}),[i,c,r,d,s,f]),ne((()=>{function e(){r(!1),d({delay:f,currentId:null})}if(i&&s&&!n&&s===c){if(m){const t=window.setTimeout(e,m);return()=>{clearTimeout(t)}}e()}}),[i,n,d,s,c,r,f,m]),ne((()=>{i&&a!==Te&&n&&a(c)}),[i,n,a,c]),l},e.useDelayGroupContext=Pe,e.useDismiss=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:o,elements:i,dataRef:c}=e,{enabled:l=!0,escapeKey:s=!0,outsidePress:f=!0,outsidePressEvent:d="pointerdown",referencePress:m=!1,referencePressEvent:v="pointerdown",ancestorScroll:h=!1,bubbles:E,capture:x}=t,I=Ie(),k=a("function"==typeof f?f:()=>!1),M="function"==typeof f?k:f,C=u.useRef(!1),O=u.useRef(!1),{escapeKey:T,outsidePress:S}=Ft(E),{escapeKey:N,outsidePress:D}=Ft(x),F=u.useRef(!1),j=a((e=>{var t;if(!n||!l||!s||"Escape"!==e.key)return;if(F.current)return;const r=null==(t=c.current.floatingContext)?void 0:t.nodeId,u=I?Je(I.nodesRef.current,r):[];if(!T&&(e.stopPropagation(),u.length>0)){let e=!0;if(u.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__escapeKeyBubbles||(e=!1)})),!e)return}o(!1,function(e){return"nativeEvent"in e}(e)?e.nativeEvent:e,"escape-key")})),K=a((e=>{var t;const n=()=>{var t;j(e),null==(t=A(e))||t.removeEventListener("keydown",n)};null==(t=A(e))||t.addEventListener("keydown",n)})),H=a((e=>{var t;const n=C.current;C.current=!1;const r=O.current;if(O.current=!1,"click"===d&&r)return;if(n)return;if("function"==typeof M&&!M(e))return;const u=A(e),l="["+ke("inert")+"]",s=P(i.floating).querySelectorAll(l);let a=p(u)?u:null;for(;a&&!y(a);){const e=w(a);if(y(e)||!p(e))break;a=e}if(s.length&&p(u)&&!u.matches("html,body")&&!R(u,i.floating)&&Array.from(s).every((e=>!R(a,e))))return;if(g(u)&&W){const t=u.clientWidth>0&&u.scrollWidth>u.clientWidth,n=u.clientHeight>0&&u.scrollHeight>u.clientHeight;let r=n&&e.offsetX>u.clientWidth;if(n){"rtl"===b(u).direction&&(r=e.offsetX<=u.offsetWidth-u.clientWidth)}if(r||t&&e.offsetY>u.clientHeight)return}const f=null==(t=c.current.floatingContext)?void 0:t.nodeId,m=I&&Je(I.nodesRef.current,f).some((t=>{var n;return L(e,null==(n=t.context)?void 0:n.elements.floating)}));if(L(e,i.floating)||L(e,i.domReference)||m)return;const v=I?Je(I.nodesRef.current,f):[];if(v.length>0){let e=!0;if(v.forEach((t=>{var n;null==(n=t.context)||!n.open||t.context.dataRef.current.__outsidePressBubbles||(e=!1)})),!e)return}o(!1,e,"outside-press")})),q=a((e=>{var t;const n=()=>{var t;H(e),null==(t=A(e))||t.removeEventListener(d,n)};null==(t=A(e))||t.addEventListener(d,n)}));u.useEffect((()=>{if(!n||!l)return;c.current.__escapeKeyBubbles=T,c.current.__outsidePressBubbles=S;let e=-1;function t(e){o(!1,e,"ancestor-scroll")}function u(){window.clearTimeout(e),F.current=!0}function a(){e=window.setTimeout((()=>{F.current=!1}),"undefined"!=typeof CSS&&CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")?5:0)}const f=P(i.floating);s&&(f.addEventListener("keydown",N?K:j,N),f.addEventListener("compositionstart",u),f.addEventListener("compositionend",a)),M&&f.addEventListener(d,D?q:H,D);let m=[];return h&&(p(i.domReference)&&(m=r.getOverflowAncestors(i.domReference)),p(i.floating)&&(m=m.concat(r.getOverflowAncestors(i.floating))),!p(i.reference)&&i.reference&&i.reference.contextElement&&(m=m.concat(r.getOverflowAncestors(i.reference.contextElement)))),m=m.filter((e=>{var t;return e!==(null==(t=f.defaultView)?void 0:t.visualViewport)})),m.forEach((e=>{e.addEventListener("scroll",t,{passive:!0})})),()=>{s&&(f.removeEventListener("keydown",N?K:j,N),f.removeEventListener("compositionstart",u),f.removeEventListener("compositionend",a)),M&&f.removeEventListener(d,D?q:H,D),m.forEach((e=>{e.removeEventListener("scroll",t)})),window.clearTimeout(e)}}),[c,i,s,M,d,n,o,h,l,T,S,j,N,K,H,D,q]),u.useEffect((()=>{C.current=!1}),[M,d]);const _=u.useMemo((()=>({onKeyDown:j,[Nt[v]]:e=>{m&&o(!1,e.nativeEvent,"reference-press")}})),[j,o,m,v]),W=u.useMemo((()=>({onKeyDown:j,onMouseDown(){O.current=!0},onMouseUp(){O.current=!0},[Dt[d]]:()=>{C.current=!0}})),[j,d]);return u.useMemo((()=>l?{reference:_,floating:W}:{}),[l,_,W])},e.useFloating=function(e){void 0===e&&(e={});const{nodeId:t}=e,n=jt({...e,elements:{reference:null,floating:null,...e.elements}}),o=e.rootContext||n,i=o.elements,[c,l]=u.useState(null),[s,a]=u.useState(null),f=(null==i?void 0:i.domReference)||c,d=u.useRef(null),m=Ie();ne((()=>{f&&(d.current=f)}),[f]);const v=r.useFloating({...e,elements:{...i,...s&&{reference:s}}}),g=u.useCallback((e=>{const t=p(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),contextElement:e}:e;a(t),v.refs.setReference(t)}),[v.refs]),h=u.useCallback((e=>{(p(e)||null===e)&&(d.current=e,l(e)),(p(v.refs.reference.current)||null===v.refs.reference.current||null!==e&&!p(e))&&v.refs.setReference(e)}),[v.refs]),y=u.useMemo((()=>({...v.refs,setReference:h,setPositionReference:g,domReference:d})),[v.refs,h,g]),b=u.useMemo((()=>({...v.elements,domReference:f})),[v.elements,f]),w=u.useMemo((()=>({...v,...o,refs:y,elements:b,nodeId:t})),[v,y,b,t,o]);return ne((()=>{o.dataRef.current.floatingContext=w;const e=null==m?void 0:m.nodesRef.current.find((e=>e.id===t));e&&(e.context=w)})),u.useMemo((()=>({...v,context:w,refs:y,elements:b})),[v,y,b,w])},e.useFloatingNodeId=function(e){const t=ye(),n=Ie(),r=xe(),o=e||r;return ne((()=>{const e={id:t,parentId:o};return null==n||n.addNode(e),()=>{null==n||n.removeNode(e)}}),[n,t,o]),t},e.useFloatingParentNodeId=xe,e.useFloatingPortalNode=bt,e.useFloatingRootContext=jt,e.useFloatingTree=Ie,e.useFocus=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,events:o,dataRef:i,elements:c}=e,{enabled:l=!0,visibleOnly:s=!0}=t,a=u.useRef(!1),f=u.useRef(),d=u.useRef(!0);u.useEffect((()=>{if(!l)return;const e=m(c.domReference);function t(){!n&&g(c.domReference)&&c.domReference===E(P(c.domReference))&&(a.current=!0)}function r(){d.current=!0}return e.addEventListener("blur",t),e.addEventListener("keydown",r,!0),()=>{e.removeEventListener("blur",t),e.removeEventListener("keydown",r,!0)}}),[c.domReference,n,l]),u.useEffect((()=>{if(l)return o.on("openchange",e),()=>{o.off("openchange",e)};function e(e){let{reason:t}=e;"reference-press"!==t&&"escape-key"!==t||(a.current=!0)}}),[o,l]),u.useEffect((()=>()=>{clearTimeout(f.current)}),[]);const v=u.useMemo((()=>({onPointerDown(e){M(e.nativeEvent)||(d.current=!1)},onMouseLeave(){a.current=!1},onFocus(e){if(a.current)return;const t=A(e.nativeEvent);if(s&&p(t))try{if(C()&&T())throw Error();if(!t.matches(":focus-visible"))return}catch(e){if(!d.current&&!D(t))return}r(!0,e.nativeEvent,"focus")},onBlur(e){a.current=!1;const t=e.relatedTarget,n=e.nativeEvent,o=p(t)&&t.hasAttribute(ke("focus-guard"))&&"outside"===t.getAttribute("data-type");f.current=window.setTimeout((()=>{var e;const u=E(c.domReference?c.domReference.ownerDocument:document);(t||u!==c.domReference)&&(R(null==(e=i.current.floatingContext)?void 0:e.refs.floating.current,u)||R(c.domReference,u)||o||r(!1,n,"focus"))}))}})),[i,c.domReference,r,s]);return u.useMemo((()=>l?{reference:v}:{}),[l,v])},e.useHover=function(e,t){void 0===t&&(t={});const{open:n,onOpenChange:r,dataRef:o,events:i,elements:c}=e,{enabled:l=!0,delay:s=0,handleClose:f=null,mouseOnly:d=!1,restMs:m=0,move:v=!0}=t,g=Ie(),h=xe(),y=Me(f),b=Me(s),w=Me(n),E=u.useRef(),x=u.useRef(-1),I=u.useRef(),k=u.useRef(-1),M=u.useRef(!0),C=u.useRef(!1),O=u.useRef((()=>{})),T=u.useRef(!1),L=u.useCallback((()=>{var e;const t=null==(e=o.current.openEvent)?void 0:e.type;return(null==t?void 0:t.includes("mouse"))&&"mousedown"!==t}),[o]);u.useEffect((()=>{if(l)return i.on("openchange",e),()=>{i.off("openchange",e)};function e(e){let{open:t}=e;t||(clearTimeout(x.current),clearTimeout(k.current),M.current=!0,T.current=!1)}}),[l,i]),u.useEffect((()=>{if(!l)return;if(!y.current)return;if(!n)return;function e(e){L()&&r(!1,e,"hover")}const t=P(c.floating).documentElement;return t.addEventListener("mouseleave",e),()=>{t.removeEventListener("mouseleave",e)}}),[c.floating,n,r,l,y,L]);const A=u.useCallback((function(e,t,n){void 0===t&&(t=!0),void 0===n&&(n="hover");const o=Oe(b.current,"close",E.current);o&&!I.current?(clearTimeout(x.current),x.current=window.setTimeout((()=>r(!1,e,n)),o)):t&&(clearTimeout(x.current),r(!1,e,n))}),[b,r]),N=a((()=>{O.current(),I.current=void 0})),D=a((()=>{if(C.current){const e=P(c.floating).body;e.style.pointerEvents="",e.removeAttribute(Ce),C.current=!1}})),F=a((()=>!!o.current.openEvent&&["click","mousedown"].includes(o.current.openEvent.type)));u.useEffect((()=>{if(l&&p(c.domReference)){var e;const r=c.domReference;return n&&r.addEventListener("mouseleave",i),null==(e=c.floating)||e.addEventListener("mouseleave",i),v&&r.addEventListener("mousemove",t,{once:!0}),r.addEventListener("mouseenter",t),r.addEventListener("mouseleave",u),()=>{var e;n&&r.removeEventListener("mouseleave",i),null==(e=c.floating)||e.removeEventListener("mouseleave",i),v&&r.removeEventListener("mousemove",t),r.removeEventListener("mouseenter",t),r.removeEventListener("mouseleave",u)}}function t(e){if(clearTimeout(x.current),M.current=!1,d&&!S(E.current)||m>0&&!Oe(b.current,"open"))return;const t=Oe(b.current,"open",E.current);t?x.current=window.setTimeout((()=>{w.current||r(!0,e,"hover")}),t):n||r(!0,e,"hover")}function u(e){if(F())return;O.current();const t=P(c.floating);if(clearTimeout(k.current),T.current=!1,y.current&&o.current.floatingContext){n||clearTimeout(x.current),I.current=y.current({...o.current.floatingContext,tree:g,x:e.clientX,y:e.clientY,onClose(){D(),N(),F()||A(e,!0,"safe-polygon")}});const r=I.current;return t.addEventListener("mousemove",r),void(O.current=()=>{t.removeEventListener("mousemove",r)})}("touch"!==E.current||!R(c.floating,e.relatedTarget))&&A(e)}function i(e){F()||o.current.floatingContext&&(null==y.current||y.current({...o.current.floatingContext,tree:g,x:e.clientX,y:e.clientY,onClose(){D(),N(),F()||A(e)}})(e))}}),[c,l,e,d,m,v,A,N,D,r,n,w,g,b,y,o,F]),ne((()=>{var e;if(l&&n&&null!=(e=y.current)&&e.__options.blockPointerEvents&&L()){C.current=!0;const e=c.floating;if(p(c.domReference)&&e){var t;const n=P(c.floating).body;n.setAttribute(Ce,"");const r=c.domReference,o=null==g||null==(t=g.nodesRef.current.find((e=>e.id===h)))||null==(t=t.context)?void 0:t.elements.floating;return o&&(o.style.pointerEvents=""),n.style.pointerEvents="none",r.style.pointerEvents="auto",e.style.pointerEvents="auto",()=>{n.style.pointerEvents="",r.style.pointerEvents="",e.style.pointerEvents=""}}}}),[l,n,h,c,g,y,L]),ne((()=>{n||(E.current=void 0,T.current=!1,N(),D())}),[n,N,D]),u.useEffect((()=>()=>{N(),clearTimeout(x.current),clearTimeout(k.current),D()}),[l,c.domReference,N,D]);const j=u.useMemo((()=>{function e(e){E.current=e.pointerType}return{onPointerDown:e,onPointerEnter:e,onMouseMove(e){const{nativeEvent:t}=e;function o(){M.current||w.current||r(!0,t,"hover")}d&&!S(E.current)||n||0===m||T.current&&e.movementX**2+e.movementY**2<2||(clearTimeout(k.current),"touch"===E.current?o():(T.current=!0,k.current=window.setTimeout(o,m)))}}}),[d,r,n,w,m]),K=u.useMemo((()=>({onMouseEnter(){clearTimeout(x.current)},onMouseLeave(e){F()||A(e.nativeEvent,!1)}})),[A,F]);return u.useMemo((()=>l?{reference:j,floating:K}:{}),[l,j,K])},e.useId=ye,e.useInnerOffset=function(e,t){const{open:n,elements:r}=e,{enabled:o=!0,overflowRef:c,scrollRef:l,onChange:s}=t,f=a(s),d=u.useRef(!1),m=u.useRef(null),v=u.useRef(null);u.useEffect((()=>{if(!o)return;function e(e){if(e.ctrlKey||!t||null==c.current)return;const n=e.deltaY,r=c.current.top>=-.5,o=c.current.bottom>=-.5,u=t.scrollHeight-t.clientHeight,l=n<0?-1:1,s=n<0?"max":"min";t.scrollHeight<=t.clientHeight||(!r&&n>0||!o&&n<0?(e.preventDefault(),i.flushSync((()=>{f((e=>e+Math[s](n,u*l)))}))):/firefox/i.test(I())&&(t.scrollTop+=n))}const t=(null==l?void 0:l.current)||r.floating;return n&&t?(t.addEventListener("wheel",e),requestAnimationFrame((()=>{m.current=t.scrollTop,null!=c.current&&(v.current={...c.current})})),()=>{m.current=null,v.current=null,t.removeEventListener("wheel",e)}):void 0}),[o,n,r.floating,c,l,f]);const p=u.useMemo((()=>({onKeyDown(){d.current=!0},onWheel(){d.current=!1},onPointerMove(){d.current=!1},onScroll(){const e=(null==l?void 0:l.current)||r.floating;if(c.current&&e&&d.current){if(null!==m.current){const t=e.scrollTop-m.current;(c.current.bottom<-.5&&t<-1||c.current.top<-.5&&t>1)&&i.flushSync((()=>f((e=>e+t))))}requestAnimationFrame((()=>{m.current=e.scrollTop}))}}})),[r.floating,f,c,l]);return u.useMemo((()=>o?{floating:p}:{}),[o,p])},e.useInteractions=function(e){void 0===e&&(e=[]);const t=e.map((e=>null==e?void 0:e.reference)),n=e.map((e=>null==e?void 0:e.floating)),r=e.map((e=>null==e?void 0:e.item)),o=u.useCallback((t=>qt(t,e,"reference")),t),i=u.useCallback((t=>qt(t,e,"floating")),n),c=u.useCallback((t=>qt(t,e,"item")),r);return u.useMemo((()=>({getReferenceProps:o,getFloatingProps:i,getItemProps:c})),[o,i,c])},e.useListItem=ie,e.useListNavigation=function(e,t){const{open:n,onOpenChange:r,elements:o}=e,{listRef:i,activeIndex:c,onNavigate:l=(()=>{}),enabled:s=!0,selectedIndex:f=null,allowEscape:d=!1,loop:m=!1,nested:v=!1,rtl:p=!1,virtual:h=!1,focusItemOnOpen:y="auto",focusItemOnHover:b=!0,openOnArrowKeyDown:w=!0,disabledIndices:x,orientation:I="vertical",cols:O=1,scrollItemIntoView:S=!0,virtualItemRef:L,itemSizes:A,dense:N=!1}=t,D=Me(Rt(o.floating)),K=xe(),H=Ie(),q=a(l),_=j(o.domReference),W=u.useRef(y),X=u.useRef(null!=f?f:-1),re=u.useRef(null),oe=u.useRef(!0),ue=u.useRef(q),ie=u.useRef(!!o.floating),ce=u.useRef(n),le=u.useRef(!1),se=u.useRef(!1),ae=Me(x),fe=Me(n),de=Me(S),me=Me(f),[ve,pe]=u.useState(),[ge,he]=u.useState(),ye=a((function(e,t,n){function r(e){h?(pe(e.id),null==H||H.events.emit("virtualfocus",e),L&&(L.current=e)):Qe(e,{preventScroll:!0,sync:!(!T()||!C())&&(_t||le.current)})}void 0===n&&(n=!1);const o=e.current[t.current];o&&r(o),requestAnimationFrame((()=>{const u=e.current[t.current]||o;if(!u)return;o||r(u);const i=de.current;i&&we&&(n||!oe.current)&&(null==u.scrollIntoView||u.scrollIntoView("boolean"==typeof i?{block:"nearest",inline:"nearest"}:i))}))}));ne((()=>{document.createElement("div").focus({get preventScroll(){return _t=!0,!1}})}),[]),ne((()=>{s&&(n&&o.floating?W.current&&null!=f&&(se.current=!0,X.current=f,q(f)):ie.current&&(X.current=-1,ue.current(null)))}),[s,n,o.floating,f,q]),ne((()=>{if(s&&n&&o.floating)if(null==c){if(le.current=!1,null!=me.current)return;if(ie.current&&(X.current=-1,ye(i,X)),(!ce.current||!ie.current)&&W.current&&(null!=re.current||!0===W.current&&null==re.current)){let e=0;const t=()=>{if(null==i.current[0]){if(e<2){(e?requestAnimationFrame:queueMicrotask)(t)}e++}else X.current=null==re.current||Ut(re.current,I,p)||v?V(i,ae.current):G(i,ae.current),re.current=null,q(X.current)};t()}}else Y(i,c)||(X.current=c,ye(i,X,se.current),se.current=!1)}),[s,n,o.floating,c,me,v,i,I,p,q,ye,ae]),ne((()=>{var e;if(!s||o.floating||!H||h||!ie.current)return;const t=H.nodesRef.current,n=null==(e=t.find((e=>e.id===K)))||null==(e=e.context)?void 0:e.elements.floating,r=E(P(o.floating)),u=t.some((e=>e.context&&R(e.context.elements.floating,r)));n&&!u&&oe.current&&n.focus({preventScroll:!0})}),[s,o.floating,H,K,h]),ne((()=>{if(s&&H&&h&&!K)return H.events.on("virtualfocus",e),()=>{H.events.off("virtualfocus",e)};function e(e){he(e.id),L&&(L.current=e)}}),[s,H,h,K,L]),ne((()=>{ue.current=q,ie.current=!!o.floating})),ne((()=>{n||(re.current=null)}),[n]),ne((()=>{ce.current=n}),[n]);const be=null!=c,we=u.useMemo((()=>{function e(e){if(!n)return;const t=i.current.indexOf(e);-1!==t&&q(t)}return{onFocus(t){let{currentTarget:n}=t;e(n)},onClick:e=>{let{currentTarget:t}=e;return t.focus({preventScroll:!0})},...b&&{onMouseMove(t){let{currentTarget:n}=t;e(n)},onPointerLeave(e){let{pointerType:t}=e;oe.current&&"touch"!==t&&(X.current=-1,ye(i,X),q(null),h||Qe(D.current,{preventScroll:!0}))}}}}),[n,D,ye,b,i,q,h]),Ee=a((e=>{if(oe.current=!1,le.current=!0,229===e.which)return;if(!fe.current&&e.currentTarget===D.current)return;if(v&&zt(e.key,I,p))return F(e),r(!1,e.nativeEvent,"list-navigation"),void(g(o.domReference)&&(h?null==H||H.events.emit("virtualfocus",o.domReference):o.domReference.focus()));const t=X.current,u=V(i,x),c=G(i,x);if(_||("Home"===e.key&&(F(e),X.current=u,q(X.current)),"End"===e.key&&(F(e),X.current=c,q(X.current))),O>1){const t=A||Array.from({length:i.current.length},(()=>({width:1,height:1}))),n=Q(t,O,N),r=n.findIndex((e=>null!=e&&!te(i.current,e,x))),o=n.reduce(((e,t,n)=>null==t||te(i.current,t,x)?e:n),-1),l=n[$({current:n.map((e=>null!=e?i.current[e]:null))},{event:e,orientation:I,loop:m,rtl:p,cols:O,disabledIndices:ee([...x||i.current.map(((e,t)=>te(i.current,t)?t:void 0)),void 0],n),minIndex:r,maxIndex:o,prevIndex:J(X.current>c?u:X.current,t,n,O,e.key===B?"bl":e.key===(p?U:z)?"tr":"tl"),stopEvent:!0})];if(null!=l&&(X.current=l,q(X.current)),"both"===I)return}if(Bt(e.key,I)){if(F(e),n&&!h&&E(e.currentTarget.ownerDocument)===e.currentTarget)return X.current=Ut(e.key,I,p)?u:c,void q(X.current);Ut(e.key,I,p)?X.current=m?t>=c?d&&t!==i.current.length?-1:u:Z(i,{startingIndex:t,disabledIndices:x}):Math.min(c,Z(i,{startingIndex:t,disabledIndices:x})):X.current=m?t<=u?d&&-1!==t?i.current.length:c:Z(i,{startingIndex:t,decrement:!0,disabledIndices:x}):Math.max(u,Z(i,{startingIndex:t,decrement:!0,disabledIndices:x})),Y(i,X.current)?q(null):q(X.current)}})),Re=u.useMemo((()=>h&&n&&be&&{"aria-activedescendant":ge||ve}),[h,n,be,ge,ve]),ke=u.useMemo((()=>({"aria-orientation":"both"===I?void 0:I,...!j(o.domReference)&&Re,onKeyDown:Ee,onPointerMove(){oe.current=!0}})),[Re,Ee,o.domReference,I]),Ce=u.useMemo((()=>{function e(e){"auto"===y&&k(e.nativeEvent)&&(W.current=!0)}return{...Re,onKeyDown(e){oe.current=!1;const t=e.key.startsWith("Arrow"),o=["Home","End"].includes(e.key),u=t||o,c=function(e,t,n){return Wt(t,n?e===U:e===z,e===B)}(e.key,I,p),l=zt(e.key,I,p),s=Bt(e.key,I),a=(v?c:s)||"Enter"===e.key||""===e.key.trim();if(h&&n){const t=null==H?void 0:H.nodesRef.current.find((e=>null==e.parentId)),n=H&&t?function(e,t){let n,r=-1;return function t(o,u){u>r&&(n=o,r=u),Je(e,o).forEach((e=>{t(e.id,u+1)}))}(t,0),e.find((e=>e.id===n))}(H.nodesRef.current,t.id):null;if(u&&n&&L){const t=new KeyboardEvent("keydown",{key:e.key,bubbles:!0});if(c||l){var d,m;const r=(null==(d=n.context)?void 0:d.elements.domReference)===e.currentTarget,o=l&&!r?null==(m=n.context)?void 0:m.elements.domReference:c?i.current.find((e=>(null==e?void 0:e.id)===ve)):null;o&&(F(e),o.dispatchEvent(t),he(void 0))}var g;if((s||o)&&n.context)if(n.context.open&&n.parentId&&e.currentTarget!==n.context.elements.domReference)return F(e),void(null==(g=n.context.elements.domReference)||g.dispatchEvent(t))}return Ee(e)}(n||w||!t)&&(a&&(re.current=v&&s?null:e.key),v?c&&(F(e),n?(X.current=V(i,ae.current),q(X.current)):r(!0,e.nativeEvent,"list-navigation")):s&&(null!=f&&(X.current=f),F(e),!n&&w?r(!0,e.nativeEvent,"list-navigation"):Ee(e),n&&q(X.current)))},onFocus(){n&&!h&&q(null)},onPointerDown:function(e){W.current=y,"auto"===y&&M(e.nativeEvent)&&(W.current=!0)},onMouseDown:e,onClick:e}}),[ve,Re,Ee,ae,y,i,v,q,r,n,w,I,p,f,H,h,L]);return u.useMemo((()=>s?{reference:Ce,floating:ke,item:we}:{}),[s,Ce,ke,we])},e.useMergeRefs=c,e.useRole=function(e,t){var n;void 0===t&&(t={});const{open:r,floatingId:o}=e,{enabled:i=!0,role:c="dialog"}=t,l=null!=(n=Xt.get(c))?n:c,s=ye(),a=null!=xe(),f=u.useMemo((()=>"tooltip"===l||"label"===c?{["aria-"+("label"===c?"labelledby":"describedby")]:r?o:void 0}:{"aria-expanded":r?"true":"false","aria-haspopup":"alertdialog"===l?"dialog":l,"aria-controls":r?o:void 0,..."listbox"===l&&{role:"combobox"},..."menu"===l&&{id:s},..."menu"===l&&a&&{role:"menuitem"},..."select"===c&&{"aria-autocomplete":"none"},..."combobox"===c&&{"aria-autocomplete":"list"}}),[l,o,a,r,s,c]),d=u.useMemo((()=>{const e={id:o,...l&&{role:l}};return"tooltip"===l||"label"===c?e:{...e,..."menu"===l&&{"aria-labelledby":s}}}),[l,o,s,c]),m=u.useCallback((e=>{let{active:t,selected:n}=e;const r={role:"option",...t&&{id:o+"-option"}};switch(c){case"select":return{...r,"aria-selected":t&&n};case"combobox":return{...r,...t&&{"aria-selected":!0}}}return{}}),[o,c]);return u.useMemo((()=>i?{reference:f,floating:d,item:m}:{}),[i,f,d,m])},e.useTransitionStatus=Gt,e.useTransitionStyles=function(e,t){void 0===t&&(t={});const{initial:n={opacity:0},open:r,close:o,common:i,duration:c=250}=t,l=e.placement,s=l.split("-")[0],a=u.useMemo((()=>({side:s,placement:l})),[s,l]),f="number"==typeof c,d=(f?c:c.open)||0,m=(f?c:c.close)||0,[v,p]=u.useState((()=>({...Vt(i,a),...Vt(n,a)}))),{isMounted:g,status:h}=Gt(e,{duration:c}),y=Me(n),b=Me(r),w=Me(o),E=Me(i);return ne((()=>{const e=Vt(y.current,a),t=Vt(w.current,a),n=Vt(E.current,a),r=Vt(b.current,a)||Object.keys(e).reduce(((e,t)=>(e[t]="",e)),{});if("initial"===h&&p((t=>({transitionProperty:t.transitionProperty,...n,...e}))),"open"===h&&p({transitionProperty:Object.keys(r).map(Yt).join(","),transitionDuration:d+"ms",...n,...r}),"close"===h){const r=t||e;p({transitionProperty:Object.keys(r).map(Yt).join(","),transitionDuration:m+"ms",...n,...r})}}),[m,w,y,b,E,d,h,a]),{isMounted:g,styles:v}},e.useTypeahead=function(e,t){var n;const{open:r,dataRef:o}=e,{listRef:i,activeIndex:c,onMatch:l,onTypingChange:s,enabled:f=!0,findMatch:d=null,resetMs:m=750,ignoreKeys:v=[],selectedIndex:p=null}=t,g=u.useRef(),h=u.useRef(""),y=u.useRef(null!=(n=null!=p?p:c)?n:-1),b=u.useRef(null),w=a(l),E=a(s),R=Me(d),x=Me(v);ne((()=>{r&&(clearTimeout(g.current),b.current=null,h.current="")}),[r]),ne((()=>{var e;r&&""===h.current&&(y.current=null!=(e=null!=p?p:c)?e:-1)}),[r,p,c]);const I=a((e=>{e?o.current.typing||(o.current.typing=e,E(e)):o.current.typing&&(o.current.typing=e,E(e))})),k=a((e=>{function t(e,t,n){const r=R.current?R.current(t,n):t.find((e=>0===(null==e?void 0:e.toLocaleLowerCase().indexOf(n.toLocaleLowerCase()))));return r?e.indexOf(r):-1}const n=i.current;if(h.current.length>0&&" "!==h.current[0]&&(-1===t(n,n,h.current)?I(!1):" "===e.key&&F(e)),null==n||x.current.includes(e.key)||1!==e.key.length||e.ctrlKey||e.metaKey||e.altKey)return;r&&" "!==e.key&&(F(e),I(!0));n.every((e=>{var t,n;return!e||(null==(t=e[0])?void 0:t.toLocaleLowerCase())!==(null==(n=e[1])?void 0:n.toLocaleLowerCase())}))&&h.current===e.key&&(h.current="",y.current=b.current),h.current+=e.key,clearTimeout(g.current),g.current=setTimeout((()=>{h.current="",y.current=b.current,I(!1)}),m);const o=y.current,u=t(n,[...n.slice((o||0)+1),...n.slice(0,(o||0)+1)],h.current);-1!==u?(w(u),b.current=u):" "!==e.key&&(h.current="",I(!1))})),M=u.useMemo((()=>({onKeyDown:k})),[k]),C=u.useMemo((()=>({onKeyDown:k,onKeyUp(e){" "===e.key&&I(!1)}})),[k,I]);return u.useMemo((()=>f?{reference:M,floating:C}:{}),[f,M,C])}}));
