<!DOCTYPE html>
<html>
<head>
    <title>Debug Token</title>
</head>
<body>
    <h1>Debug Authentication Token</h1>
    <button onclick="checkToken()">Check Token</button>
    <button onclick="testAPI()">Test API</button>
    <div id="output"></div>

    <script>
        function checkToken() {
            const output = document.getElementById('output');
            
            // Check localStorage
            const directToken = localStorage.getItem('accessToken');
            
            // Check Zustand storage
            const authStorage = localStorage.getItem('auth-storage');
            let zustandToken = null;
            if (authStorage) {
                try {
                    const parsed = JSON.parse(authStorage);
                    zustandToken = parsed.state?.accessToken;
                } catch (e) {
                    console.error('Failed to parse auth storage:', e);
                }
            }
            
            output.innerHTML = `
                <h3>Token Check Results:</h3>
                <p><strong>Direct localStorage token:</strong> ${directToken || 'Not found'}</p>
                <p><strong>Zustand storage token:</strong> ${zustandToken || 'Not found'}</p>
                <p><strong>Auth storage raw:</strong> ${authStorage || 'Not found'}</p>
            `;
        }
        
        async function testAPI() {
            const output = document.getElementById('output');
            
            try {
                // Get token from Zustand storage
                const authStorage = localStorage.getItem('auth-storage');
                let token = null;
                if (authStorage) {
                    const parsed = JSON.parse(authStorage);
                    token = parsed.state?.accessToken;
                }
                
                if (!token) {
                    output.innerHTML += '<p><strong>Error:</strong> No token found!</p>';
                    return;
                }
                
                // Test API call
                const response = await fetch('/api/broadcast-links/fixture/1274453', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                output.innerHTML += `
                    <h3>API Test Results:</h3>
                    <p><strong>Status:</strong> ${response.status}</p>
                    <p><strong>Response:</strong> ${JSON.stringify(data, null, 2)}</p>
                `;
                
            } catch (error) {
                output.innerHTML += `<p><strong>Error:</strong> ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
