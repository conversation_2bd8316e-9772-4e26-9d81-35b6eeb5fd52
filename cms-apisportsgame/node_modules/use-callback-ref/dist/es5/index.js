"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.useRefToCallback = exports.refToCallback = exports.transformRef = exports.useTransformRef = exports.useMergeRefs = exports.mergeRefs = exports.createCallbackRef = exports.useCallbackRef = exports.assignRef = void 0;
var assignRef_1 = require("./assignRef");
Object.defineProperty(exports, "assignRef", { enumerable: true, get: function () { return assignRef_1.assignRef; } });
// callback ref
var useRef_1 = require("./useRef");
Object.defineProperty(exports, "useCallbackRef", { enumerable: true, get: function () { return useRef_1.useCallbackRef; } });
var createRef_1 = require("./createRef");
Object.defineProperty(exports, "createCallbackRef", { enumerable: true, get: function () { return createRef_1.createCallbackRef; } });
// merge ref
var mergeRef_1 = require("./mergeRef");
Object.defineProperty(exports, "mergeRefs", { enumerable: true, get: function () { return mergeRef_1.mergeRefs; } });
var useMergeRef_1 = require("./useMergeRef");
Object.defineProperty(exports, "useMergeRefs", { enumerable: true, get: function () { return useMergeRef_1.useMergeRefs; } });
// transform ref
var useTransformRef_1 = require("./useTransformRef");
Object.defineProperty(exports, "useTransformRef", { enumerable: true, get: function () { return useTransformRef_1.useTransformRef; } });
var transformRef_1 = require("./transformRef");
Object.defineProperty(exports, "transformRef", { enumerable: true, get: function () { return transformRef_1.transformRef; } });
// refToCallback
var refToCallback_1 = require("./refToCallback");
Object.defineProperty(exports, "refToCallback", { enumerable: true, get: function () { return refToCallback_1.refToCallback; } });
Object.defineProperty(exports, "useRefToCallback", { enumerable: true, get: function () { return refToCallback_1.useRefToCallback; } });
